package examples;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.converter.ConversionContext;
import com.talkweb.ai.indexer.core.impl.PdfToMarkdownConverter;
import com.talkweb.ai.indexer.core.performance.PerformanceOptimizer;
import com.talkweb.ai.indexer.core.performance.StreamingProcessor;
import com.talkweb.ai.indexer.service.PerformanceConfigurationService;
import com.talkweb.ai.indexer.core.util.ConversionCacheManager;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Advanced examples demonstrating performance optimization techniques
 * 
 * This class shows how to use the performance optimization features
 * of the new converter architecture for handling large files and
 * high-throughput scenarios.
 */
public class PerformanceOptimizationExample {
    
    private final PerformanceOptimizer performanceOptimizer;
    private final PerformanceConfigurationService perfConfigService;
    
    public PerformanceOptimizationExample() {
        this.performanceOptimizer = new PerformanceOptimizer();
        this.perfConfigService = new PerformanceConfigurationService();
        this.perfConfigService.initialize();
    }
    
    public static void main(String[] args) {
        PerformanceOptimizationExample example = new PerformanceOptimizationExample();
        
        try {
            example.demonstrateMemoryOptimization();
            example.demonstrateStreamingProcessing();
            example.demonstrateCacheOptimization();
            example.demonstrateConcurrentProcessing();
            example.demonstratePerformanceMonitoring();
            
        } catch (Exception e) {
            System.err.println("Performance example failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            example.cleanup();
        }
    }
    
    /**
     * Demonstrate memory-aware optimization
     */
    public void demonstrateMemoryOptimization() throws IOException {
        System.out.println("=== Memory Optimization Example ===");
        
        // Create a large sample file
        Path largeFile = createLargeSampleFile();
        
        // Get memory recommendations
        PerformanceConfigurationService.MemoryRecommendations recommendations = 
            perfConfigService.getMemoryRecommendations(largeFile.toFile().length());
        
        System.out.println("Memory Analysis:");
        System.out.println("  File size: " + formatBytes(largeFile.toFile().length()));
        System.out.println("  Available memory: " + formatBytes(recommendations.getAvailableMemory()));
        System.out.println("  Recommendation: " + recommendations.getRecommendation());
        System.out.println("  Use streaming: " + recommendations.isUseStreaming());
        
        if (recommendations.isUseStreaming()) {
            System.out.println("  Recommended chunk size: " + formatBytes(recommendations.getRecommendedChunkSize()));
        }
        
        // Get optimization settings
        PerformanceOptimizer.ConversionOptimizationSettings settings = 
            performanceOptimizer.optimizeForLargeFile(largeFile.toFile().length());
        
        System.out.println("\nOptimization Settings:");
        System.out.println("  Use streaming: " + settings.isUseStreamProcessing());
        System.out.println("  Chunk size: " + formatBytes(settings.getChunkSize()));
        System.out.println("  Enable caching: " + settings.isEnableCaching());
        System.out.println("  Max memory usage: " + formatBytes(settings.getMaxMemoryUsage()));
        
        // Clean up
        Files.deleteIfExists(largeFile);
    }
    
    /**
     * Demonstrate streaming processing for large files
     */
    public void demonstrateStreamingProcessing() throws IOException {
        System.out.println("\n=== Streaming Processing Example ===");
        
        // Create a large text file
        Path largeTextFile = createLargeTextFile();
        
        System.out.println("Processing large file: " + formatBytes(largeTextFile.toFile().length()));
        
        // Create optimized streaming processor
        StreamingProcessor processor = perfConfigService.createOptimizedStreamingProcessor(largeTextFile.toFile());
        
        System.out.println("Streaming processor configuration:");
        System.out.println("  Buffer size: " + formatBytes(processor.getBufferSize()));
        System.out.println("  Using direct buffers: " + processor.isUsingDirectBuffers());
        
        // Process file line by line
        Path outputFile = Paths.get("output/streamed_output.md");
        Files.createDirectories(outputFile.getParent());
        
        long startTime = System.currentTimeMillis();
        
        processor.processTextFileStreaming(
            largeTextFile.toFile(),
            line -> {
                // Simple processing: convert to markdown list item
                if (!line.trim().isEmpty()) {
                    return "- " + line.trim();
                }
                return null;
            },
            outputFile.toFile()
        );
        
        long processingTime = System.currentTimeMillis() - startTime;
        
        System.out.println("Streaming processing completed in " + processingTime + " ms");
        System.out.println("Output file size: " + formatBytes(outputFile.toFile().length()));
        
        // Clean up
        Files.deleteIfExists(largeTextFile);
        Files.deleteIfExists(outputFile);
    }
    
    /**
     * Demonstrate cache optimization
     */
    public void demonstrateCacheOptimization() throws ConversionException, IOException {
        System.out.println("\n=== Cache Optimization Example ===");
        
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter();
        ConversionContext context = ConversionContext.builder().build();
        
        // Create sample files
        List<File> sampleFiles = createSamplePdfFiles();
        
        System.out.println("Initial cache statistics:");
        printCacheStatistics();
        
        // First conversion (cache miss)
        long startTime = System.currentTimeMillis();
        for (File file : sampleFiles) {
            if (file.exists()) {
                try {
                    ConversionResult result = converter.convert(file, context);
                    System.out.println("Converted: " + file.getName() + " (success: " + result.isSuccess() + ")");
                } catch (ConversionException e) {
                    System.out.println("Failed: " + file.getName() + " - " + e.getMessage());
                }
            }
        }
        long firstRunTime = System.currentTimeMillis() - startTime;
        
        System.out.println("\nAfter first run cache statistics:");
        printCacheStatistics();
        
        // Second conversion (cache hit)
        startTime = System.currentTimeMillis();
        for (File file : sampleFiles) {
            if (file.exists()) {
                try {
                    ConversionResult result = converter.convert(file, context);
                    System.out.println("Converted (cached): " + file.getName() + " (success: " + result.isSuccess() + ")");
                } catch (ConversionException e) {
                    System.out.println("Failed: " + file.getName() + " - " + e.getMessage());
                }
            }
        }
        long secondRunTime = System.currentTimeMillis() - startTime;
        
        System.out.println("\nAfter second run cache statistics:");
        printCacheStatistics();
        
        System.out.println("\nPerformance comparison:");
        System.out.println("  First run (cache miss): " + firstRunTime + " ms");
        System.out.println("  Second run (cache hit): " + secondRunTime + " ms");
        System.out.println("  Speed improvement: " + String.format("%.1fx", (double) firstRunTime / secondRunTime));
        
        // Clean up
        for (File file : sampleFiles) {
            Files.deleteIfExists(file.toPath());
        }
    }
    
    /**
     * Demonstrate concurrent processing
     */
    public void demonstrateConcurrentProcessing() throws IOException {
        System.out.println("\n=== Concurrent Processing Example ===");
        
        // Create multiple sample files
        List<File> files = createMultipleSampleFiles();
        
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter();
        ConversionContext context = ConversionContext.builder().build();
        
        // Sequential processing
        long startTime = System.currentTimeMillis();
        int sequentialSuccess = 0;
        for (File file : files) {
            if (file.exists()) {
                try {
                    ConversionResult result = converter.convert(file, context);
                    if (result.isSuccess()) {
                        sequentialSuccess++;
                    }
                } catch (ConversionException e) {
                    // Handle error
                }
            }
        }
        long sequentialTime = System.currentTimeMillis() - startTime;
        
        // Concurrent processing
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
        
        startTime = System.currentTimeMillis();
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();
        
        for (File file : files) {
            if (file.exists()) {
                CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        ConversionResult result = converter.convert(file, context);
                        return result.isSuccess();
                    } catch (ConversionException e) {
                        return false;
                    }
                }, executor);
                futures.add(future);
            }
        }
        
        // Wait for all to complete
        int concurrentSuccess = 0;
        for (CompletableFuture<Boolean> future : futures) {
            try {
                if (future.get()) {
                    concurrentSuccess++;
                }
            } catch (Exception e) {
                // Handle error
            }
        }
        long concurrentTime = System.currentTimeMillis() - startTime;
        
        executor.shutdown();
        
        System.out.println("Processing comparison:");
        System.out.println("  Sequential: " + sequentialTime + " ms (" + sequentialSuccess + " successful)");
        System.out.println("  Concurrent: " + concurrentTime + " ms (" + concurrentSuccess + " successful)");
        System.out.println("  Speed improvement: " + String.format("%.1fx", (double) sequentialTime / concurrentTime));
        
        // Clean up
        for (File file : files) {
            Files.deleteIfExists(file.toPath());
        }
    }
    
    /**
     * Demonstrate performance monitoring
     */
    public void demonstratePerformanceMonitoring() {
        System.out.println("\n=== Performance Monitoring Example ===");
        
        PerformanceOptimizer.PerformanceMetrics metrics = performanceOptimizer.getMetrics();
        
        System.out.println("Current performance metrics:");
        System.out.println("  Current memory usage: " + String.format("%.1f%%", metrics.getCurrentMemoryUsage() * 100));
        System.out.println("  Max memory usage: " + String.format("%.1f%%", metrics.getMaxMemoryUsage() * 100));
        System.out.println("  Memory pressure events: " + metrics.getMemoryPressureEvents());
        System.out.println("  Critical memory events: " + metrics.getCriticalMemoryEvents());
        System.out.println("  Total optimizations: " + metrics.getTotalOptimizations());
        
        // Simulate memory pressure
        System.out.println("\nSimulating memory pressure...");
        List<byte[]> memoryConsumer = new ArrayList<>();
        try {
            for (int i = 0; i < 100; i++) {
                memoryConsumer.add(new byte[1024 * 1024]); // 1MB each
                Thread.sleep(10); // Give optimizer time to react
            }
        } catch (OutOfMemoryError | InterruptedException e) {
            // Expected
        }
        
        // Check metrics again
        metrics = performanceOptimizer.getMetrics();
        System.out.println("\nAfter memory pressure simulation:");
        System.out.println("  Memory pressure events: " + metrics.getMemoryPressureEvents());
        System.out.println("  Critical memory events: " + metrics.getCriticalMemoryEvents());
        System.out.println("  Total optimizations: " + metrics.getTotalOptimizations());
        
        // Clear memory
        memoryConsumer.clear();
        System.gc();
    }
    
    // Helper methods
    
    private Path createLargeSampleFile() throws IOException {
        Path file = Paths.get("temp/large_sample.txt");
        Files.createDirectories(file.getParent());
        
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            content.append("This is line ").append(i).append(" of the large sample file.\n");
        }
        
        Files.writeString(file, content.toString());
        return file;
    }
    
    private Path createLargeTextFile() throws IOException {
        Path file = Paths.get("temp/large_text.txt");
        Files.createDirectories(file.getParent());
        
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < 50000; i++) {
            content.append("Line ").append(i).append(": Lorem ipsum dolor sit amet, consectetur adipiscing elit.\n");
        }
        
        Files.writeString(file, content.toString());
        return file;
    }
    
    private List<File> createSamplePdfFiles() throws IOException {
        // Note: These would be actual PDF files in a real scenario
        // For this example, we'll create text files with .pdf extension
        List<File> files = new ArrayList<>();
        Path tempDir = Paths.get("temp");
        Files.createDirectories(tempDir);
        
        for (int i = 1; i <= 3; i++) {
            Path file = tempDir.resolve("sample" + i + ".pdf");
            Files.writeString(file, "Sample PDF content " + i);
            files.add(file.toFile());
        }
        
        return files;
    }
    
    private List<File> createMultipleSampleFiles() throws IOException {
        List<File> files = new ArrayList<>();
        Path tempDir = Paths.get("temp");
        Files.createDirectories(tempDir);
        
        for (int i = 1; i <= 10; i++) {
            Path file = tempDir.resolve("concurrent_sample" + i + ".pdf");
            Files.writeString(file, "Concurrent processing sample " + i);
            files.add(file.toFile());
        }
        
        return files;
    }
    
    private void printCacheStatistics() {
        var stats = ConversionCacheManager.getAllCacheStatistics();
        System.out.println("  Cache size: " + stats.getSize());
        System.out.println("  Cache hits: " + stats.getHitCount());
        System.out.println("  Cache misses: " + stats.getMissCount());
        System.out.println("  Hit rate: " + String.format("%.1f%%", stats.getHitRate() * 100));
    }
    
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
    
    private void cleanup() {
        performanceOptimizer.shutdown();
        perfConfigService.shutdown();
        
        // Clean up temp directory
        try {
            Path tempDir = Paths.get("temp");
            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                    .sorted((a, b) -> b.compareTo(a)) // Delete files before directories
                    .forEach(path -> {
                        try {
                            Files.deleteIfExists(path);
                        } catch (IOException e) {
                            // Ignore
                        }
                    });
            }
        } catch (IOException e) {
            // Ignore cleanup errors
        }
    }
}
