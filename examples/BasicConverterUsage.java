package examples;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.converter.ConversionContext;
import com.talkweb.ai.indexer.core.impl.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Basic examples of using the new converter architecture
 * 
 * This class demonstrates common usage patterns for document conversion
 * using the refactored converter architecture.
 */
public class BasicConverterUsage {
    
    public static void main(String[] args) {
        BasicConverterUsage examples = new BasicConverterUsage();
        
        try {
            examples.basicPdfConversion();
            examples.excelConversionWithOptions();
            examples.htmlConversionWithModes();
            examples.wordConversionWithFormatting();
            examples.batchConversion();
            
        } catch (Exception e) {
            System.err.println("Example execution failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Basic PDF to Markdown conversion
     */
    public void basicPdfConversion() throws ConversionException, IOException {
        System.out.println("=== Basic PDF Conversion ===");
        
        // Create converter
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter();
        
        // Create simple context
        ConversionContext context = ConversionContext.builder().build();
        
        // Convert file
        File pdfFile = new File("examples/sample.pdf");
        if (pdfFile.exists()) {
            ConversionResult result = converter.convert(pdfFile, context);
            
            if (result.isSuccess()) {
                System.out.println("Conversion successful!");
                System.out.println("Content length: " + result.getContent().length());
                
                // Save to file
                Path outputPath = Paths.get("output/sample.md");
                Files.createDirectories(outputPath.getParent());
                Files.writeString(outputPath, result.getContent());
                System.out.println("Saved to: " + outputPath);
            } else {
                System.err.println("Conversion failed: " + result.getMessage());
            }
        } else {
            System.out.println("Sample PDF file not found, skipping example");
        }
    }
    
    /**
     * Excel conversion with custom options
     */
    public void excelConversionWithOptions() throws ConversionException, IOException {
        System.out.println("\n=== Excel Conversion with Options ===");
        
        ExcelToMarkdownConverter converter = new ExcelToMarkdownConverter();
        
        // Create context with options
        ConversionContext context = ConversionContext.builder()
                .option("includeSheetNames", true)
                .option("convertFormulas", false)
                .option("preserveFormatting", true)
                .option("maxRowsPerSheet", 1000)
                .build();
        
        File excelFile = new File("examples/sample.xlsx");
        if (excelFile.exists()) {
            ConversionResult result = converter.convert(excelFile, context);
            
            if (result.isSuccess()) {
                System.out.println("Excel conversion successful!");
                System.out.println("First 200 characters:");
                System.out.println(result.getContent().substring(0, 
                    Math.min(200, result.getContent().length())));
            }
        } else {
            System.out.println("Sample Excel file not found, skipping example");
        }
    }
    
    /**
     * HTML conversion with different modes
     */
    public void htmlConversionWithModes() throws ConversionException, IOException {
        System.out.println("\n=== HTML Conversion with Modes ===");
        
        HtmlToMarkdownConverter converter = new HtmlToMarkdownConverter();
        
        // Create sample HTML file
        String htmlContent = """
            <html>
            <body>
                <h1>Sample Document</h1>
                <p>This is a <strong>sample</strong> HTML document with <em>formatting</em>.</p>
                <ul>
                    <li>Item 1</li>
                    <li>Item 2</li>
                </ul>
                <table>
                    <tr><th>Header 1</th><th>Header 2</th></tr>
                    <tr><td>Cell 1</td><td>Cell 2</td></tr>
                </table>
            </body>
            </html>
            """;
        
        Path htmlFile = Paths.get("temp/sample.html");
        Files.createDirectories(htmlFile.getParent());
        Files.writeString(htmlFile, htmlContent);
        
        // Convert with LOOSE mode
        ConversionContext looseContext = ConversionContext.builder()
                .option("conversionMode", "LOOSE")
                .build();
        
        ConversionResult looseResult = converter.convert(htmlFile.toFile(), looseContext);
        System.out.println("LOOSE mode result:");
        System.out.println(looseResult.getContent());
        
        // Convert with STRICT mode
        ConversionContext strictContext = ConversionContext.builder()
                .option("conversionMode", "STRICT")
                .build();
        
        ConversionResult strictResult = converter.convert(htmlFile.toFile(), strictContext);
        System.out.println("\nSTRICT mode result:");
        System.out.println(strictResult.getContent());
        
        // Clean up
        Files.deleteIfExists(htmlFile);
    }
    
    /**
     * Word conversion with formatting preservation
     */
    public void wordConversionWithFormatting() throws ConversionException {
        System.out.println("\n=== Word Conversion with Formatting ===");
        
        WordToMarkdownConverter converter = new WordToMarkdownConverter();
        
        ConversionContext context = ConversionContext.builder()
                .option("preserveFormatting", true)
                .option("extractImages", false)
                .option("convertTables", true)
                .option("includeMetadata", true)
                .build();
        
        File wordFile = new File("examples/sample.docx");
        if (wordFile.exists()) {
            try {
                ConversionResult result = converter.convert(wordFile, context);
                
                if (result.isSuccess()) {
                    System.out.println("Word conversion successful!");
                    System.out.println("Content preview:");
                    String content = result.getContent();
                    System.out.println(content.substring(0, Math.min(300, content.length())));
                    
                    if (content.length() > 300) {
                        System.out.println("... (truncated)");
                    }
                }
            } catch (ConversionException e) {
                System.err.println("Word conversion failed: " + e.getMessage());
            }
        } else {
            System.out.println("Sample Word file not found, skipping example");
        }
    }
    
    /**
     * Batch conversion of multiple files
     */
    public void batchConversion() throws IOException {
        System.out.println("\n=== Batch Conversion ===");
        
        // Create sample files for batch processing
        createSampleFiles();
        
        File[] files = {
            new File("temp/sample1.html"),
            new File("temp/sample2.html"),
            new File("temp/sample3.html")
        };
        
        HtmlToMarkdownConverter converter = new HtmlToMarkdownConverter();
        ConversionContext context = ConversionContext.builder()
                .option("conversionMode", "LOOSE")
                .build();
        
        int successCount = 0;
        int failureCount = 0;
        
        for (File file : files) {
            if (file.exists()) {
                try {
                    ConversionResult result = converter.convert(file, context);
                    
                    if (result.isSuccess()) {
                        successCount++;
                        System.out.println("✓ Converted: " + file.getName());
                        
                        // Save result
                        String outputName = file.getName().replace(".html", ".md");
                        Path outputPath = Paths.get("output", outputName);
                        Files.createDirectories(outputPath.getParent());
                        Files.writeString(outputPath, result.getContent());
                        
                    } else {
                        failureCount++;
                        System.err.println("✗ Failed: " + file.getName() + " - " + result.getMessage());
                    }
                    
                } catch (ConversionException e) {
                    failureCount++;
                    System.err.println("✗ Error: " + file.getName() + " - " + e.getMessage());
                }
            }
        }
        
        System.out.println("\nBatch conversion completed:");
        System.out.println("  Successful: " + successCount);
        System.out.println("  Failed: " + failureCount);
        
        // Clean up
        for (File file : files) {
            Files.deleteIfExists(file.toPath());
        }
    }
    
    /**
     * Create sample HTML files for batch processing
     */
    private void createSampleFiles() throws IOException {
        Path tempDir = Paths.get("temp");
        Files.createDirectories(tempDir);
        
        String[] samples = {
            "<html><body><h1>Document 1</h1><p>Content of document 1</p></body></html>",
            "<html><body><h1>Document 2</h1><p>Content of document 2 with <strong>bold</strong> text</p></body></html>",
            "<html><body><h1>Document 3</h1><ul><li>Item A</li><li>Item B</li></ul></body></html>"
        };
        
        for (int i = 0; i < samples.length; i++) {
            Path file = tempDir.resolve("sample" + (i + 1) + ".html");
            Files.writeString(file, samples[i]);
        }
    }
    
    /**
     * Demonstrate error handling
     */
    public void errorHandlingExample() {
        System.out.println("\n=== Error Handling Example ===");
        
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter();
        ConversionContext context = ConversionContext.builder().build();
        
        // Try to convert non-existent file
        File nonExistentFile = new File("does-not-exist.pdf");
        
        try {
            ConversionResult result = converter.convert(nonExistentFile, context);
            System.out.println("Unexpected success");
        } catch (ConversionException e) {
            System.out.println("Expected error caught: " + e.getMessage());
            
            // Check if error is recoverable
            if (e.getCause() instanceof IOException) {
                System.out.println("This is an I/O error - file might be missing or inaccessible");
            }
        }
        
        // Try to convert file with wrong extension
        try {
            File txtFile = new File("sample.txt");
            ConversionResult result = converter.convert(txtFile, context);
            System.out.println("Unexpected success");
        } catch (ConversionException e) {
            System.out.println("Expected format error: " + e.getMessage());
        }
    }
}
