# 智能文档转换系统 (Intelligent Document Converter)

一个基于 Spring Boot 的高性能文档转换系统，专为 RAG (Retrieval-Augmented Generation) 系统设计。支持多种文档格式转换为 Markdown，并提供 AI 增强的文档处理能力。

## 🚀 核心特性

- **多格式支持**: 支持 PDF、Word、Excel、PowerPoint、HTML、TXT 等主流文档格式
- **AI 增强处理**: 集成 Spring AI，提供文档摘要、向量嵌入和智能分析
- **插件化架构**: 基于 Java SPI 的可扩展插件系统，支持热加载
- **高性能并发**: 基于 Java 21 虚拟线程的异步处理
- **智能框架识别**: 自动识别 Bootstrap、Tailwind CSS、jQuery、Ant Design 等前端框架
- **结构保留**: 完整保留文档层次结构和元数据信息
- **批量处理**: 支持目录递归扫描和并行处理

## 📋 系统要求

- **Java**: 21 或更高版本
- **Maven**: 3.9+
- **内存**: 建议 4GB+ (处理大型文档时)
- **存储**: 根据文档数量和大小调整

## 🛠️ 构建与安装

### 快速构建

```bash
# 克隆项目
git clone <repository-url>
cd converter

# 编译打包
mvn clean package -DskipTests

# 或包含测试
mvn clean package
```

构建完成后，可执行 JAR 文件位于 `target/` 目录。

### 开发环境构建

```bash
# 安装依赖
mvn dependency:resolve

# 编译
mvn compile

# 运行测试
mvn test
```

## 🎯 使用指南

### 基本命令结构

```bash
java -jar target/converter-*.jar [全局选项] <命令> [命令选项]
```

### 文档转换命令

#### 单文件转换

```bash
# 转换单个 PDF 文件
java -jar target/converter-*.jar convert -i document.pdf -o output/

# 转换 Word 文档并启用 AI 增强
java -jar target/converter-*.jar convert -i report.docx -o output/ --enable-ai

# 转换 Excel 文件，保留所有工作表
java -jar target/converter-*.jar convert -i data.xlsx -o output/ --preserve-structure
```

#### 批量转换

```bash
# 递归转换目录下所有支持的文档
java -jar target/converter-*.jar convert -i documents/ -o output/ --recursive

# 并行处理多个文件
java -jar target/converter-*.jar convert -i documents/ -o output/ --parallel --threads 8

# 按文件类型过滤
java -jar target/converter-*.jar convert -i documents/ -o output/ --include "*.pdf,*.docx"
```

#### AI 增强处理

```bash
# 启用文档摘要和向量嵌入
java -jar target/converter-*.jar convert -i documents/ -o output/ \
  --enable-ai --ai-model gpt-3.5-turbo --generate-summary --create-embeddings

# 自定义 AI 配置
java -jar target/converter-*.jar convert -i documents/ -o output/ \
  --enable-ai --ai-temperature 0.7 --max-tokens 2048
```

### 插件管理命令

```bash
# 列出所有可用插件
java -jar target/converter-*.jar plugin list

# 查看插件详细信息
java -jar target/converter-*.jar plugin info <plugin-name>

# 启用/禁用插件
java -jar target/converter-*.jar plugin enable <plugin-name>
java -jar target/converter-*.jar plugin disable <plugin-name>
```

### 全局选项

- `--verbose, -v`: 详细输出模式
- `--quiet, -q`: 静默模式
- `--no-color`: 禁用彩色输出
- `--log-level`: 设置日志级别 (TRACE, DEBUG, INFO, WARN, ERROR)
- `--config`: 指定配置文件路径

## 📁 项目结构

```
src/main/java/com/talkweb/ai/converter/
├── cli/                    # 命令行接口与参数解析
├── core/                   # 核心处理引擎与插件框架
├── pipeline/               # 处理流水线
├── plugin/                 # 插件机制
├── config/                 # 配置管理
├── processor/              # 各类文档处理器
├── service/                # 业务服务
├── model/                  # 数据模型
├── util/                   # 工具类
└── DocConverterApplication.java  # Spring Boot 启动类
```

## 🔧 支持的文档格式

| 格式                 | 扩展名                               | 特性支持                               |
| -------------------- | ------------------------------------ | -------------------------------------- |
| **PDF**        | .pdf                                 | ✅ 页面分割、加密文档、结构保留        |
| **Word**       | .docx, .doc                          | ✅ 多版本兼容、样式保留                |
| **Excel**      | .xlsx, .xls, .xlsm                   | ✅ 合并单元格、公式处理、多工作表      |
| **PowerPoint** | .pptx, .ppt, .pptm                   | ✅ 幻灯片内容、演讲者备注              |
| **HTML**       | .html, .htm                          | ✅ 框架识别、表格处理、样式保留        |
| **图像**       | .png, .jpg, .jpeg, .tiff, .bmp, .gif | ✅ OCR文字识别、多语言支持、图像预处理 |
| **文本**       | .txt, .md                            | ✅ 编码检测、格式化                    |

## 🖼️ OCR 图像识别

### 支持的图像格式

- **PNG**: 无损压缩，适合文档截图
- **JPEG/JPG**: 有损压缩，适合照片
- **TIFF**: 高质量图像，适合扫描文档
- **BMP**: 位图格式，无压缩
- **GIF**: 支持动画，适合简单图像

### OCR 功能特性

- **多语言支持**: 中文、英文等多种语言识别
- **图像预处理**: 去噪、二值化、倾斜校正、分辨率优化
- **置信度评估**: 提供识别结果的可信度评分
- **文本结构化**: 自动识别段落、标题、列表等结构
- **批量处理**: 支持大量图像文件的并行处理
- **异步处理**: 非阻塞式OCR识别，提高处理效率

### OCR 配置选项

```yaml
app:
  ocr:
    enabled: true
    languages: [eng, chi_sim]  # 英文和简体中文
    confidence-threshold: 60   # 置信度阈值
    preprocessing-enabled: true
    timeout-seconds: 30
```

## 🤖 AI 增强功能

### 文档摘要服务

- 自动生成文档摘要和关键点提取
- 支持多种语言和文档类型
- 可配置摘要长度和详细程度

### 向量嵌入服务

- 生成 384 维语义向量表示
- 支持文档块化和语义搜索
- 兼容主流向量数据库

### 智能文档分析

- 自动识别文档结构和内容类型
- 提取关键实体和关系
- 支持知识图谱构建

## ⚙️ 配置说明

### 应用配置文件

创建 `application.yml` 或 `application.properties`:

```yaml
# AI 配置
spring:
  ai:
    enabled: true
    model: gpt-3.5-turbo
    temperature: 0.7
    max-tokens: 2048

# 处理配置
converter:
  processing:
    parallel: true
    max-threads: 8
    batch-size: 100

  # 输出配置
  output:
    preserve-structure: true
    generate-metadata: true
    create-index: true
```

### 插件配置

```yaml
plugins:
  pdf:
    split-pages: true
    extract-images: false
    password-protected: true

  excel:
    process-formulas: true
    merge-cells: true
    include-charts: false

  html:
    framework-detection: true
    preserve-css: false
    extract-tables: true

  image:
    ocr-enabled: true
    preprocessing-enabled: true
    confidence-threshold: 60
    include-metadata: true
    structure-level: "BASIC"  # NONE, BASIC, ADVANCED
    timeout-seconds: 30
```

## 🔌 插件开发

### 创建自定义文档处理器

```java
@Component
public class CustomDocumentProcessor implements DocumentProcessor, Plugin {

    @Override
    public ConversionResult process(File input) throws ConversionException {
        // 实现文档处理逻辑
        return new ConversionResult(/* ... */);
    }

    @Override
    public boolean supportsExtension(String extension) {
        return "custom".equalsIgnoreCase(extension);
    }

    // 实现 Plugin 接口的其他方法...
}
```

### 插件注册

在 `META-INF/services/com.talkweb.ai.converter.core.DocumentProcessor` 文件中注册:

```
com.example.CustomDocumentProcessor
```

## 📊 性能优化

### 内存优化

- 使用流式处理大型文档
- 配置合适的 JVM 堆内存: `-Xmx4g -Xms2g`
- 启用 G1 垃圾收集器: `-XX:+UseG1GC`

### 并发优化

- 根据 CPU 核心数调整线程池大小
- 使用虚拟线程处理 I/O 密集型任务
- 合理配置批处理大小

### 示例 JVM 参数

```bash
java -Xmx4g -Xms2g -XX:+UseG1GC -XX:+UseStringDeduplication \
     --enable-preview \
     -jar target/converter-*.jar convert -i documents/ -o output/
```

## 🧪 测试

### 运行测试套件

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=PdfConverterTest

# 运行集成测试
mvn verify

# 生成测试报告
mvn surefire-report:report
```

### 测试覆盖率

当前测试统计:

- **测试总数**: 271 个
- **通过率**: 99.6% (270/271)
- **代码覆盖率**: 85%+

## 🚨 故障排除

### 常见问题

**1. 内存不足错误**

```bash
# 增加堆内存
java -Xmx8g -jar target/converter-*.jar ...
```

**2. 文档格式不支持**

```bash
# 检查支持的格式
java -jar target/converter-*.jar plugin list
```

**3. AI 服务连接失败**

```bash
# 检查网络连接和 API 密钥
java -jar target/converter-*.jar convert ... --disable-ai
```

**4. 插件加载失败**

```bash
# 查看详细日志
java -jar target/converter-*.jar --verbose --log-level DEBUG ...
```

### 日志配置

在 `logback-spring.xml` 中配置日志级别:

```xml
<configuration>
    <logger name="com.talkweb.ai.converter" level="INFO"/>
    <logger name="com.talkweb.ai.converter.core" level="DEBUG"/>
</configuration>
```

## 🤝 贡献指南

1. Fork 项目仓库
2. 创建特性分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建 Pull Request

### 开发规范

- 遵循 Java 代码规范
- 编写单元测试和集成测试
- 更新相关文档
- 确保所有测试通过

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 📞 支持与联系

- **文档**: [docs/](docs/) 目录包含详细的技术文档
- **问题反馈**: 请使用 GitHub Issues
- **技术支持**: 查看 [故障排除](#-故障排除) 部分

## 🎯 路线图

- [ ] 支持更多文档格式 (RTF, ODT, etc.)
- [ ] 集成更多 AI 模型和服务
- [ ] 添加 Web 界面
- [ ] 支持分布式处理
- [ ] 增强向量数据库集成

---

**项目状态**: 高度完成 (85% 整体进度) | **风险等级**: 中等 | **最后更新**: 2025-06-22
