package com.talkweb.ai.converter.recovery;

import com.talkweb.ai.converter.logging.OcrLogger;
import com.talkweb.ai.converter.metrics.OcrMetrics;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OCR错误处理器测试类
 */
@ExtendWith(MockitoExtension.class)
class OcrErrorHandlerTest {

    @Mock
    private OcrLogger ocrLogger;
    
    @Mock
    private OcrMetrics ocrMetrics;

    private OcrErrorHandler errorHandler;

    @BeforeEach
    void setUp() {
        errorHandler = new OcrErrorHandler();
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field loggerField = OcrErrorHandler.class.getDeclaredField("ocrLogger");
            loggerField.setAccessible(true);
            loggerField.set(errorHandler, ocrLogger);

            java.lang.reflect.Field metricsField = OcrErrorHandler.class.getDeclaredField("ocrMetrics");
            metricsField.setAccessible(true);
            metricsField.set(errorHandler, ocrMetrics);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set up test", e);
        }

        // 重置错误统计
        errorHandler.resetErrorStatistics();
    }

    @Test
    void testHandleError_TesseractError() {
        // Given
        BufferedImage image = createTestImage();
        Exception tesseractError = new RuntimeException("Tesseract OCR failed");
        String requestId = "test_request_1";

        // When
        OcrErrorResult result = errorHandler.handleError(requestId, tesseractError, image, 1);

        // Then
        assertNotNull(result);
        assertTrue(result.isRetry());
        assertTrue(result.shouldContinue());
        assertTrue(result.getRetryDelayMs() > 0);
        
        verify(ocrLogger).logError(eq(requestId), eq("tesseract_error"), 
                                  eq(tesseractError.getMessage()), eq(tesseractError), any(Map.class));
    }

    @Test
    void testHandleError_TimeoutError() {
        // Given
        BufferedImage image = createTestImage();
        Exception timeoutError = new RuntimeException("Processing timeout occurred");
        String requestId = "test_request_2";

        // When
        OcrErrorResult result = errorHandler.handleError(requestId, timeoutError, image, 1);

        // Then
        assertNotNull(result);
        assertTrue(result.isRetry());
        assertTrue(result.shouldContinue());
    }

    @Test
    void testHandleError_MemoryError() {
        // Given
        BufferedImage image = createTestImage();
        Throwable memoryError = new OutOfMemoryError("Java heap space");
        String requestId = "test_request_3";

        // When
        OcrErrorResult result = errorHandler.handleError(requestId, memoryError, image, 1);

        // Then
        assertNotNull(result);
        assertTrue(result.isFail());
        assertFalse(result.shouldContinue());
    }

    @Test
    void testHandleError_ImageFormatError() {
        // Given
        BufferedImage image = createTestImage();
        Exception formatError = new RuntimeException("Unsupported image format");
        String requestId = "test_request_4";

        // When
        OcrErrorResult result = errorHandler.handleError(requestId, formatError, image, 1);

        // Then
        assertNotNull(result);
        assertTrue(result.isFallback());
        assertTrue(result.shouldContinue());
        assertNotNull(result.getFallbackResult());
    }

    @Test
    void testHandleError_MaxRetriesExceeded() {
        // Given
        BufferedImage image = createTestImage();
        Exception tesseractError = new RuntimeException("Tesseract OCR failed");
        String requestId = "test_request_5";

        // When
        OcrErrorResult result = errorHandler.handleError(requestId, tesseractError, image, 4); // 超过最大重试次数

        // Then
        assertNotNull(result);
        assertTrue(result.isFail());
        assertFalse(result.shouldContinue());
    }

    @Test
    void testHandleError_FileError() throws IOException {
        // Given
        File tempFile = File.createTempFile("test", ".png");
        tempFile.deleteOnExit();
        Exception ioError = new RuntimeException("File IO error");
        String requestId = "test_request_6";

        // When
        OcrErrorResult result = errorHandler.handleError(requestId, ioError, tempFile, 1);

        // Then
        assertNotNull(result);
        assertTrue(result.isRetry());
        
        verify(ocrLogger).logError(eq(requestId), eq("io_error"), 
                                  eq(ioError.getMessage()), eq(ioError), any(Map.class));
    }

    @Test
    void testShouldRetry_RetryableError() {
        // Given
        OcrErrorHandler.ErrorType retryableError = OcrErrorHandler.ErrorType.TESSERACT_ERROR;

        // When & Then
        assertTrue(errorHandler.shouldRetry(retryableError, 1));
        assertTrue(errorHandler.shouldRetry(retryableError, 2));
        assertFalse(errorHandler.shouldRetry(retryableError, 3)); // 第3次已经达到最大重试次数
        assertFalse(errorHandler.shouldRetry(retryableError, 4)); // 超过最大重试次数
    }

    @Test
    void testShouldRetry_NonRetryableError() {
        // Given
        OcrErrorHandler.ErrorType nonRetryableError = OcrErrorHandler.ErrorType.MEMORY_ERROR;

        // When & Then
        assertFalse(errorHandler.shouldRetry(nonRetryableError, 1));
        assertFalse(errorHandler.shouldRetry(nonRetryableError, 2));
    }

    @Test
    void testGetRetryDelay_ExponentialBackoff() {
        // When & Then
        assertEquals(1000, errorHandler.getRetryDelay(1));
        assertEquals(2000, errorHandler.getRetryDelay(2));
        assertEquals(4000, errorHandler.getRetryDelay(3));
        assertEquals(8000, errorHandler.getRetryDelay(4));
    }

    @Test
    void testErrorStatistics() {
        // Given
        BufferedImage image = createTestImage();
        Exception error1 = new RuntimeException("Tesseract error");
        Exception error2 = new RuntimeException("Timeout error");
        Exception error3 = new RuntimeException("Another tesseract error");

        // When
        errorHandler.handleError("req1", error1, image, 1);
        errorHandler.handleError("req2", error2, image, 1);
        errorHandler.handleError("req3", error3, image, 1);

        // Then
        Map<String, Integer> stats = errorHandler.getErrorStatistics();
        assertEquals(2, stats.get("tesseract_error").intValue());
        assertEquals(1, stats.get("timeout_error").intValue());
    }

    @Test
    void testResetErrorStatistics() {
        // Given
        BufferedImage image = createTestImage();
        Exception error = new RuntimeException("Tesseract error");
        errorHandler.handleError("req1", error, image, 1);

        // When
        errorHandler.resetErrorStatistics();

        // Then
        Map<String, Integer> stats = errorHandler.getErrorStatistics();
        assertTrue(stats.isEmpty());
    }

    @Test
    void testGetRecoveryAdvice() {
        // When & Then
        String tesseractAdvice = errorHandler.getRecoveryAdvice(OcrErrorHandler.ErrorType.TESSERACT_ERROR);
        assertNotNull(tesseractAdvice);
        assertTrue(tesseractAdvice.contains("Tesseract"));

        String memoryAdvice = errorHandler.getRecoveryAdvice(OcrErrorHandler.ErrorType.MEMORY_ERROR);
        assertNotNull(memoryAdvice);
        assertTrue(memoryAdvice.contains("内存"));

        String timeoutAdvice = errorHandler.getRecoveryAdvice(OcrErrorHandler.ErrorType.TIMEOUT_ERROR);
        assertNotNull(timeoutAdvice);
        assertTrue(timeoutAdvice.contains("超时"));
    }

    @Test
    void testErrorTypeClassification() {
        // Given
        BufferedImage image = createTestImage();

        // Test different error types
        Exception tesseractError = new RuntimeException("Tesseract initialization failed");
        Exception timeoutError = new RuntimeException("Operation timed out");
        Throwable memoryError = new OutOfMemoryError("Java heap space");
        Exception ioError = new RuntimeException("File not found");
        Exception imageError = new RuntimeException("Invalid image format");
        Exception configError = new RuntimeException("Configuration parameter invalid");
        Exception threadError = new RuntimeException("Thread pool exhausted");
        Exception cacheError = new RuntimeException("Cache operation failed");
        Exception unknownError = new RuntimeException("Something went wrong");

        // When & Then
        OcrErrorResult result1 = errorHandler.handleError("req1", tesseractError, image, 1);
        assertTrue(result1.isRetry());

        OcrErrorResult result2 = errorHandler.handleError("req2", timeoutError, image, 1);
        assertTrue(result2.isRetry());

        OcrErrorResult result3 = errorHandler.handleError("req3", memoryError, image, 1);
        assertTrue(result3.isFail());

        OcrErrorResult result4 = errorHandler.handleError("req4", ioError, image, 1);
        assertTrue(result4.isRetry());

        OcrErrorResult result5 = errorHandler.handleError("req5", imageError, image, 1);
        assertTrue(result5.isFallback());

        OcrErrorResult result6 = errorHandler.handleError("req6", configError, image, 1);
        assertTrue(result6.isFail());

        OcrErrorResult result7 = errorHandler.handleError("req7", threadError, image, 1);
        assertTrue(result7.isRetry());

        OcrErrorResult result8 = errorHandler.handleError("req8", cacheError, image, 1);
        assertTrue(result8.isRetry());

        OcrErrorResult result9 = errorHandler.handleError("req9", unknownError, image, 1);
        assertTrue(result9.isRetry());
    }

    @Test
    void testErrorTypeProperties() {
        // Test error type properties
        OcrErrorHandler.ErrorType tesseractError = OcrErrorHandler.ErrorType.TESSERACT_ERROR;
        assertEquals("tesseract_error", tesseractError.getCode());
        assertTrue(tesseractError.isRetryable());
        assertNotNull(tesseractError.getDescription());

        OcrErrorHandler.ErrorType memoryError = OcrErrorHandler.ErrorType.MEMORY_ERROR;
        assertEquals("memory_error", memoryError.getCode());
        assertFalse(memoryError.isRetryable());
        assertNotNull(memoryError.getDescription());
    }

    @Test
    void testRecoveryStrategyProperties() {
        // Test recovery strategy properties
        OcrErrorHandler.RecoveryStrategy retry = OcrErrorHandler.RecoveryStrategy.RETRY;
        assertEquals("重试处理", retry.getDescription());

        OcrErrorHandler.RecoveryStrategy fallback = OcrErrorHandler.RecoveryStrategy.FALLBACK;
        assertEquals("降级处理", fallback.getDescription());

        OcrErrorHandler.RecoveryStrategy skip = OcrErrorHandler.RecoveryStrategy.SKIP;
        assertEquals("跳过处理", skip.getDescription());

        OcrErrorHandler.RecoveryStrategy fail = OcrErrorHandler.RecoveryStrategy.FAIL;
        assertEquals("失败终止", fail.getDescription());
    }

    // Helper method
    private BufferedImage createTestImage() {
        BufferedImage image = new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 100, 100);
        g2d.setColor(Color.BLACK);
        g2d.drawString("Test", 10, 50);
        g2d.dispose();
        return image;
    }
}
