package com.talkweb.ai.converter.web.service;

import com.talkweb.ai.converter.core.DocumentProcessor;
import com.talkweb.ai.converter.core.PluginManager;
import com.talkweb.ai.converter.core.ProcessingContext;
import com.talkweb.ai.converter.core.ProcessingResult;
import com.talkweb.ai.converter.web.exception.ConversionException;
import com.talkweb.ai.converter.web.model.ConversionTask;
import com.talkweb.ai.converter.web.model.TaskStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AsyncConversionServiceTest {

    @Mock
    private ConversionTaskService taskService;
    
    @Mock
    private FileStorageService fileStorageService;
    
    @Mock
    private TaskProgressService progressService;
    
    @Mock
    private PluginManager pluginManager;
    
    @Mock
    private DocumentProcessor documentProcessor;
    
    @TempDir
    Path tempDir;
    
    private AsyncConversionService asyncConversionService;
    
    @BeforeEach
    void setUp() {
        asyncConversionService = new AsyncConversionService(
            taskService, fileStorageService, progressService, pluginManager);
    }
    
    @Test
    void testCreateDocumentProcessorChain() throws Exception {
        // Arrange
        List<DocumentProcessor> processors = Arrays.asList(documentProcessor);
        when(pluginManager.getPlugins()).thenReturn(Arrays.asList(documentProcessor));
        
        // Use reflection to test the private method
        var method = AsyncConversionService.class.getDeclaredMethod("createDocumentProcessorChain");
        method.setAccessible(true);
        
        // Act
        DocumentProcessor result = (DocumentProcessor) method.invoke(asyncConversionService);
        
        // Assert
        assertNotNull(result);
        verify(pluginManager).getPlugins();
    }
    
    @Test
    void testCreateDocumentProcessorChainWithNoProcessors() throws Exception {
        // Arrange
        when(pluginManager.getPlugins()).thenReturn(Arrays.asList());
        
        // Use reflection to test the private method
        var method = AsyncConversionService.class.getDeclaredMethod("createDocumentProcessorChain");
        method.setAccessible(true);
        
        // Act
        DocumentProcessor result = (DocumentProcessor) method.invoke(asyncConversionService);
        
        // Assert
        assertNull(result);
        verify(pluginManager).getPlugins();
    }
    
    @Test
    void testConvertFileWithProcessor() throws Exception {
        // Arrange
        Path testFile = tempDir.resolve("test.txt");
        Files.writeString(testFile, "Test content");
        
        Path outputFile = tempDir.resolve("test.md");
        Files.writeString(outputFile, "# Converted Content\nTest content");
        
        ProcessingResult processingResult = ProcessingResult.success(outputFile.toFile());
        
        when(documentProcessor.supports(anyString())).thenReturn(true);
        when(documentProcessor.process(any(File.class), any(ProcessingContext.class)))
            .thenReturn(processingResult);
        
        // Use reflection to test the private method
        var method = AsyncConversionService.class.getDeclaredMethod(
            "convertFileWithProcessor", DocumentProcessor.class, Path.class, String.class);
        method.setAccessible(true);
        
        // Act
        ProcessingResult result = (ProcessingResult) method.invoke(
            asyncConversionService, documentProcessor, testFile, "test-task-id");
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(outputFile.toFile(), result.getOutputFile());
        
        verify(documentProcessor).supports("txt");
        verify(documentProcessor).process(any(File.class), any(ProcessingContext.class));
        verify(progressService, atLeastOnce()).updateProgress(
            eq("test-task-id"), eq(TaskStatus.PROCESSING), anyInt(), anyString(), anyString());
    }
    
    @Test
    void testConvertFileWithUnsupportedType() throws Exception {
        // Arrange
        Path testFile = tempDir.resolve("test.unsupported");
        Files.writeString(testFile, "Test content");

        when(documentProcessor.supports(anyString())).thenReturn(false);

        // Use reflection to test the private method
        var method = AsyncConversionService.class.getDeclaredMethod(
            "convertFileWithProcessor", DocumentProcessor.class, Path.class, String.class);
        method.setAccessible(true);

        // Act & Assert - Expect InvocationTargetException when using reflection
        Exception exception = assertThrows(Exception.class, () -> {
            method.invoke(asyncConversionService, documentProcessor, testFile, "test-task-id");
        });

        // For reflection calls, we need to check the InvocationTargetException
        if (exception instanceof java.lang.reflect.InvocationTargetException) {
            Throwable cause = exception.getCause();
            assertNotNull(cause);
            assertTrue(cause instanceof ConversionException,
                "Expected ConversionException but got: " + cause.getClass().getSimpleName());
            assertTrue(cause.getMessage().contains("Document processing failed"));
        } else {
            // Direct ConversionException (shouldn't happen with reflection but good to handle)
            assertTrue(exception instanceof ConversionException);
        }

        verify(documentProcessor).supports("unsupported");
        verify(documentProcessor, never()).process(any(File.class), any(ProcessingContext.class));
    }
    
    @Test
    void testGenerateResultFileName() throws Exception {
        // Use reflection to test the private method
        var method = AsyncConversionService.class.getDeclaredMethod("generateResultFileName", String.class);
        method.setAccessible(true);
        
        // Test various file names
        assertEquals("test.md", method.invoke(asyncConversionService, "test.txt"));
        assertEquals("document.md", method.invoke(asyncConversionService, "document.docx"));
        assertEquals("presentation.md", method.invoke(asyncConversionService, "presentation.pptx"));
        assertEquals("noextension.md", method.invoke(asyncConversionService, "noextension"));
    }
    
    @Test
    void testCancelConversion() {
        // Test cancellation functionality
        String taskId = "test-task-id";
        
        // Test when no task is running
        boolean result = asyncConversionService.cancelConversion(taskId);
        assertFalse(result);
        
        // Test task running status
        assertFalse(asyncConversionService.isTaskRunning(taskId));
        
        // Test running task count
        assertEquals(0, asyncConversionService.getRunningTaskCount());
    }
}
