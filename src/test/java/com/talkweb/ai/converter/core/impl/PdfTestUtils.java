package com.talkweb.ai.converter.core.impl;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.apache.pdfbox.pdmodel.font.PDType1Font;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;

public class PdfTestUtils {

    public static File createSimplePdf(Path tempDir, String filename) throws IOException {
        File pdfFile = tempDir.resolve(filename).toFile();
        try (PDDocument doc = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            doc.addPage(page);
            
            try (PDPageContentStream contents = new PDPageContentStream(doc, page)) {
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 12);
                contents.newLineAtOffset(100, 700);
                contents.showText("Test PDF Document");
                contents.endText();
            }
            
            doc.save(pdfFile);
        }
        return pdfFile;
    }

    public static File createPdfWithTable(Path tempDir) throws IOException {
        File pdfFile = tempDir.resolve("table.pdf").toFile();
        try (PDDocument doc = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            doc.addPage(page);
            
            try (PDPageContentStream contents = new PDPageContentStream(doc, page)) {
                // 表头
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 12);
                contents.newLineAtOffset(100, 700);
                contents.showText("Header 1    Header 2");
                contents.endText();
                
                // 表格内容
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 12);
                contents.newLineAtOffset(100, 680);
                contents.showText("Cell 1      Cell 2");
                contents.endText();
            }
            
            doc.save(pdfFile);
        }
        return pdfFile;
    }

    public static File createPdfWithComplexHeadings(Path tempDir) throws IOException {
        File pdfFile = tempDir.resolve("headings.pdf").toFile();
        try (PDDocument doc = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            doc.addPage(page);
            
            try (PDPageContentStream contents = new PDPageContentStream(doc, page)) {
                // 三级标题
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 14);
                contents.newLineAtOffset(100, 700);
                contents.showText("Section 3.1");
                contents.endText();
                
                // 四级标题
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 12);
                contents.newLineAtOffset(100, 680);
                contents.showText("Subsection");
                contents.endText();
            }
            
            doc.save(pdfFile);
        }
        return pdfFile;
    }

    public static File createPdfWithLists(Path tempDir) throws IOException {
        File pdfFile = tempDir.resolve("lists.pdf").toFile();
        try (PDDocument doc = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            doc.addPage(page);
            
            try (PDPageContentStream contents = new PDPageContentStream(doc, page)) {
                // 编号列表
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 12);
                contents.newLineAtOffset(100, 700);
                contents.showText("1. First item");
                contents.newLineAtOffset(0, -15);
                contents.showText("2. Second item");
                contents.endText();
            }
            
            doc.save(pdfFile);
        }
        return pdfFile;
    }

    public static File createPdfWithHeadings(Path tempDir) throws IOException {
        File pdfFile = tempDir.resolve("headings.pdf").toFile();
        try (PDDocument doc = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            doc.addPage(page);
            
            try (PDPageContentStream contents = new PDPageContentStream(doc, page)) {
                // 主标题
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 16);
                contents.newLineAtOffset(100, 700);
                contents.showText("Main Heading");
                contents.endText();
                
                // 下划线
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 16);
                contents.newLineAtOffset(100, 695);
                contents.showText("============");
                contents.endText();
                
                // 子标题
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 14);
                contents.newLineAtOffset(100, 670);
                contents.showText("Subheading");
                contents.endText();
                
                // 下划线
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 14);
                contents.newLineAtOffset(100, 665);
                contents.showText("----------");
                contents.endText();
                
                // 内容段落
                contents.beginText();
                contents.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 12);
                contents.newLineAtOffset(100, 650);
                contents.showText("Content paragraph text");
                contents.endText();
            }
            
            doc.save(pdfFile);
        }
        return pdfFile;
    }
}
