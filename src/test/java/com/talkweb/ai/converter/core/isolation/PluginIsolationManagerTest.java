package com.talkweb.ai.converter.core.isolation;

import com.talkweb.ai.converter.core.security.DefaultPluginSandbox;
import com.talkweb.ai.converter.core.security.PluginSandbox;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PluginIsolationManager测试
 */
public class PluginIsolationManagerTest {

    private PluginIsolationManager isolationManager;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        isolationManager = new PluginIsolationManager();
    }

    @AfterEach
    void tearDown() {
        if (isolationManager != null) {
            isolationManager.cleanup();
        }
    }

    @Test
    void testCreateAndDestroyIsolationContext() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        Path pluginPath = createTestPluginFile();
        PluginSandbox sandbox = new DefaultPluginSandbox(pluginId);

        // Act
        PluginIsolationContext context = isolationManager.createIsolationContext(pluginId, pluginPath, sandbox);

        // Assert
        assertNotNull(context);
        assertEquals(pluginId, context.getPluginId());
        assertNotNull(context.getClassLoader());
        assertNotNull(context.getSandbox());
        assertTrue(isolationManager.isPluginIsolated(pluginId));

        // Act - destroy
        isolationManager.destroyIsolationContext(pluginId);

        // Assert
        assertFalse(isolationManager.isPluginIsolated(pluginId));
        assertTrue(context.isDestroyed());
    }

    @Test
    void testGetIsolationContext() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        Path pluginPath = createTestPluginFile();
        PluginSandbox sandbox = new DefaultPluginSandbox(pluginId);

        PluginIsolationContext originalContext = isolationManager.createIsolationContext(pluginId, pluginPath, sandbox);

        // Act
        PluginIsolationContext retrievedContext = isolationManager.getIsolationContext(pluginId);

        // Assert
        assertSame(originalContext, retrievedContext);
    }

    @Test
    void testGetIsolationContextNonExistent() {
        // Act
        PluginIsolationContext context = isolationManager.getIsolationContext("non-existent-plugin");

        // Assert
        assertNull(context);
    }

    @Test
    void testConfigurePluginCommunication() throws Exception {
        // Arrange
        String plugin1Id = "plugin1";
        String plugin2Id = "plugin2";
        Path pluginPath1 = createTestPluginFile("plugin1.jar");
        Path pluginPath2 = createTestPluginFile("plugin2.jar");
        PluginSandbox sandbox1 = new DefaultPluginSandbox(plugin1Id);
        PluginSandbox sandbox2 = new DefaultPluginSandbox(plugin2Id);

        isolationManager.createIsolationContext(plugin1Id, pluginPath1, sandbox1);
        isolationManager.createIsolationContext(plugin2Id, pluginPath2, sandbox2);

        // Act
        isolationManager.configurePluginCommunication(plugin1Id, plugin2Id, true);

        // Assert - 这里我们只能验证方法调用不抛出异常
        // 实际的通信测试需要更复杂的设置
        assertTrue(isolationManager.isPluginIsolated(plugin1Id));
        assertTrue(isolationManager.isPluginIsolated(plugin2Id));
    }

    @Test
    void testSetSharedPackages() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        Path pluginPath = createTestPluginFile();
        PluginSandbox sandbox = new DefaultPluginSandbox(pluginId);

        isolationManager.createIsolationContext(pluginId, pluginPath, sandbox);

        // Act
        isolationManager.setSharedPackages(pluginId, "com.example.shared", "org.apache.commons");

        // Assert - 验证方法调用不抛出异常
        assertTrue(isolationManager.isPluginIsolated(pluginId));
    }

    @Test
    void testSetIsolationEnabled() {
        // Act
        isolationManager.setIsolationEnabled(false);

        // Assert
        assertFalse(isolationManager.isIsolationEnabled());

        // Act
        isolationManager.setIsolationEnabled(true);

        // Assert
        assertTrue(isolationManager.isIsolationEnabled());
    }

    @Test
    void testGetStatistics() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        Path pluginPath = createTestPluginFile();
        PluginSandbox sandbox = new DefaultPluginSandbox(pluginId);

        // Act - before creating context
        PluginIsolationManager.IsolationStatistics statsBefore = isolationManager.getStatistics();

        // Assert
        assertEquals(0, statsBefore.getIsolatedPlugins());
        assertEquals(0, statsBefore.getClassLoaders());
        assertEquals(0, statsBefore.getSandboxes());
        assertTrue(statsBefore.isIsolationEnabled());

        // Act - create context
        isolationManager.createIsolationContext(pluginId, pluginPath, sandbox);
        PluginIsolationManager.IsolationStatistics statsAfter = isolationManager.getStatistics();

        // Assert
        assertEquals(1, statsAfter.getIsolatedPlugins());
        assertEquals(1, statsAfter.getClassLoaders());
        assertEquals(1, statsAfter.getSandboxes());
        assertTrue(statsAfter.isIsolationEnabled());
    }

    @Test
    void testCleanup() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        Path pluginPath = createTestPluginFile();
        PluginSandbox sandbox = new DefaultPluginSandbox(pluginId);

        PluginIsolationContext context = isolationManager.createIsolationContext(pluginId, pluginPath, sandbox);

        // Act
        isolationManager.cleanup();

        // Assert
        assertFalse(isolationManager.isPluginIsolated(pluginId));
        assertTrue(context.isDestroyed());

        PluginIsolationManager.IsolationStatistics stats = isolationManager.getStatistics();
        assertEquals(0, stats.getIsolatedPlugins());
        assertEquals(0, stats.getClassLoaders());
        assertEquals(0, stats.getSandboxes());
    }

    @Test
    void testCreateIsolationContextWithDisabledIsolation() throws Exception {
        // Arrange
        isolationManager.setIsolationEnabled(false);
        String pluginId = "test-plugin";
        Path pluginPath = createTestPluginFile();
        PluginSandbox sandbox = new DefaultPluginSandbox(pluginId);

        // Act
        PluginIsolationContext context = isolationManager.createIsolationContext(pluginId, pluginPath, sandbox);

        // Assert
        assertNull(context);
        assertFalse(isolationManager.isPluginIsolated(pluginId));
    }

    @Test
    void testCreateIsolationContextWithInvalidPath() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        Path invalidPath = tempDir.resolve("non-existent.jar");
        PluginSandbox sandbox = new DefaultPluginSandbox(pluginId);

        // Act & Assert
        assertThrows(PluginIsolationException.class, () -> {
            isolationManager.createIsolationContext(pluginId, invalidPath, sandbox);
        });
    }

    @Test
    void testMultiplePluginIsolation() throws Exception {
        // Arrange
        String plugin1Id = "plugin1";
        String plugin2Id = "plugin2";
        Path pluginPath1 = createTestPluginFile("plugin1.jar");
        Path pluginPath2 = createTestPluginFile("plugin2.jar");
        PluginSandbox sandbox1 = new DefaultPluginSandbox(plugin1Id);
        PluginSandbox sandbox2 = new DefaultPluginSandbox(plugin2Id);

        // Act
        PluginIsolationContext context1 = isolationManager.createIsolationContext(plugin1Id, pluginPath1, sandbox1);
        PluginIsolationContext context2 = isolationManager.createIsolationContext(plugin2Id, pluginPath2, sandbox2);

        // Assert
        assertNotNull(context1);
        assertNotNull(context2);
        assertNotSame(context1, context2);
        assertTrue(isolationManager.isPluginIsolated(plugin1Id));
        assertTrue(isolationManager.isPluginIsolated(plugin2Id));

        PluginIsolationManager.IsolationStatistics stats = isolationManager.getStatistics();
        assertEquals(2, stats.getIsolatedPlugins());
        assertEquals(2, stats.getClassLoaders());
        assertEquals(2, stats.getSandboxes());
    }

    /**
     * 创建测试用的插件文件
     */
    private Path createTestPluginFile() throws IOException {
        return createTestPluginFile("test-plugin.jar");
    }

    /**
     * 创建测试用的插件文件
     */
    private Path createTestPluginFile(String fileName) throws IOException {
        Path pluginFile = tempDir.resolve(fileName);
        Files.createFile(pluginFile);
        return pluginFile;
    }
}
