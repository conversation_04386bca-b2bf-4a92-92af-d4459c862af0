package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for HtmlToMarkdownConverter
 */
class HtmlToMarkdownConverterTest {

    private HtmlToMarkdownConverter converter;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        converter = new HtmlToMarkdownConverter();
    }

    @Test
    void testSupportsExtensions() {
        assertTrue(converter.getSupportedExtensions().contains("html"));
        assertTrue(converter.getSupportedExtensions().contains("htm"));
        assertFalse(converter.getSupportedExtensions().contains("txt"));
    }

    @Test
    void testSimpleConversion() throws IOException, ConversionException {
        Path htmlFile = tempDir.resolve("test.html");
        Files.writeString(htmlFile, "<h1>Hello</h1><p>World</p>");

        ConversionContext context = ConversionContext.builder().build();
        ConversionResult result = converter.convert(htmlFile.toFile(), context);

        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());
        assertNotNull(result.getContent());
        assertTrue(result.getContent().contains("Hello"));
        assertTrue(result.getContent().contains("World"));
    }

    @Test
    void testNonExistentFile() {
        File nonExistentFile = new File(tempDir.toFile(), "nonexistent.html");
        ConversionContext context = ConversionContext.builder().build();

        assertThrows(ConversionException.class, () -> {
            converter.convert(nonExistentFile, context);
        });
    }

    @Test
    void testNullFile() {
        ConversionContext context = ConversionContext.builder().build();

        assertThrows(ConversionException.class, () -> {
            converter.convert(null, context);
        });
    }
}
