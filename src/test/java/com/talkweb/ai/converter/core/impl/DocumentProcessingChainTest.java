package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.core.DocumentProcessor;
import com.talkweb.ai.converter.core.DocumentProcessingException;
import com.talkweb.ai.converter.core.ProcessingContext;
import com.talkweb.ai.converter.core.ProcessingResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class DocumentProcessingChainTest {

    @TempDir
    Path tempDir;

    @Mock
    private DocumentProcessor processor1;

    @Mock
    private DocumentProcessor processor2;

    private DocumentProcessingChain chain;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testEmptyChain() throws DocumentProcessingException {
        chain = new DocumentProcessingChain(Collections.emptyList());

        // 空链不应该支持任何扩展名
        assertFalse(chain.supports("txt"));
        assertFalse(chain.supports("pdf"));
        assertFalse(chain.supports("docx"));

        // 但处理应该失败
        File testFile = tempDir.resolve("test.txt").toFile();
        // 处理应该抛出异常
        assertThrows(UnsupportedOperationException.class, () -> {
            chain.process(testFile, new ProcessingContext.Builder().build());
        });
    }

    @Test
    void testSingleProcessor() throws DocumentProcessingException {
        // 设置处理器
        when(processor1.supports("txt")).thenReturn(true);
        when(processor1.supports("pdf")).thenReturn(false);

        File outputFile = tempDir.resolve("output.md").toFile();
        ProcessingResult successResult = ProcessingResult.success(outputFile);
        when(processor1.process(any(), any())).thenReturn(successResult);

        // 创建链
        chain = new DocumentProcessingChain(Collections.singletonList(processor1));

        // 测试支持的扩展名
        assertTrue(chain.supports("txt"));
        assertFalse(chain.supports("pdf"));

        // 测试处理
        File testFile = tempDir.resolve("test.txt").toFile();
        ProcessingResult result = chain.process(testFile, new ProcessingContext.Builder().build());

        assertTrue(result.isSuccess());
        assertEquals(outputFile, result.getOutputFile());

        // 验证调用
        verify(processor1).process(eq(testFile), any());
    }

    @Test
    void testMultipleProcessors() throws DocumentProcessingException {
        // 设置处理器1
        when(processor1.supports("txt")).thenReturn(true);
        when(processor1.supports("pdf")).thenReturn(false);

        // 设置处理器2
        when(processor2.supports("txt")).thenReturn(false);
        when(processor2.supports("pdf")).thenReturn(true);

        // 创建链
        chain = new DocumentProcessingChain(Arrays.asList(processor1, processor2));

        // 测试支持的扩展名
        assertTrue(chain.supports("txt"));
        assertTrue(chain.supports("pdf"));
        assertFalse(chain.supports("docx"));

        // 测试处理txt文件
        File txtFile = tempDir.resolve("test.txt").toFile();
        File txtOutput = tempDir.resolve("test_txt.md").toFile();
        when(processor1.process(eq(txtFile), any())).thenReturn(ProcessingResult.success(txtOutput));

        ProcessingResult txtResult = chain.process(txtFile, new ProcessingContext.Builder().build());
        assertTrue(txtResult.isSuccess());
        assertEquals(txtOutput, txtResult.getOutputFile());

        // 测试处理pdf文件
        File pdfFile = tempDir.resolve("test.pdf").toFile();
        File pdfOutput = tempDir.resolve("test_pdf.md").toFile();
        when(processor2.process(eq(pdfFile), any())).thenReturn(ProcessingResult.success(pdfOutput));

        ProcessingResult pdfResult = chain.process(pdfFile, new ProcessingContext.Builder().build());
        assertTrue(pdfResult.isSuccess());
        assertEquals(pdfOutput, pdfResult.getOutputFile());

        // 验证调用
        verify(processor1).process(eq(txtFile), any());
        verify(processor2).process(eq(pdfFile), any());
        verify(processor1, never()).process(eq(pdfFile), any());
        verify(processor2, never()).process(eq(txtFile), any());
    }

    @Test
    void testProcessorFailure() throws DocumentProcessingException {
        // 设置处理器
        when(processor1.supports("txt")).thenReturn(true);
        String errorMessage = "Processing failed";
        when(processor1.process(any(), any())).thenReturn(ProcessingResult.failure(errorMessage));

        // 创建链
        chain = new DocumentProcessingChain(Collections.singletonList(processor1));

        // 测试处理
        File testFile = tempDir.resolve("test.txt").toFile();
        ProcessingResult result = chain.process(testFile, new ProcessingContext.Builder().build());

        assertFalse(result.isSuccess());
        assertEquals(errorMessage, result.getErrorMessage());
    }
}
