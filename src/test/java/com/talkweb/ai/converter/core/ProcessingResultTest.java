package com.talkweb.ai.converter.core;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class ProcessingResultTest {

    @TempDir
    Path tempDir;
    
    @Test
    void testSuccessResult() {
        // 创建输出文件
        File outputFile = tempDir.resolve("output.md").toFile();
        
        // 创建成功结果
        ProcessingResult result = ProcessingResult.success(outputFile);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(outputFile, result.getOutputFile());
        assertNull(result.getErrorMessage());
        assertNull(result.getError());
    }
    
    @Test
    void testFailureResultWithMessage() {
        // 创建失败结果
        String errorMessage = "Processing failed";
        ProcessingResult result = ProcessingResult.failure(errorMessage);
        
        // 验证结果
        assertFalse(result.isSuccess());
        assertNull(result.getOutputFile());
        assertEquals(errorMessage, result.getErrorMessage());
        assertNull(result.getError());
    }
    
    @Test
    void testFailureResultWithException() {
        // 创建异常
        Exception exception = new RuntimeException("Test exception");
        
        // 创建失败结果
        ProcessingResult result = ProcessingResult.failure(exception);
        
        // 验证结果
        assertFalse(result.isSuccess());
        assertNull(result.getOutputFile());
        assertEquals("Test exception", result.getErrorMessage());
        assertEquals(exception, result.getError());
    }
    
    @Test
    void testBuilderWithMetadata() {
        // 创建输出文件
        File outputFile = tempDir.resolve("output.md").toFile();
        
        // 创建结果
        ProcessingResult result = new ProcessingResult.Builder()
                .success(true)
                .outputFile(outputFile)
                .metadata("key1", "value1")
                .metadata("key2", 123)
                .build();
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(outputFile, result.getOutputFile());
        assertEquals("value1", result.getMetadata("key1"));
        assertEquals(123, result.getMetadata("key2"));
        assertNull(result.getMetadata("nonExistent"));
        assertEquals("default", result.getMetadata("nonExistent", "default"));
    }
    
    @Test
    void testBuilderWithError() {
        // 创建异常
        Exception exception = new RuntimeException("Test exception");
        
        // 创建结果
        ProcessingResult result = new ProcessingResult.Builder()
                .success(false)
                .errorMessage("Custom error message")
                .error(exception)
                .build();
        
        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("Custom error message", result.getErrorMessage());
        assertEquals(exception, result.getError());
    }
}