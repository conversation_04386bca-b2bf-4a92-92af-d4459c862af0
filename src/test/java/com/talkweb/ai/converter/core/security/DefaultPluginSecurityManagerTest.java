package com.talkweb.ai.converter.core.security;

import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginMetadata;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.FilePermission;
import java.nio.file.Path;
import java.security.Permission;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * DefaultPluginSecurityManager测试
 */
public class DefaultPluginSecurityManagerTest {

    @Mock
    private Plugin mockPlugin;
    
    @Mock
    private PluginMetadata mockMetadata;

    private DefaultPluginSecurityManager securityManager;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        when(mockMetadata.getId()).thenReturn("test-plugin");
        when(mockPlugin.getMetadata()).thenReturn(mockMetadata);
        
        securityManager = new DefaultPluginSecurityManager();
    }

    @Test
    void testGrantAndCheckPermissions() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        Permission filePermission = new FilePermission("/tmp/*", "read");
        Set<Permission> permissions = Set.of(filePermission);

        // Act
        securityManager.grantPermissions(pluginId, permissions);

        // Assert
        Set<Permission> grantedPermissions = securityManager.getPluginPermissions(pluginId);
        assertTrue(grantedPermissions.contains(filePermission));
        assertTrue(securityManager.checkPermission(mockPlugin, filePermission));
    }

    @Test
    void testRevokePermissions() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        Permission filePermission = new FilePermission("/tmp/*", "read");
        Set<Permission> permissions = Set.of(filePermission);

        securityManager.grantPermissions(pluginId, permissions);

        // Act
        securityManager.revokePermissions(pluginId, permissions);

        // Assert
        Set<Permission> remainingPermissions = securityManager.getPluginPermissions(pluginId);
        assertFalse(remainingPermissions.contains(filePermission));
    }

    @Test
    void testCreateAndDestroySandbox() throws Exception {
        // Arrange
        String pluginId = "test-plugin";

        // Act
        PluginSandbox sandbox = securityManager.createSandbox(pluginId);

        // Assert
        assertNotNull(sandbox);
        assertEquals(pluginId, sandbox.getPluginId());
        assertTrue(securityManager.isInSandbox(pluginId));

        // Act - destroy sandbox
        securityManager.destroySandbox(pluginId);

        // Assert
        assertFalse(securityManager.isInSandbox(pluginId));
        assertTrue(sandbox.isDestroyed());
    }

    @Test
    void testCreateSandboxTwice() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        securityManager.createSandbox(pluginId);

        // Act & Assert
        assertThrows(PluginSecurityException.class, () -> {
            securityManager.createSandbox(pluginId);
        });
    }

    @Test
    void testSignatureVerificationDisabled() {
        // Arrange
        securityManager.setSignatureVerificationEnabled(false);
        Path pluginPath = tempDir.resolve("test-plugin.jar");

        // Act
        PluginSecurityManager.SignatureVerificationResult result = 
            securityManager.verifySignature(pluginPath);

        // Assert
        assertTrue(result.isValid());
        assertEquals("Verification disabled", result.getSignerInfo());
    }

    @Test
    void testAddAndRemoveSecurityPolicy() {
        // Arrange
        SecurityPolicy policy = SecurityPolicy.builder()
            .policyId("test-policy")
            .name("Test Policy")
            .description("Test security policy")
            .type(SecurityPolicy.PolicyType.RESTRICTIVE)
            .enabled(true)
            .build();

        // Act
        securityManager.addSecurityPolicy(policy);

        // Assert
        List<SecurityPolicy> policies = securityManager.getSecurityPolicies();
        assertTrue(policies.stream().anyMatch(p -> "test-policy".equals(p.getPolicyId())));

        // Act - remove policy
        securityManager.removeSecurityPolicy("test-policy");

        // Assert
        policies = securityManager.getSecurityPolicies();
        assertFalse(policies.stream().anyMatch(p -> "test-policy".equals(p.getPolicyId())));
    }

    @Test
    void testAuditSecurityEvent() {
        // Arrange
        SecurityEvent event = SecurityEvent.pluginLoaded("test-plugin", "test-source");

        // Act
        securityManager.auditSecurityEvent(event);

        // Assert
        List<SecurityEvent> auditLog = securityManager.getAuditLog("test-plugin");
        assertEquals(1, auditLog.size());
        assertEquals(event.getEventType(), auditLog.get(0).getEventType());
        assertEquals("test-plugin", auditLog.get(0).getPluginId());
    }

    @Test
    void testGetAuditLogForAllPlugins() {
        // Arrange
        SecurityEvent event1 = SecurityEvent.pluginLoaded("plugin1", "source1");
        SecurityEvent event2 = SecurityEvent.pluginLoaded("plugin2", "source2");

        securityManager.auditSecurityEvent(event1);
        securityManager.auditSecurityEvent(event2);

        // Act
        List<SecurityEvent> allEvents = securityManager.getAuditLog(null);

        // Assert
        assertEquals(2, allEvents.size());
    }

    @Test
    void testSandboxDisabled() throws Exception {
        // Arrange
        securityManager.setSandboxEnabled(false);
        String pluginId = "test-plugin";

        // Act
        PluginSandbox sandbox = securityManager.createSandbox(pluginId);

        // Assert
        assertNull(sandbox);
        assertFalse(securityManager.isInSandbox(pluginId));
    }

    @Test
    void testDefaultSecurityPolicyExists() {
        // Act
        List<SecurityPolicy> policies = securityManager.getSecurityPolicies();

        // Assert
        assertFalse(policies.isEmpty());
        assertTrue(policies.stream().anyMatch(p -> "default-restrictive".equals(p.getPolicyId())));
    }

    @Test
    void testPermissionCheckWithPolicy() throws Exception {
        // Arrange
        String pluginId = "test-plugin";
        Permission filePermission = new FilePermission("/tmp/*", "read");
        
        // 创建一个拒绝所有权限的策略
        SecurityPolicy restrictivePolicy = SecurityPolicy.builder()
            .policyId("restrictive-test")
            .name("Restrictive Test Policy")
            .type(SecurityPolicy.PolicyType.RESTRICTIVE)
            .deniedPermissions(Set.of(filePermission))
            .enabled(true)
            .build();

        securityManager.addSecurityPolicy(restrictivePolicy);

        // Act
        boolean allowed = securityManager.checkPermission(mockPlugin, filePermission);

        // Assert
        assertFalse(allowed);
    }

    @Test
    void testGetPluginPermissionsForNonExistentPlugin() {
        // Act
        Set<Permission> permissions = securityManager.getPluginPermissions("non-existent-plugin");

        // Assert
        assertNotNull(permissions);
        assertTrue(permissions.isEmpty());
    }
}
