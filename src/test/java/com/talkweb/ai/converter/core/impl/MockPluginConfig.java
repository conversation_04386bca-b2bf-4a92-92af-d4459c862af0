package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.core.PluginConfig;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class MockPluginConfig implements PluginConfig {
    private Path pluginsDir;
    private long pollInterval = 500;
    private long debounceTime = 200;
    private String mode = "BLACKLIST";
    private List<String> blacklist = new ArrayList<>();
    private List<String> whitelist = new ArrayList<>();

    public MockPluginConfig() {
        this.pluginsDir = Paths.get("src/test/resources/plugins");
    }

    public MockPluginConfig(String pluginsDir) {
        this.pluginsDir = Paths.get(pluginsDir);
    }

    @Override
    public long getPollInterval() {
        return pollInterval;
    }

    @Override
    public long getDebounceTime() {
        return debounceTime;
    }

    @Override
    public String getMode() {
        return mode;
    }

    @Override
    public List<String> getBlacklist() {
        return blacklist;
    }

    @Override
    public List<String> getWhitelist() {
        return whitelist;
    }

    @Override
    public Path getPluginsDir() {
        return pluginsDir;
    }

    public void setPluginsDir(String path) {
        this.pluginsDir = Paths.get(path);
    }

    public void setPollInterval(long pollInterval) {
        this.pollInterval = pollInterval;
    }

    public void setDebounceTime(long debounceTime) {
        this.debounceTime = debounceTime;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public void setBlacklist(List<String> blacklist) {
        this.blacklist = blacklist;
    }

    public void setWhitelist(List<String> whitelist) {
        this.whitelist = whitelist;
    }
}