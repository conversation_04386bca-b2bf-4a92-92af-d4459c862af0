package com.talkweb.ai.converter.util.image;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.awt.*;
import java.awt.image.BufferedImage;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TableDetector测试类
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class TableDetectorTest {

    private TableDetector tableDetector;
    private BufferedImage testImage;

    @BeforeEach
    void setUp() {
        tableDetector = new TableDetector();
        testImage = createTestTableImage();
    }

    @Test
    void testDetectTables_WithValidImage_ShouldReturnResult() {
        // Given
        TableDetector.TableDetectionConfig config = new TableDetector.TableDetectionConfig();
        
        // When
        TableDetector.TableDetectionResult result = tableDetector.detectTables(testImage, config);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getTables());
        assertNotNull(result.getMetadata());
    }

    @Test
    void testDetectTables_WithNullImage_ShouldReturnError() {
        // When
        TableDetector.TableDetectionResult result = tableDetector.detectTables(null);
        
        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("Input image is null", result.getErrorMessage());
    }

    @Test
    void testDetectTables_WithDefaultConfig_ShouldUseDefaults() {
        // When
        TableDetector.TableDetectionResult result = tableDetector.detectTables(testImage);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
    }

    @Test
    void testTableDetectionConfig_DefaultValues() {
        // Given
        TableDetector.TableDetectionConfig config = new TableDetector.TableDetectionConfig();
        
        // Then
        assertEquals(100, config.getMinTableWidth());
        assertEquals(50, config.getMinTableHeight());
        assertEquals(20, config.getMinCellWidth());
        assertEquals(15, config.getMinCellHeight());
        assertEquals(0.8, config.getLineThreshold());
        assertTrue(config.isEnableAdvancedDetection());
        assertEquals(0.7, config.getConfidenceThreshold());
    }

    @Test
    void testTableRegion_Properties() {
        // Given
        Rectangle bounds = new Rectangle(10, 20, 100, 50);
        java.util.List<java.util.List<Rectangle>> cells = java.util.Arrays.asList(
            java.util.Arrays.asList(new Rectangle(10, 20, 50, 25), new Rectangle(60, 20, 50, 25)),
            java.util.Arrays.asList(new Rectangle(10, 45, 50, 25), new Rectangle(60, 45, 50, 25))
        );
        double confidence = 0.85;
        
        // When
        TableDetector.TableRegion region = new TableDetector.TableRegion(bounds, cells, confidence);
        
        // Then
        assertEquals(bounds, region.getBounds());
        assertEquals(2, region.getRows());
        assertEquals(2, region.getColumns());
        assertEquals(confidence, region.getConfidence());
        assertNotNull(region.getCells());
        assertEquals(2, region.getCells().size());
    }

    @Test
    void testGetDebugInfo_ShouldReturnValidInfo() {
        // Given
        TableDetector.TableDetectionConfig config = new TableDetector.TableDetectionConfig();
        
        // When
        String debugInfo = tableDetector.getDebugInfo(testImage, config);
        
        // Then
        assertNotNull(debugInfo);
        assertTrue(debugInfo.contains("Table Detection Debug Info"));
        assertTrue(debugInfo.contains("Image Size:"));
        assertTrue(debugInfo.contains("Config:"));
    }

    @Test
    void testTableDetectionResult_Success() {
        // Given
        java.util.List<TableDetector.TableRegion> tables = java.util.Arrays.asList(
            new TableDetector.TableRegion(new Rectangle(0, 0, 100, 50), java.util.Collections.emptyList(), 0.8)
        );
        double confidence = 0.85;
        
        // When
        TableDetector.TableDetectionResult result = new TableDetector.TableDetectionResult(tables, confidence);
        
        // Then
        assertTrue(result.isSuccess());
        assertEquals(1, result.getTables().size());
        assertEquals(confidence, result.getConfidence());
        assertNull(result.getErrorMessage());
        assertNotNull(result.getMetadata());
    }

    @Test
    void testTableDetectionResult_Error() {
        // Given
        String errorMessage = "Test error";
        
        // When
        TableDetector.TableDetectionResult result = new TableDetector.TableDetectionResult(errorMessage);
        
        // Then
        assertFalse(result.isSuccess());
        assertTrue(result.getTables().isEmpty());
        assertEquals(0.0, result.getConfidence());
        assertEquals(errorMessage, result.getErrorMessage());
    }

    /**
     * 创建测试用的表格图像
     */
    private BufferedImage createTestTableImage() {
        BufferedImage image = new BufferedImage(200, 100, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 填充白色背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 200, 100);
        
        // 绘制简单的表格线条
        g2d.setColor(Color.BLACK);
        g2d.setStroke(new BasicStroke(1));
        
        // 水平线
        g2d.drawLine(20, 30, 180, 30);
        g2d.drawLine(20, 50, 180, 50);
        g2d.drawLine(20, 70, 180, 70);
        
        // 垂直线
        g2d.drawLine(20, 30, 20, 70);
        g2d.drawLine(100, 30, 100, 70);
        g2d.drawLine(180, 30, 180, 70);
        
        g2d.dispose();
        return image;
    }
}
