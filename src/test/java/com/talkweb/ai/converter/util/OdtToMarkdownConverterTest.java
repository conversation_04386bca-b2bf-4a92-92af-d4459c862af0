package com.talkweb.ai.converter.util;

import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.PluginMetadata;
import com.talkweb.ai.converter.util.odt.OdtConversionConfig;
import com.talkweb.ai.converter.util.odt.OdtConversionMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for ODT to Markdown converter
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
class OdtToMarkdownConverterTest {

    private OdtToMarkdownConverter converter;
    
    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        PluginMetadata metadata = PluginMetadata.builder()
                .id("odt-converter-test")
                .name("ODT Converter Test")
                .version("1.0.0")
                .description("Test ODT converter")
                .className("com.talkweb.ai.converter.util.OdtToMarkdownConverter")
                .build();
        
        converter = new OdtToMarkdownConverter(metadata);
    }

    @Test
    void testSupportsExtension() {
        assertTrue(converter.supportsExtension("odt"));
        assertTrue(converter.supportsExtension("ODT"));
        assertTrue(converter.supportsExtension(".odt"));
        assertFalse(converter.supportsExtension("doc"));
        assertFalse(converter.supportsExtension("docx"));
        assertFalse(converter.supportsExtension("txt"));
        assertFalse(converter.supportsExtension(null));
    }

    @Test
    void testPluginMetadata() {
        assertNotNull(converter.getMetadata());
        assertEquals("odt-converter-test", converter.getMetadata().getId());
        assertEquals("ODT Converter Test", converter.getMetadata().getName());
    }

    @Test
    void testConfigurationDefaults() {
        OdtConversionConfig config = converter.getConfig();
        assertNotNull(config);
        assertEquals(OdtConversionMode.LOOSE, config.getMode());
        assertTrue(config.isPreserveFormatting());
        assertTrue(config.isConvertTables());
        assertTrue(config.isConvertLists());
        assertTrue(config.isPreserveHeadingLevels());
    }

    @Test
    void testConfigurationBuilder() {
        OdtConversionConfig config = OdtConversionConfig.builder()
                .mode(OdtConversionMode.STRICT)
                .preserveFormatting(false)
                .convertTables(false)
                .convertLists(false)
                .preserveHeadingLevels(false)
                .build();
        
        converter.setConfig(config);
        
        assertEquals(OdtConversionMode.STRICT, converter.getConfig().getMode());
        assertFalse(converter.getConfig().isPreserveFormatting());
        assertFalse(converter.getConfig().isConvertTables());
        assertFalse(converter.getConfig().isConvertLists());
        assertFalse(converter.getConfig().isPreserveHeadingLevels());
    }

    @Test
    void testConvertNonExistentFile() throws Exception {
        File nonExistentFile = new File(tempDir.toFile(), "nonexistent.odt");

        // Should return failed result for non-existent file
        ConversionResult result = converter.convert(nonExistentFile);
        assertNotNull(result);
        assertFalse(result.isSuccess());
    }

    @Test
    void testConvertUnsupportedFile() throws IOException {
        File txtFile = tempDir.resolve("test.txt").toFile();
        try (FileWriter writer = new FileWriter(txtFile)) {
            writer.write("This is a text file");
        }
        
        assertThrows(IllegalArgumentException.class, () -> converter.convert(txtFile));
    }

    @Test
    void testStrictModeErrorHandling() throws IOException {
        // Configure strict mode
        OdtConversionConfig strictConfig = OdtConversionConfig.builder()
                .mode(OdtConversionMode.STRICT)
                .build();
        converter.setConfig(strictConfig);
        
        // Create an invalid ODT file (just a text file with .odt extension)
        File invalidOdtFile = createInvalidOdtFile();
        
        // In strict mode, should throw exception for invalid files
        assertThrows(Exception.class, () -> converter.convert(invalidOdtFile));
    }

    @Test
    void testLooseModeErrorHandling() throws Exception {
        // Configure loose mode (default)
        OdtConversionConfig looseConfig = OdtConversionConfig.builder()
                .mode(OdtConversionMode.LOOSE)
                .build();
        converter.setConfig(looseConfig);
        
        // Create an invalid ODT file
        File invalidOdtFile = createInvalidOdtFile();
        
        // In loose mode, should return result with error message but not throw exception
        ConversionResult result = converter.convert(invalidOdtFile);
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNotNull(result.getMessage());
        assertTrue(result.getMessage().contains("ODT conversion failed"));
    }

    @Test
    void testPluginLifecycle() throws Exception {
        assertEquals(converter.getState().name(), "STOPPED");
        
        converter.init(null);
        assertEquals(converter.getState().name(), "INITIALIZING");
        
        converter.start();
        assertEquals(converter.getState().name(), "RUNNING");
        
        converter.stop();
        assertEquals(converter.getState().name(), "STOPPED");
        
        converter.destroy();
        assertEquals(converter.getState().name(), "DESTROYED");
    }

    @Test
    void testConversionModes() {
        // Test all conversion modes
        for (OdtConversionMode mode : OdtConversionMode.values()) {
            OdtConversionConfig config = OdtConversionConfig.builder()
                    .mode(mode)
                    .build();
            
            converter.setConfig(config);
            assertEquals(mode, converter.getConfig().getMode());
            
            // Test mode properties
            switch (mode) {
                case STRICT:
                    assertTrue(mode.requiresStrictProcessing());
                    assertFalse(mode.allowsErrorRecovery());
                    break;
                case LOOSE:
                    assertFalse(mode.requiresStrictProcessing());
                    assertTrue(mode.allowsErrorRecovery());
                    break;
                case BALANCED:
                    assertFalse(mode.requiresStrictProcessing());
                    assertTrue(mode.allowsErrorRecovery());
                    break;
            }
        }
    }

    @Test
    void testConfigurationToString() {
        OdtConversionConfig config = OdtConversionConfig.builder()
                .mode(OdtConversionMode.STRICT)
                .preserveFormatting(true)
                .convertTables(true)
                .maxDocumentSize(1024 * 1024)
                .defaultEncoding("UTF-8")
                .build();
        
        String configString = config.toString();
        assertNotNull(configString);
        assertTrue(configString.contains("STRICT"));
        assertTrue(configString.contains("preserveFormatting=true"));
        assertTrue(configString.contains("convertTables=true"));
        assertTrue(configString.contains("UTF-8"));
    }

    @Test
    void testDefaultConversionMode() {
        assertEquals(OdtConversionMode.LOOSE, OdtConversionMode.getDefault());
    }

    @Test
    void testConversionModeDescriptions() {
        for (OdtConversionMode mode : OdtConversionMode.values()) {
            assertNotNull(mode.getDescription());
            assertFalse(mode.getDescription().isEmpty());
            
            String toString = mode.toString();
            assertTrue(toString.contains(mode.name()));
            assertTrue(toString.contains(mode.getDescription()));
        }
    }

    /**
     * Creates an invalid ODT file for testing error handling
     */
    private File createInvalidOdtFile() throws IOException {
        File odtFile = tempDir.resolve("invalid.odt").toFile();
        
        // Create invalid ODT content (just plain text, not a valid ODT/ZIP file)
        String invalidContent = "This is not a valid ODT file content";
        
        try (FileWriter writer = new FileWriter(odtFile)) {
            writer.write(invalidContent);
        }
        
        return odtFile;
    }
}
