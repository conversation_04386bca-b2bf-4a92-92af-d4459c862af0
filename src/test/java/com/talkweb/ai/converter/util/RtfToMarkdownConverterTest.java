package com.talkweb.ai.converter.util;

import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.PluginMetadata;
import com.talkweb.ai.converter.util.rtf.RtfConversionConfig;
import com.talkweb.ai.converter.util.rtf.RtfConversionMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for RTF to Markdown converter
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
class RtfToMarkdownConverterTest {

    private RtfToMarkdownConverter converter;
    
    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        PluginMetadata metadata = PluginMetadata.builder()
                .id("rtf-converter-test")
                .name("RTF Converter Test")
                .version("1.0.0")
                .description("Test RTF converter")
                .className("com.talkweb.ai.converter.util.RtfToMarkdownConverter")
                .build();
        
        converter = new RtfToMarkdownConverter(metadata);
    }

    @Test
    void testSupportsExtension() {
        assertTrue(converter.supportsExtension("rtf"));
        assertTrue(converter.supportsExtension("RTF"));
        assertTrue(converter.supportsExtension(".rtf"));
        assertFalse(converter.supportsExtension("doc"));
        assertFalse(converter.supportsExtension("txt"));
        assertFalse(converter.supportsExtension(null));
    }

    @Test
    void testPluginMetadata() {
        assertNotNull(converter.getMetadata());
        assertEquals("rtf-converter-test", converter.getMetadata().getId());
        assertEquals("RTF Converter Test", converter.getMetadata().getName());
    }

    @Test
    void testConfigurationDefaults() {
        RtfConversionConfig config = converter.getConfig();
        assertNotNull(config);
        assertEquals(RtfConversionMode.LOOSE, config.getMode());
        assertTrue(config.isUseAdvancedParsing());
        assertTrue(config.isPreserveFormatting());
    }

    @Test
    void testConfigurationBuilder() {
        RtfConversionConfig config = RtfConversionConfig.builder()
                .mode(RtfConversionMode.STRICT)
                .useAdvancedParsing(false)
                .preserveFormatting(false)
                .convertTables(false)
                .build();
        
        converter.setConfig(config);
        
        assertEquals(RtfConversionMode.STRICT, converter.getConfig().getMode());
        assertFalse(converter.getConfig().isUseAdvancedParsing());
        assertFalse(converter.getConfig().isPreserveFormatting());
        assertFalse(converter.getConfig().isConvertTables());
    }

    @Test
    void testConvertSimpleRtfFile() throws Exception {
        // Create a simple RTF file for testing
        File rtfFile = createSimpleRtfFile();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        assertFalse(result.getContent().trim().isEmpty());
    }

    @Test
    void testConvertNonExistentFile() throws Exception {
        File nonExistentFile = new File(tempDir.toFile(), "nonexistent.rtf");

        // In loose mode, should return failed result instead of throwing exception
        ConversionResult result = converter.convert(nonExistentFile);
        assertNotNull(result);
        assertFalse(result.isSuccess());
    }

    @Test
    void testConvertUnsupportedFile() throws IOException {
        File txtFile = tempDir.resolve("test.txt").toFile();
        try (FileWriter writer = new FileWriter(txtFile)) {
            writer.write("This is a text file");
        }
        
        assertThrows(IllegalArgumentException.class, () -> converter.convert(txtFile));
    }

    @Test
    void testStrictModeErrorHandling() throws Exception {
        // Configure strict mode
        RtfConversionConfig strictConfig = RtfConversionConfig.builder()
                .mode(RtfConversionMode.STRICT)
                .build();
        converter.setConfig(strictConfig);

        // Create a non-existent file to trigger an error
        File nonExistentFile = new File(tempDir.toFile(), "nonexistent-strict.rtf");

        // In strict mode, should throw exception for file errors
        assertThrows(Exception.class, () -> converter.convert(nonExistentFile));
    }

    @Test
    void testLooseModeErrorHandling() throws Exception {
        // Configure loose mode (default)
        RtfConversionConfig looseConfig = RtfConversionConfig.builder()
                .mode(RtfConversionMode.LOOSE)
                .build();
        converter.setConfig(looseConfig);
        
        // Create an invalid RTF file
        File invalidRtfFile = createInvalidRtfFile();
        
        // In loose mode, should return result with error but not throw exception
        ConversionResult result = converter.convert(invalidRtfFile);
        assertNotNull(result);
        // Result might be successful with fallback content or unsuccessful with error message
    }

    @Test
    void testPluginLifecycle() throws Exception {
        assertEquals(converter.getState().name(), "STOPPED");
        
        converter.init(null);
        assertEquals(converter.getState().name(), "INITIALIZING");
        
        converter.start();
        assertEquals(converter.getState().name(), "RUNNING");
        
        converter.stop();
        assertEquals(converter.getState().name(), "STOPPED");
        
        converter.destroy();
        assertEquals(converter.getState().name(), "DESTROYED");
    }

    /**
     * Creates a simple RTF file for testing
     */
    private File createSimpleRtfFile() throws IOException {
        File rtfFile = tempDir.resolve("test.rtf").toFile();
        
        // Create a basic RTF content
        String rtfContent = "{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}} " +
                "\\f0\\fs24 Hello World! \\par " +
                "This is a test RTF document. \\par " +
                "\\b Bold text \\b0 and \\i italic text \\i0. \\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates an invalid RTF file for testing error handling
     */
    private File createInvalidRtfFile() throws IOException {
        File rtfFile = tempDir.resolve("invalid.rtf").toFile();
        
        // Create invalid RTF content
        String invalidContent = "This is not a valid RTF file content";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(invalidContent);
        }
        
        return rtfFile;
    }
}
