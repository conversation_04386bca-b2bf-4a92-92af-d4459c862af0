package com.talkweb.ai.converter.util;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginMetadata;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.jar.JarEntry;
import java.util.jar.JarOutputStream;
import java.util.jar.Manifest;

import static org.junit.jupiter.api.Assertions.*;

class PluginUtilsTest {

    @TempDir
    Path tempDir;
    
    @Test
    void testLoadPluginMetadataFromClass() throws PluginException {
        // 加载插件元数据应该抛出异常，因为没有元数据文件
        assertThrows(PluginException.class, () -> PluginUtils.loadPluginMetadata(getClass()));
    }
    
    @Test
    void testLoadPluginMetadataFromJar() throws IOException, PluginException {
        // 创建测试JAR文件
        Path jarPath = tempDir.resolve("test-plugin.jar");
        createTestJar(jarPath.toFile());
        
        // 加载插件元数据
        PluginMetadata metadata = PluginUtils.loadPluginMetadata(jarPath.toFile());
        
        // 验证元数据
        assertEquals("test-plugin.jar", metadata.getId());
        assertEquals("test-plugin", metadata.getName());
        assertEquals("1.0.0", metadata.getVersion());
    }
    
    @Test
    void testLoadPluginMetadataFromInvalidJar() throws IOException {
        // 创建无效JAR文件（没有元数据）
        Path jarPath = tempDir.resolve("invalid-plugin.jar");
        createInvalidJar(jarPath.toFile());
        
        // 加载插件元数据应该抛出异常
        assertThrows(PluginException.class, () -> PluginUtils.loadPluginMetadata(jarPath.toFile()));
    }
    
    @Test
    void testExtractJar() throws IOException {
        // 创建测试JAR文件
        Path jarPath = tempDir.resolve("extract-test.jar");
        createTestJar(jarPath.toFile());
        
        // 提取JAR文件
        Path extractDir = tempDir.resolve("extract");
        PluginUtils.extractJar(jarPath.toFile(), extractDir);
        
        // 验证提取结果
        assertTrue(Files.exists(extractDir.resolve("META-INF/plugin.xml")));
        assertTrue(Files.exists(extractDir.resolve("META-INF/MANIFEST.MF")));
    }
    
    /**
     * 创建测试JAR文件
     * @param jarFile JAR文件
     * @throws IOException 如果创建失败
     */
    private void createTestJar(File jarFile) throws IOException {
        Manifest manifest = new Manifest();
        manifest.getMainAttributes().putValue("Manifest-Version", "1.0");
        
        try (JarOutputStream jos = new JarOutputStream(new FileOutputStream(jarFile), manifest)) {
            // 添加插件元数据
            JarEntry entry = new JarEntry("META-INF/plugin.xml");
            jos.putNextEntry(entry);
            jos.write("<plugin>test</plugin>".getBytes());
            jos.closeEntry();
            
            // 添加测试类
            entry = new JarEntry("com/test/TestPlugin.class");
            jos.putNextEntry(entry);
            jos.write(new byte[]{(byte)0xCA, (byte)0xFE, (byte)0xBA, (byte)0xBE}); // 假的类文件头
            jos.closeEntry();
        }
    }
    
    /**
     * 创建无效JAR文件（没有元数据）
     * @param jarFile JAR文件
     * @throws IOException 如果创建失败
     */
    private void createInvalidJar(File jarFile) throws IOException {
        Manifest manifest = new Manifest();
        manifest.getMainAttributes().putValue("Manifest-Version", "1.0");
        
        try (JarOutputStream jos = new JarOutputStream(new FileOutputStream(jarFile), manifest)) {
            // 只添加测试类，不添加元数据
            JarEntry entry = new JarEntry("com/test/TestPlugin.class");
            jos.putNextEntry(entry);
            jos.write(new byte[]{(byte)0xCA, (byte)0xFE, (byte)0xBA, (byte)0xBE}); // 假的类文件头
            jos.closeEntry();
        }
    }
}