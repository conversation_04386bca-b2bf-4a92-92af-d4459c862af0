package com.talkweb.ai.converter.util;

import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.PluginMetadata;
import com.talkweb.ai.converter.util.rtf.RtfConversionConfig;
import com.talkweb.ai.converter.util.rtf.RtfConversionMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Enhanced test class for RTF to Markdown converter with complex RTF features
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
class RtfToMarkdownConverterEnhancedTest {

    private RtfToMarkdownConverter converter;
    
    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        PluginMetadata metadata = PluginMetadata.builder()
                .id("rtf-converter-enhanced-test")
                .name("RTF Converter Enhanced Test")
                .version("1.0.0")
                .description("Enhanced test RTF converter")
                .className("com.talkweb.ai.converter.util.RtfToMarkdownConverter")
                .build();
        
        converter = new RtfToMarkdownConverter(metadata);
        
        // Configure for advanced parsing
        RtfConversionConfig config = RtfConversionConfig.builder()
                .mode(RtfConversionMode.LOOSE)
                .useAdvancedParsing(true)
                .preserveFormatting(true)
                .build();
        converter.setConfig(config);
    }

    @Test
    void testConvertRtfWithBoldAndItalic() throws Exception {
        File rtfFile = createRtfWithFormatting();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("RTF with formatting result:\n" + content);
        
        // Should contain bold and italic formatting
        assertTrue(content.contains("**") || content.contains("*"));
    }

    @Test
    void testConvertRtfWithHeadings() throws Exception {
        File rtfFile = createRtfWithHeadings();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("RTF with headings result:\n" + content);
        
        // Should contain heading markers
        assertTrue(content.contains("#"));
    }

    @Test
    void testConvertRtfWithParagraphs() throws Exception {
        File rtfFile = createRtfWithParagraphs();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("RTF with paragraphs result:\n" + content);
        
        // Should contain paragraph breaks
        assertTrue(content.contains("\n"));
    }

    @Test
    void testConvertComplexRtfDocument() throws Exception {
        File rtfFile = createComplexRtfDocument();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("Complex RTF document result:\n" + content);
        
        // Should contain various elements
        assertFalse(content.trim().isEmpty());
        assertTrue(content.length() > 50); // Should have substantial content
    }

    @Test
    void testAdvancedParsingVsSimpleParsing() throws Exception {
        File rtfFile = createRtfWithFormatting();
        
        // Test with advanced parsing
        RtfConversionConfig advancedConfig = RtfConversionConfig.builder()
                .useAdvancedParsing(true)
                .preserveFormatting(true)
                .build();
        converter.setConfig(advancedConfig);
        
        ConversionResult advancedResult = converter.convert(rtfFile);
        
        // Test with simple parsing
        RtfConversionConfig simpleConfig = RtfConversionConfig.builder()
                .useAdvancedParsing(false)
                .preserveFormatting(false)
                .build();
        converter.setConfig(simpleConfig);
        
        ConversionResult simpleResult = converter.convert(rtfFile);
        
        // Both should succeed
        assertTrue(advancedResult.isSuccess());
        assertTrue(simpleResult.isSuccess());
        
        System.out.println("Advanced parsing result:\n" + advancedResult.getContent());
        System.out.println("Simple parsing result:\n" + simpleResult.getContent());
        
        // Advanced parsing might preserve more structure
        assertNotNull(advancedResult.getContent());
        assertNotNull(simpleResult.getContent());
    }

    /**
     * Creates RTF file with bold and italic formatting
     */
    private File createRtfWithFormatting() throws IOException {
        File rtfFile = tempDir.resolve("formatted.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0 " +
                "{\\fonttbl {\\f0 Times New Roman;}} " +
                "\\f0\\fs24 " +
                "This is normal text. \\par " +
                "\\b This is bold text. \\b0 \\par " +
                "\\i This is italic text. \\i0 \\par " +
                "\\b\\i This is bold and italic. \\b0\\i0 \\par " +
                "Back to normal text. \\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates RTF file with heading styles
     */
    private File createRtfWithHeadings() throws IOException {
        File rtfFile = tempDir.resolve("headings.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0 " +
                "{\\fonttbl {\\f0 Times New Roman;}} " +
                "\\f0\\fs24 " +
                "\\s1 Chapter 1: Introduction \\par " +
                "\\pard This is the introduction paragraph. \\par " +
                "\\s2 Section 1.1: Overview \\par " +
                "\\pard This is the overview section. \\par " +
                "\\s3 Subsection 1.1.1: Details \\par " +
                "\\pard These are the details. \\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates RTF file with multiple paragraphs
     */
    private File createRtfWithParagraphs() throws IOException {
        File rtfFile = tempDir.resolve("paragraphs.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0 " +
                "{\\fonttbl {\\f0 Times New Roman;}} " +
                "\\f0\\fs24 " +
                "First paragraph with some text content. \\par " +
                "\\par " +
                "Second paragraph after a line break. \\par " +
                "\\par " +
                "Third paragraph with more content. \\line " +
                "Same paragraph, new line. \\par " +
                "\\par " +
                "Final paragraph. \\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates a complex RTF document with various features
     */
    private File createComplexRtfDocument() throws IOException {
        File rtfFile = tempDir.resolve("complex.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0 " +
                "{\\fonttbl {\\f0 Times New Roman;}{\\f1 Arial;}} " +
                "{\\colortbl;\\red255\\green0\\blue0;\\red0\\green255\\blue0;} " +
                "\\f0\\fs24 " +
                "\\s1 Document Title \\par " +
                "\\pard \\par " +
                "This document contains \\b bold text \\b0, \\i italic text \\i0, " +
                "and \\ul underlined text \\ul0. \\par " +
                "\\par " +
                "\\s2 Section with Different Font \\par " +
                "\\f1 This text uses Arial font. \\f0 \\par " +
                "\\par " +
                "\\s3 Formatted Content \\par " +
                "\\pard Here is a paragraph with \\b\\i bold italic \\b0\\i0 text. \\par " +
                "\\tab This line starts with a tab. \\par " +
                "\\line This is a new line in the same paragraph. \\par " +
                "\\par " +
                "End of document. \\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }
}
