package com.talkweb.ai.converter.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class HtmlToMarkdownConverterTest {

    @Test
    void testConvertSimpleHtml() {
        String html = "<h1>Title</h1><p>This is a paragraph.</p>";
        String result = HtmlToMarkdownConverter.convert(html);
        
        assertTrue(result.contains("# Title"));
        assertTrue(result.contains("This is a paragraph."));
    }

    @Test
    void testConvertWithLooseMode() {
        String html = "<h1>Title</h1><p>This is a paragraph.</p>";
        String result = HtmlToMarkdownConverter.convert(html, HtmlConversionMode.LOOSE);
        
        assertTrue(result.contains("# Title"));
        assertTrue(result.contains("This is a paragraph."));
    }

    @Test
    void testConvertWithStrictMode() {
        String cleanHtml = "<h1>Title</h1><p>Clean content</p>";
        String result = HtmlToMarkdownConverter.convert(cleanHtml, HtmlConversionMode.STRICT);
        
        assertTrue(result.contains("# Title"));
        assertTrue(result.contains("Clean content"));
    }

    @Test
    void testConvertStrictModeWithProblematicHtml() {
        String problematicHtml = "<script>alert('test');</script><h1>Title</h1>";
        
        assertThrows(RuntimeException.class, () -> {
            HtmlToMarkdownConverter.convert(problematicHtml, HtmlConversionMode.STRICT);
        });
    }

    @Test
    void testConvertComplexHtml() {
        String complexHtml = """
            <html>
            <body>
                <h1>Main Title</h1>
                <h2>Subtitle</h2>
                <p>This is a paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
                <p>Here's a <a href="https://example.com">link</a> and some <code>inline code</code>.</p>
                
                <ul>
                    <li>First item</li>
                    <li>Second item</li>
                </ul>
                
                <ol>
                    <li>Ordered item 1</li>
                    <li>Ordered item 2</li>
                </ol>
                
                <table>
                    <tr>
                        <th>Header 1</th>
                        <th>Header 2</th>
                    </tr>
                    <tr>
                        <td>Cell 1</td>
                        <td>Cell 2</td>
                    </tr>
                </table>
                
                <blockquote>This is a blockquote</blockquote>
                
                <pre><code>function test() { return true; }</code></pre>
                
                <hr>
                
                <img src="image.jpg" alt="Test Image">
            </body>
            </html>
            """;
        
        String result = HtmlToMarkdownConverter.convert(complexHtml, HtmlConversionMode.LOOSE);
        
        // Verify headings
        assertTrue(result.contains("# Main Title"));
        assertTrue(result.contains("## Subtitle"));
        
        // Verify formatting
        assertTrue(result.contains("**bold**"));
        assertTrue(result.contains("*italic*"));
        assertTrue(result.contains("[link](https://example.com)"));
        assertTrue(result.contains("`inline code`"));
        
        // Verify lists
        assertTrue(result.contains("- First item"));
        assertTrue(result.contains("- Second item"));
        assertTrue(result.contains("1. Ordered item 1"));
        assertTrue(result.contains("2. Ordered item 2"));
        
        // Verify table
        assertTrue(result.contains("| Header 1 | Header 2 |"));
        assertTrue(result.contains("|---|---|"));
        assertTrue(result.contains("| Cell 1 | Cell 2 |"));
        
        // Verify blockquote
        assertTrue(result.contains("> This is a blockquote"));
        
        // Verify code block
        assertTrue(result.contains("```"));
        assertTrue(result.contains("function test()"));
        
        // Verify horizontal rule
        assertTrue(result.contains("---"));
        
        // Verify image
        assertTrue(result.contains("![Test Image](image.jpg)"));
    }

    @Test
    void testConvertNestedElements() {
        String nestedHtml = """
            <div>
                <p>Paragraph with <strong>bold <em>and italic</em></strong> text.</p>
                <ul>
                    <li>Item with <a href="link.html">nested link</a></li>
                    <li>Item with <code>nested code</code></li>
                </ul>
            </div>
            """;
        
        String result = HtmlToMarkdownConverter.convert(nestedHtml, HtmlConversionMode.LOOSE);
        
        assertTrue(result.contains("**bold *and italic***"));
        assertTrue(result.contains("- Item with [nested link](link.html)"));
        assertTrue(result.contains("- Item with `nested code`"));
    }

    @Test
    void testConvertHtmlEntities() {
        String htmlWithEntities = """
            <p>Entities: &lt; &gt; &amp; &quot; &apos; &nbsp; &copy; &trade; &mdash; &ndash;</p>
            """;
        
        String result = HtmlToMarkdownConverter.convert(htmlWithEntities, HtmlConversionMode.LOOSE);
        
        assertTrue(result.contains("< > & \" '"));
        assertTrue(result.contains("©"));
        assertTrue(result.contains("™"));
        assertTrue(result.contains("—"));
        assertTrue(result.contains("–"));
    }

    @Test
    void testConvertEmptyAndNullInput() {
        assertEquals("", HtmlToMarkdownConverter.convert(null));
        assertEquals("", HtmlToMarkdownConverter.convert(""));
        assertEquals("", HtmlToMarkdownConverter.convert("   "));
    }

    @Test
    void testConvertTableWithComplexStructure() {
        String tableHtml = """
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Age</th>
                        <th>City</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>John</td>
                        <td>25</td>
                        <td>New York</td>
                    </tr>
                    <tr>
                        <td>Jane</td>
                        <td>30</td>
                        <td>Los Angeles</td>
                    </tr>
                </tbody>
            </table>
            """;
        
        String result = HtmlToMarkdownConverter.convert(tableHtml, HtmlConversionMode.LOOSE);
        
        assertTrue(result.contains("| Name | Age | City |"));
        assertTrue(result.contains("|---|---|---|"));
        assertTrue(result.contains("| John | 25 | New York |"));
        assertTrue(result.contains("| Jane | 30 | Los Angeles |"));
    }

    @Test
    void testConvertTableWithEscapedPipes() {
        String tableHtml = """
            <table>
                <tr>
                    <th>Code</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>a | b</td>
                    <td>Pipe character</td>
                </tr>
            </table>
            """;
        
        String result = HtmlToMarkdownConverter.convert(tableHtml, HtmlConversionMode.LOOSE);
        
        assertTrue(result.contains("| Code | Description |"));
        assertTrue(result.contains("| a \\| b | Pipe character |"));
    }

    @Test
    void testConvertPreservesElementOrder() {
        String orderedHtml = """
            <h1>First</h1>
            <p>Second</p>
            <h2>Third</h2>
            <ul><li>Fourth</li></ul>
            <p>Fifth</p>
            """;
        
        String result = HtmlToMarkdownConverter.convert(orderedHtml, HtmlConversionMode.LOOSE);
        
        int firstPos = result.indexOf("# First");
        int secondPos = result.indexOf("Second");
        int thirdPos = result.indexOf("## Third");
        int fourthPos = result.indexOf("- Fourth");
        int fifthPos = result.indexOf("Fifth");
        
        assertTrue(firstPos < secondPos);
        assertTrue(secondPos < thirdPos);
        assertTrue(thirdPos < fourthPos);
        assertTrue(fourthPos < fifthPos);
    }

    @Test
    void testFallbackConversionInLooseMode() {
        // Test with malformed HTML that might cause parsing issues
        String malformedHtml = "<h1>Title<p>No closing tag<strong>Bold";
        
        // Should not throw exception in LOOSE mode
        String result = HtmlToMarkdownConverter.convert(malformedHtml, HtmlConversionMode.LOOSE);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
