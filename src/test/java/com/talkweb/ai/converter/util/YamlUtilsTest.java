package com.talkweb.ai.converter.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class YamlUtilsTest {

    @TempDir
    Path tempDir;
    
    @Test
    void testLoadYaml() throws IOException {
        // 创建YAML文件
        String yaml = "key1: value1\n" +
                "key2: 123\n" +
                "nested:\n" +
                "  nestedKey1: nestedValue1\n" +
                "  nestedKey2: 456\n";
        
        Path yamlFile = tempDir.resolve("test.yaml");
        Files.writeString(yamlFile, yaml);
        
        // 加载YAML
        Map<String, Object> result = YamlUtils.loadYaml(yamlFile.toFile());
        
        // 验证结果
        assertEquals("value1", result.get("key1"));
        assertEquals(123, result.get("key2"));
        assertTrue(result.get("nested") instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> nested = (Map<String, Object>) result.get("nested");
        assertEquals("nestedValue1", nested.get("nestedKey1"));
        assertEquals(456, nested.get("nestedKey2"));
    }
    
    @Test
    void testLoadEmptyYaml() throws IOException {
        // 创建空YAML文件
        Path yamlFile = tempDir.resolve("empty.yaml");
        Files.writeString(yamlFile, "");
        
        // 加载YAML
        Map<String, Object> result = YamlUtils.loadYaml(yamlFile.toFile());
        
        // 验证结果
        assertTrue(result.isEmpty());
    }
    
    @Test
    void testLoadInvalidYaml() throws IOException {
        // 创建无效YAML文件
        String yaml = "invalid: : yaml";
        Path yamlFile = tempDir.resolve("invalid.yaml");
        Files.writeString(yamlFile, yaml);
        
        // 加载YAML应该抛出异常
        assertThrows(Exception.class, () -> YamlUtils.loadYaml(yamlFile.toFile()));
    }
    
    @Test
    void testFlattenYamlMap() throws IOException {
        // 创建嵌套Map
        String yaml = "key1: value1\n" +
                "key2: 123\n" +
                "nested:\n" +
                "  nestedKey1: nestedValue1\n" +
                "  nestedKey2: 456\n" +
                "  deepNested:\n" +
                "    deepKey: deepValue\n";
        
        Path yamlFile = tempDir.resolve("nested.yaml");
        Files.writeString(yamlFile, yaml);
        
        // 加载YAML
        Map<String, Object> yamlMap = YamlUtils.loadYaml(yamlFile.toFile());
        
        // 扁平化Map
        Map<String, String> flattened = YamlUtils.flattenYamlMap(yamlMap);
        
        // 验证结果
        assertEquals("value1", flattened.get("key1"));
        assertEquals("123", flattened.get("key2"));
        assertEquals("nestedValue1", flattened.get("nested.nestedKey1"));
        assertEquals("456", flattened.get("nested.nestedKey2"));
        assertEquals("deepValue", flattened.get("nested.deepNested.deepKey"));
    }
    
    @Test
    void testConvertYamlListToMap() throws IOException {
        // 创建带列表的YAML
        String yaml = "list:\n" +
                "  - item1\n" +
                "  - item2\n" +
                "  - name: item3\n" +
                "    value: 789\n";
        
        Path yamlFile = tempDir.resolve("list.yaml");
        Files.writeString(yamlFile, yaml);
        
        // 加载YAML
        Map<String, Object> yamlMap = YamlUtils.loadYaml(yamlFile.toFile());
        
        // 获取列表
        @SuppressWarnings("unchecked")
        java.util.List<Object> list = (java.util.List<Object>) yamlMap.get("list");
        assertNotNull(list);
        
        // 转换列表
        Map<String, Object> convertedMap = YamlUtils.convertYamlListToMap(list);
        
        // 验证结果
        assertEquals(list, convertedMap.get("list"));
        assertEquals("item1", convertedMap.get("0"));
        assertEquals("item2", convertedMap.get("1"));
        assertTrue(convertedMap.get("2") instanceof com.talkweb.ai.converter.config.MapConfiguration);
        assertEquals(3, convertedMap.get("size"));
    }
}