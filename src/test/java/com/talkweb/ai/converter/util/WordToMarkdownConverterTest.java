package com.talkweb.ai.converter.util;

import com.talkweb.ai.converter.util.word.WordConversionMode;
import com.talkweb.ai.converter.util.word.WordConversionOptions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.io.TempDir;
import org.apache.poi.xwpf.usermodel.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.math.BigInteger;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for WordToMarkdownConverter
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class WordToMarkdownConverterTest {
    
    @TempDir
    Path tempDir;
    
    private File testDocxFile;
    
    @BeforeEach
    void setUp() throws IOException {
        // Create a simple test DOCX file
        testDocxFile = tempDir.resolve("test.docx").toFile();
        createTestDocxFile(testDocxFile);
    }
    
    @Test
    void testConvertSimpleDocument() throws IOException {
        // Test basic conversion
        String markdown = WordToMarkdownConverter.convert(testDocxFile);
        
        assertNotNull(markdown);
        assertFalse(markdown.trim().isEmpty());
        
        // Check for expected content
        assertTrue(markdown.contains("# Test Heading"));
        assertTrue(markdown.contains("This is a test paragraph"));
        assertTrue(markdown.contains("**bold text**"));
        assertTrue(markdown.contains("*italic text*"));
    }
    
    @Test
    void testConvertWithStrictMode() throws IOException {
        // Test strict mode conversion
        String markdown = WordToMarkdownConverter.convert(testDocxFile, WordConversionMode.STRICT);
        
        assertNotNull(markdown);
        assertFalse(markdown.trim().isEmpty());
    }
    
    @Test
    void testConvertWithLooseMode() throws IOException {
        // Test loose mode conversion
        String markdown = WordToMarkdownConverter.convert(testDocxFile, WordConversionMode.LOOSE);
        
        assertNotNull(markdown);
        assertFalse(markdown.trim().isEmpty());
    }
    
    @Test
    void testConvertWithOptions() throws IOException {
        // Test conversion with custom options
        WordConversionOptions options = new WordConversionOptions()
                .setExtractImages(false)
                .setPreserveTableFormatting(true)
                .setSkipEmptyParagraphs(true);
        
        String markdown = WordToMarkdownConverter.convert(testDocxFile, options);
        
        assertNotNull(markdown);
        assertFalse(markdown.trim().isEmpty());
    }
    
    @Test
    void testConvertWithHighFidelityOptions() throws IOException {
        // Test conversion with high fidelity options
        WordConversionOptions options = WordConversionOptions.highFidelity();
        
        String markdown = WordToMarkdownConverter.convert(testDocxFile, options);
        
        assertNotNull(markdown);
        assertFalse(markdown.trim().isEmpty());
    }
    
    @Test
    void testConvertWithBasicOptions() throws IOException {
        // Test conversion with basic options
        WordConversionOptions options = WordConversionOptions.basic();
        
        String markdown = WordToMarkdownConverter.convert(testDocxFile, options);
        
        assertNotNull(markdown);
        assertFalse(markdown.trim().isEmpty());
    }
    
    @Test
    void testConvertNonExistentFile() {
        // Test with non-existent file
        File nonExistentFile = new File("non-existent.docx");
        
        assertThrows(IllegalArgumentException.class, () -> {
            WordToMarkdownConverter.convert(nonExistentFile);
        });
    }
    
    @Test
    void testConvertNullFile() {
        // Test with null file
        assertThrows(IllegalArgumentException.class, () -> {
            WordToMarkdownConverter.convert((File) null);
        });
    }
    
    @Test
    void testWordConversionOptions() {
        // Test WordConversionOptions functionality
        WordConversionOptions options = new WordConversionOptions();

        // Test default values (based on actual defaults in WordConversionOptions)
        assertFalse(options.isExtractImages()); // Default is false
        assertEquals("images", options.getImageOutputDir()); // Default is "images" not "images/"
        assertTrue(options.isPreserveTableFormatting());
        assertFalse(options.isConvertFootnotes()); // Default is false
        assertFalse(options.isIncludePageBreaks());
        
        // Test setters
        options.setExtractImages(false)
               .setImageOutputDir("custom/")
               .setPreserveTableFormatting(false);

        assertFalse(options.isExtractImages());
        assertEquals("custom/", options.getImageOutputDir());
        assertFalse(options.isIncludeTables()); // setPreserveTableFormatting actually sets includeTables field
        
        // Test copy constructor
        WordConversionOptions copy = new WordConversionOptions(options);
        assertEquals(options.isExtractImages(), copy.isExtractImages());
        assertEquals(options.getImageOutputDir(), copy.getImageOutputDir());
        assertEquals(options.isIncludeTables(), copy.isIncludeTables());
    }
    
    @Test
    void testWordConversionMode() {
        // Test enum values (WordConversionMode has 6 values: STRICT, LOOSE, STANDARD, MINIMAL, COMPLETE, CUSTOM)
        assertEquals(6, WordConversionMode.values().length);
        assertNotNull(WordConversionMode.STRICT);
        assertNotNull(WordConversionMode.LOOSE);
    }
    
    /**
     * Creates a test DOCX file with various elements
     */
    private void createTestDocxFile(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {
            
            // Add heading
            XWPFParagraph heading = document.createParagraph();
            heading.setStyle("Heading1");
            XWPFRun headingRun = heading.createRun();
            headingRun.setText("Test Heading");
            headingRun.setBold(true);
            headingRun.setFontSize(18);
            
            // Add regular paragraph
            XWPFParagraph paragraph = document.createParagraph();
            XWPFRun run1 = paragraph.createRun();
            run1.setText("This is a test paragraph with ");
            
            XWPFRun run2 = paragraph.createRun();
            run2.setText("bold text");
            run2.setBold(true);
            
            XWPFRun run3 = paragraph.createRun();
            run3.setText(" and ");
            
            XWPFRun run4 = paragraph.createRun();
            run4.setText("italic text");
            run4.setItalic(true);
            
            XWPFRun run5 = paragraph.createRun();
            run5.setText(".");
            
            // Add another paragraph
            XWPFParagraph paragraph2 = document.createParagraph();
            XWPFRun run6 = paragraph2.createRun();
            run6.setText("This is another paragraph for testing purposes.");
            
            // Add empty paragraph
            document.createParagraph();
            
            // Add paragraph with special characters
            XWPFParagraph paragraph3 = document.createParagraph();
            XWPFRun run7 = paragraph3.createRun();
            run7.setText("This paragraph contains special characters: *asterisk* _underscore_ `backtick` #hash [bracket] (parenthesis) |pipe|");
            
            document.write(out);
        }
    }

    // ========== Additional Comprehensive Test Cases ==========

    @Test
    void testAdvancedWordConversionOptions() {
        WordConversionOptions options = new WordConversionOptions();

        // Test advanced options
        assertTrue(options.isGenerateImageAltText());
        assertTrue(options.isPreserveListFormatting());
        assertFalse(options.isConvertHeadersFooters());
        assertFalse(options.isIncludeComments());
        assertFalse(options.isIncludePageBreaks());
        assertTrue(options.isPreserveHyperlinks());

        // Test image format options
        assertEquals(WordConversionOptions.ImageFormat.PNG, options.getPreferredImageFormat());
        assertEquals(800, options.getMaxImageWidth());
        assertEquals(600, options.getMaxImageHeight());

        // Test configuration chaining
        options.setGenerateImageAltText(false)
               .setPreserveListFormatting(false)
               .setConvertHeadersFooters(true)
               .setIncludeComments(true)
               .setIncludePageBreaks(true)
               .setPreserveHyperlinks(false)
               .setPreferredImageFormat(WordConversionOptions.ImageFormat.JPEG)
               .setMaxImageWidth(1024)
               .setMaxImageHeight(768);

        assertFalse(options.isGenerateImageAltText());
        assertFalse(options.isPreserveListFormatting());
        assertTrue(options.isConvertHeadersFooters());
        assertTrue(options.isIncludeComments());
        assertTrue(options.isIncludePageBreaks());
        assertFalse(options.isPreserveHyperlinks());
        assertEquals(WordConversionOptions.ImageFormat.JPEG, options.getPreferredImageFormat());
        assertEquals(1024, options.getMaxImageWidth());
        assertEquals(768, options.getMaxImageHeight());
    }

    @Test
    void testImageFormatEnum() {
        // Test all image format values
        WordConversionOptions.ImageFormat[] formats = WordConversionOptions.ImageFormat.values();
        assertEquals(5, formats.length);

        // Test specific formats
        assertNotNull(WordConversionOptions.ImageFormat.PNG);
        assertNotNull(WordConversionOptions.ImageFormat.JPEG);
        assertNotNull(WordConversionOptions.ImageFormat.GIF);
        assertNotNull(WordConversionOptions.ImageFormat.BMP);
        assertNotNull(WordConversionOptions.ImageFormat.ORIGINAL);

        // Test valueOf
        assertEquals(WordConversionOptions.ImageFormat.PNG,
                    WordConversionOptions.ImageFormat.valueOf("PNG"));
        assertEquals(WordConversionOptions.ImageFormat.JPEG,
                    WordConversionOptions.ImageFormat.valueOf("JPEG"));
    }

    @Test
    void testPresetConfigurations() {
        // Test high fidelity preset (based on actual implementation)
        WordConversionOptions highFidelity = WordConversionOptions.highFidelity();
        assertFalse(highFidelity.isExtractImages()); // Default is false, not changed in highFidelity
        assertTrue(highFidelity.isIncludeTables()); // highFidelity calls createComplete which sets this to true
        assertFalse(highFidelity.isConvertFootnotes()); // Default is false, not changed in highFidelity
        assertTrue(highFidelity.isPreserveHyperlinks());
        assertTrue(highFidelity.isPreserveListFormatting());
        assertTrue(highFidelity.isPreserveTextFormatting());
        assertFalse(highFidelity.isIncludeComments()); // Default is false, not changed in highFidelity

        // Test basic preset (basic() calls createMinimal())
        WordConversionOptions basic = WordConversionOptions.basic();
        assertFalse(basic.isExtractImages());
        assertFalse(basic.isIncludeImages()); // createMinimal sets this to false
        assertFalse(basic.isConvertFootnotes());
        assertTrue(basic.isPreserveHyperlinks()); // Default is true, not changed in createMinimal
        assertFalse(basic.isIncludeComments());
        assertFalse(basic.isPreserveFormatting()); // createMinimal sets this to false

        // Test that presets are independent
        highFidelity.setExtractImages(true);
        WordConversionOptions newHighFidelity = WordConversionOptions.highFidelity();
        assertFalse(newHighFidelity.isExtractImages()); // Should still be false (default)
    }

    @Test
    void testComplexDocumentConversion() throws IOException {
        // Create a complex document with various elements
        File complexDoc = tempDir.resolve("complex.docx").toFile();
        createComplexDocxFile(complexDoc);

        // Test conversion with high fidelity options
        WordConversionOptions options = WordConversionOptions.highFidelity();
        String markdown = WordToMarkdownConverter.convert(complexDoc, options);

        assertNotNull(markdown);
        assertFalse(markdown.trim().isEmpty());

        // Should contain various markdown elements
        assertTrue(markdown.contains("#") || markdown.contains("Main Section")); // Headers
        assertTrue(markdown.contains("**") || markdown.contains("bold")); // Bold text
        assertTrue(markdown.contains("*") || markdown.contains("italic")); // Italic text
        // Note: Lists and tables may not be converted to markdown format in all cases
        assertTrue(markdown.length() > 100); // Should have substantial content
    }

    @Test
    void testTableConversion() throws IOException {
        // Create document with tables
        File tableDoc = tempDir.resolve("table.docx").toFile();
        createDocxWithTable(tableDoc);

        // Test with table formatting enabled
        WordConversionOptions options = new WordConversionOptions()
                .setPreserveTableFormatting(true);
        String markdown = WordToMarkdownConverter.convert(tableDoc, options);

        assertNotNull(markdown);
        assertTrue(markdown.contains("|")); // Table pipes
        assertTrue(markdown.contains("Header 1"));
        assertTrue(markdown.contains("Cell 1"));

        // Test with table formatting disabled
        options.setPreserveTableFormatting(false);
        String markdownNoTable = WordToMarkdownConverter.convert(tableDoc, options);

        assertNotNull(markdownNoTable);
        // Should still contain the text content
        assertTrue(markdownNoTable.contains("Header 1"));
        assertTrue(markdownNoTable.contains("Cell 1"));
    }

    @Test
    void testListConversion() throws IOException {
        // Create document with lists
        File listDoc = tempDir.resolve("list.docx").toFile();
        createDocxWithLists(listDoc);

        // Test with list formatting enabled
        WordConversionOptions options = new WordConversionOptions()
                .setPreserveListFormatting(true);
        String markdown = WordToMarkdownConverter.convert(listDoc, options);

        assertNotNull(markdown);
        assertTrue(markdown.contains("-") || markdown.contains("*") || markdown.contains("1.")); // List markers

        // Test with list formatting disabled
        options.setPreserveListFormatting(false);
        String markdownNoList = WordToMarkdownConverter.convert(listDoc, options);

        assertNotNull(markdownNoList);
        // Should still contain the text content
        assertTrue(markdownNoList.contains("First item"));
        assertTrue(markdownNoList.contains("Second item"));
    }

    @Test
    void testImageExtractionOptions() throws IOException {
        // Create document with embedded images (simulated)
        File imageDoc = tempDir.resolve("image.docx").toFile();
        createDocxWithImages(imageDoc);

        // Test with image extraction enabled
        WordConversionOptions options = new WordConversionOptions()
                .setExtractImages(true)
                .setImageOutputDir("test_images/")
                .setPreferredImageFormat(WordConversionOptions.ImageFormat.PNG)
                .setMaxImageWidth(1024)
                .setMaxImageHeight(768)
                .setGenerateImageAltText(true);

        String markdown = WordToMarkdownConverter.convert(imageDoc, options);

        assertNotNull(markdown);
        // Should contain image references even if no actual images
        assertTrue(markdown.length() > 0);

        // Test with image extraction disabled
        options.setExtractImages(false);
        String markdownNoImages = WordToMarkdownConverter.convert(imageDoc, options);

        assertNotNull(markdownNoImages);
        assertTrue(markdownNoImages.length() > 0);
    }

    @Test
    void testFootnoteConversion() throws IOException {
        // Create document with footnotes
        File footnoteDoc = tempDir.resolve("footnote.docx").toFile();
        createDocxWithFootnotes(footnoteDoc);

        // Test with footnote conversion enabled
        WordConversionOptions options = new WordConversionOptions()
                .setConvertFootnotes(true);
        String markdown = WordToMarkdownConverter.convert(footnoteDoc, options);

        assertNotNull(markdown);
        assertTrue(markdown.contains("footnote") || markdown.contains("reference"));

        // Test with footnote conversion disabled
        options.setConvertFootnotes(false);
        String markdownNoFootnotes = WordToMarkdownConverter.convert(footnoteDoc, options);

        assertNotNull(markdownNoFootnotes);
        assertTrue(markdownNoFootnotes.length() > 0);
    }

    @Test
    void testHyperlinkPreservation() throws IOException {
        // Create document with hyperlinks
        File hyperlinkDoc = tempDir.resolve("hyperlink.docx").toFile();
        createDocxWithHyperlinks(hyperlinkDoc);

        // Test with hyperlink preservation enabled
        WordConversionOptions options = new WordConversionOptions()
                .setPreserveHyperlinks(true);
        String markdown = WordToMarkdownConverter.convert(hyperlinkDoc, options);

        assertNotNull(markdown);
        assertTrue(markdown.contains("http") || markdown.contains("[") || markdown.contains("]"));

        // Test with hyperlink preservation disabled
        options.setPreserveHyperlinks(false);
        String markdownNoLinks = WordToMarkdownConverter.convert(hyperlinkDoc, options);

        assertNotNull(markdownNoLinks);
        assertTrue(markdownNoLinks.length() > 0);
    }

    @Test
    void testTextFormattingPreservation() throws IOException {
        // Create document with various text formatting
        File formattedDoc = tempDir.resolve("formatted.docx").toFile();
        createDocxWithFormatting(formattedDoc);

        // Test with text formatting preservation enabled
        WordConversionOptions options = new WordConversionOptions()
                .setPreserveTextFormatting(true);
        String markdown = WordToMarkdownConverter.convert(formattedDoc, options);

        assertNotNull(markdown);
        assertTrue(markdown.contains("**") || markdown.contains("*") || markdown.contains("`"));

        // Test with text formatting preservation disabled
        options.setPreserveTextFormatting(false);
        String markdownNoFormatting = WordToMarkdownConverter.convert(formattedDoc, options);

        assertNotNull(markdownNoFormatting);
        // Should contain plain text without markdown formatting
        assertTrue(markdownNoFormatting.length() > 0);
    }

    @Test
    void testEmptyParagraphHandling() throws IOException {
        // Create document with empty paragraphs
        File emptyParaDoc = tempDir.resolve("empty_para.docx").toFile();
        createDocxWithEmptyParagraphs(emptyParaDoc);

        // Test with skip empty paragraphs enabled
        WordConversionOptions options = new WordConversionOptions()
                .setSkipEmptyParagraphs(true);
        String markdown = WordToMarkdownConverter.convert(emptyParaDoc, options);

        assertNotNull(markdown);
        // Should have fewer newlines
        int newlineCount = markdown.split("\n").length;

        // Test with skip empty paragraphs disabled
        options.setSkipEmptyParagraphs(false);
        String markdownWithEmpty = WordToMarkdownConverter.convert(emptyParaDoc, options);

        assertNotNull(markdownWithEmpty);
        int newlineCountWithEmpty = markdownWithEmpty.split("\n").length;

        // Should have more newlines when empty paragraphs are preserved
        assertTrue(newlineCountWithEmpty >= newlineCount);
    }

    @Test
    void testPageBreakHandling() throws IOException {
        // Create document with page breaks
        File pageBreakDoc = tempDir.resolve("page_break.docx").toFile();
        createDocxWithPageBreaks(pageBreakDoc);

        // Test with page break inclusion enabled
        WordConversionOptions options = new WordConversionOptions()
                .setIncludePageBreaks(true);
        String markdown = WordToMarkdownConverter.convert(pageBreakDoc, options);

        assertNotNull(markdown);
        assertTrue(markdown.contains("---") || markdown.contains("page") || markdown.length() > 0);

        // Test with page break inclusion disabled
        options.setIncludePageBreaks(false);
        String markdownNoBreaks = WordToMarkdownConverter.convert(pageBreakDoc, options);

        assertNotNull(markdownNoBreaks);
        assertTrue(markdownNoBreaks.length() > 0);
    }

    @Test
    void testCommentHandling() throws IOException {
        // Create document with comments
        File commentDoc = tempDir.resolve("comment.docx").toFile();
        createDocxWithComments(commentDoc);

        // Test with comment inclusion enabled
        WordConversionOptions options = new WordConversionOptions()
                .setIncludeComments(true);
        String markdown = WordToMarkdownConverter.convert(commentDoc, options);

        assertNotNull(markdown);
        assertTrue(markdown.length() > 0);

        // Test with comment inclusion disabled
        options.setIncludeComments(false);
        String markdownNoComments = WordToMarkdownConverter.convert(commentDoc, options);

        assertNotNull(markdownNoComments);
        assertTrue(markdownNoComments.length() > 0);
    }

    @Test
    void testHeaderFooterConversion() throws IOException {
        // Create document with headers and footers
        File headerFooterDoc = tempDir.resolve("header_footer.docx").toFile();
        createDocxWithHeaderFooter(headerFooterDoc);

        // Test with header/footer conversion enabled
        WordConversionOptions options = new WordConversionOptions()
                .setConvertHeadersFooters(true);
        String markdown = WordToMarkdownConverter.convert(headerFooterDoc, options);

        assertNotNull(markdown);
        assertTrue(markdown.length() > 0);

        // Test with header/footer conversion disabled
        options.setConvertHeadersFooters(false);
        String markdownNoHeaderFooter = WordToMarkdownConverter.convert(headerFooterDoc, options);

        assertNotNull(markdownNoHeaderFooter);
        assertTrue(markdownNoHeaderFooter.length() > 0);
    }

    @Test
    void testCorruptedDocumentHandling() throws IOException {
        // Create a corrupted DOCX file
        File corruptedDoc = tempDir.resolve("corrupted.docx").toFile();
        Files.write(corruptedDoc.toPath(), "This is not a valid DOCX file".getBytes());

        // Test strict mode - should throw exception
        assertThrows(IOException.class, () -> {
            WordToMarkdownConverter.convert(corruptedDoc, WordConversionMode.STRICT);
        });

        // Test loose mode - should also throw IOException for invalid format
        assertThrows(IOException.class, () -> {
            WordToMarkdownConverter.convert(corruptedDoc, WordConversionMode.LOOSE);
        });
    }

    @Test
    void testLargeDocumentHandling() throws IOException {
        // Create a large document
        File largeDoc = tempDir.resolve("large.docx").toFile();
        createLargeDocxFile(largeDoc);

        // Test conversion with basic options for performance
        WordConversionOptions options = WordConversionOptions.basic();
        String markdown = WordToMarkdownConverter.convert(largeDoc, options);

        assertNotNull(markdown);
        assertTrue(markdown.length() > 0);

        // Test with high fidelity options
        WordConversionOptions highFidelity = WordConversionOptions.highFidelity();
        String markdownHighFidelity = WordToMarkdownConverter.convert(largeDoc, highFidelity);

        assertNotNull(markdownHighFidelity);
        assertTrue(markdownHighFidelity.length() > 0);
    }

    @Test
    void testConcurrentConversion() throws InterruptedException {
        // Test thread safety
        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        boolean[] results = new boolean[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            executor.submit(() -> {
                try {
                    String markdown = WordToMarkdownConverter.convert(testDocxFile);
                    results[index] = markdown != null && !markdown.trim().isEmpty();
                } catch (Exception e) {
                    results[index] = false;
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(30, TimeUnit.SECONDS));
        executor.shutdown();

        // All conversions should succeed
        for (boolean result : results) {
            assertTrue(result);
        }
    }

    @Test
    void testInputStreamConversion() throws IOException {
        // Test conversion from InputStream
        try (InputStream inputStream = Files.newInputStream(testDocxFile.toPath())) {
            String markdown = WordToMarkdownConverter.convert(inputStream, testDocxFile,
                                                            WordConversionMode.LOOSE,
                                                            new WordConversionOptions());

            assertNotNull(markdown);
            assertFalse(markdown.trim().isEmpty());
            assertTrue(markdown.contains("Test Heading"));
        }

        // Test with null InputStream
        assertThrows(IllegalArgumentException.class, () -> {
            WordToMarkdownConverter.convert((InputStream) null, testDocxFile,
                                          WordConversionMode.LOOSE,
                                          new WordConversionOptions());
        });
    }

    @Test
    void testFileExtensionValidation() {
        // Test supported extensions
        File docFile = new File("test.doc");
        File docxFile = new File("test.docx");

        // These should not throw exceptions for extension validation
        // (actual file existence will be checked separately)
        assertDoesNotThrow(() -> {
            try {
                WordToMarkdownConverter.convert(docFile);
            } catch (IllegalArgumentException e) {
                // Should be about file not existing, not extension
                assertTrue(e.getMessage().contains("exist") || e.getMessage().contains("found"));
            }
        });

        assertDoesNotThrow(() -> {
            try {
                WordToMarkdownConverter.convert(docxFile);
            } catch (IllegalArgumentException e) {
                // Should be about file not existing, not extension
                assertTrue(e.getMessage().contains("exist") || e.getMessage().contains("found"));
            }
        });
    }

    @Test
    void testConfigurationToString() {
        WordConversionOptions options = new WordConversionOptions()
                .setExtractImages(true)
                .setImageOutputDir("custom/")
                .setPreserveTableFormatting(false);

        String toString = options.toString();
        assertNotNull(toString);
        // Just check that toString returns a non-empty string (default Object.toString implementation)
        assertFalse(toString.isEmpty());
        assertTrue(toString.contains("WordConversionOptions") || toString.contains("@"));
    }

    @Test
    void testConfigurationCopyConstructor() {
        // Skip this test since WordConversionOptions doesn't have a copy constructor
        // This test would need to be implemented if copy constructor is added to WordConversionOptions
        assertTrue(true, "Copy constructor test skipped - not implemented in WordConversionOptions");
    }

    @Test
    void testBoundaryValues() {
        WordConversionOptions options = new WordConversionOptions();

        // Test extreme image dimensions
        options.setMaxImageWidth(Integer.MAX_VALUE);
        assertEquals(Integer.MAX_VALUE, options.getMaxImageWidth());

        options.setMaxImageHeight(Integer.MAX_VALUE);
        assertEquals(Integer.MAX_VALUE, options.getMaxImageHeight());

        options.setMaxImageWidth(0);
        assertEquals(0, options.getMaxImageWidth()); // No clamping implemented

        options.setMaxImageHeight(0);
        assertEquals(0, options.getMaxImageHeight()); // No clamping implemented

        // Test negative values
        options.setMaxImageWidth(-100);
        assertEquals(-100, options.getMaxImageWidth()); // No clamping implemented

        options.setMaxImageHeight(-100);
        assertEquals(-100, options.getMaxImageHeight()); // No clamping implemented
    }

    @Test
    void testImageOutputDirectoryValidation() {
        WordConversionOptions options = new WordConversionOptions();

        // Test various directory formats
        options.setImageOutputDir("images/");
        assertEquals("images/", options.getImageOutputDir());

        options.setImageOutputDir("custom");
        assertEquals("custom", options.getImageOutputDir());

        options.setImageOutputDir("/absolute/path/");
        assertEquals("/absolute/path/", options.getImageOutputDir());

        options.setImageOutputDir("");
        assertEquals("", options.getImageOutputDir());

        options.setImageOutputDir(null);
        assertEquals(null, options.getImageOutputDir()); // No null validation implemented
    }

    @Test
    void testSpecialCharacterHandling() throws IOException {
        // Create document with special characters
        File specialCharDoc = tempDir.resolve("special_chars.docx").toFile();
        createDocxWithSpecialCharacters(specialCharDoc);

        String markdown = WordToMarkdownConverter.convert(specialCharDoc);

        assertNotNull(markdown);
        assertTrue(markdown.length() > 0);
        // Should handle special characters gracefully
        assertTrue(markdown.contains("special") || markdown.contains("character") || markdown.length() > 10);
    }

    @Test
    void testMemoryEfficiency() throws IOException {
        // Test that conversion doesn't cause memory leaks
        for (int i = 0; i < 10; i++) {
            String markdown = WordToMarkdownConverter.convert(testDocxFile);
            assertNotNull(markdown);
            assertFalse(markdown.trim().isEmpty());
        }

        // Force garbage collection
        System.gc();
        Thread.yield();

        // Should still work after multiple conversions
        String finalMarkdown = WordToMarkdownConverter.convert(testDocxFile);
        assertNotNull(finalMarkdown);
        assertFalse(finalMarkdown.trim().isEmpty());
    }

    @Test
    void testErrorMessageQuality() {
        // Test null file error message
        Exception nullException = assertThrows(IllegalArgumentException.class, () -> {
            WordToMarkdownConverter.convert((File) null);
        });
        assertTrue(nullException.getMessage().contains("null") ||
                  nullException.getMessage().contains("file"));

        // Test non-existent file error message
        File nonExistent = new File("definitely_does_not_exist.docx");
        Exception notFoundException = assertThrows(IllegalArgumentException.class, () -> {
            WordToMarkdownConverter.convert(nonExistent);
        });
        assertTrue(notFoundException.getMessage().contains("exist") ||
                  notFoundException.getMessage().contains("found") ||
                  notFoundException.getMessage().contains("does not exist"));
    }

    // ========== Helper Methods for Creating Test Documents ==========

    /**
     * Creates a complex DOCX file with various elements
     */
    private void createComplexDocxFile(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            title.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Complex Document Test");
            titleRun.setBold(true);
            titleRun.setFontSize(20);

            // Add heading 1
            XWPFParagraph h1 = document.createParagraph();
            h1.setStyle("Heading1");
            XWPFRun h1Run = h1.createRun();
            h1Run.setText("Main Section");
            h1Run.setBold(true);
            h1Run.setFontSize(16);

            // Add paragraph with mixed formatting
            XWPFParagraph para = document.createParagraph();
            XWPFRun run1 = para.createRun();
            run1.setText("This paragraph contains ");

            XWPFRun run2 = para.createRun();
            run2.setText("bold text");
            run2.setBold(true);

            XWPFRun run3 = para.createRun();
            run3.setText(", ");

            XWPFRun run4 = para.createRun();
            run4.setText("italic text");
            run4.setItalic(true);

            XWPFRun run5 = para.createRun();
            run5.setText(", and ");

            XWPFRun run6 = para.createRun();
            run6.setText("underlined text");
            run6.setUnderline(UnderlinePatterns.SINGLE);

            XWPFRun run7 = para.createRun();
            run7.setText(".");

            // Add heading 2
            XWPFParagraph h2 = document.createParagraph();
            h2.setStyle("Heading2");
            XWPFRun h2Run = h2.createRun();
            h2Run.setText("Subsection");
            h2Run.setBold(true);
            h2Run.setFontSize(14);

            // Add bullet list
            XWPFParagraph listItem1 = document.createParagraph();
            listItem1.setNumID(BigInteger.valueOf(1));
            XWPFRun listRun1 = listItem1.createRun();
            listRun1.setText("First bullet point");

            XWPFParagraph listItem2 = document.createParagraph();
            listItem2.setNumID(BigInteger.valueOf(1));
            XWPFRun listRun2 = listItem2.createRun();
            listRun2.setText("Second bullet point");

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with tables
     */
    private void createDocxWithTable(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Table");
            titleRun.setBold(true);

            // Create table
            XWPFTable table = document.createTable(3, 3);

            // Header row
            XWPFTableRow headerRow = table.getRow(0);
            headerRow.getCell(0).setText("Header 1");
            headerRow.getCell(1).setText("Header 2");
            headerRow.getCell(2).setText("Header 3");

            // Data rows
            XWPFTableRow row1 = table.getRow(1);
            row1.getCell(0).setText("Cell 1");
            row1.getCell(1).setText("Cell 2");
            row1.getCell(2).setText("Cell 3");

            XWPFTableRow row2 = table.getRow(2);
            row2.getCell(0).setText("Data 1");
            row2.getCell(1).setText("Data 2");
            row2.getCell(2).setText("Data 3");

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with lists
     */
    private void createDocxWithLists(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Lists");
            titleRun.setBold(true);

            // Add unordered list
            XWPFParagraph listTitle = document.createParagraph();
            XWPFRun listTitleRun = listTitle.createRun();
            listTitleRun.setText("Unordered List:");
            listTitleRun.setBold(true);

            XWPFParagraph item1 = document.createParagraph();
            XWPFRun item1Run = item1.createRun();
            item1Run.setText("First item");

            XWPFParagraph item2 = document.createParagraph();
            XWPFRun item2Run = item2.createRun();
            item2Run.setText("Second item");

            XWPFParagraph item3 = document.createParagraph();
            XWPFRun item3Run = item3.createRun();
            item3Run.setText("Third item");

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with images (simulated)
     */
    private void createDocxWithImages(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Images");
            titleRun.setBold(true);

            // Add paragraph describing images
            XWPFParagraph para = document.createParagraph();
            XWPFRun run = para.createRun();
            run.setText("This document would contain images if they were embedded.");

            // Note: Actually embedding images requires more complex setup
            // For testing purposes, we just create a document that mentions images

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with footnotes
     */
    private void createDocxWithFootnotes(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Footnotes");
            titleRun.setBold(true);

            // Add paragraph with footnote reference
            XWPFParagraph para = document.createParagraph();
            XWPFRun run1 = para.createRun();
            run1.setText("This text has a footnote reference");

            XWPFRun run2 = para.createRun();
            run2.setText(" [1]");
            run2.setVerticalAlignment("superscript");

            XWPFRun run3 = para.createRun();
            run3.setText(" in the middle of the sentence.");

            // Add footnote text (simplified)
            XWPFParagraph footnote = document.createParagraph();
            XWPFRun footnoteRun = footnote.createRun();
            footnoteRun.setText("[1] This is the footnote text.");
            footnoteRun.setFontSize(10);

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with hyperlinks
     */
    private void createDocxWithHyperlinks(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Hyperlinks");
            titleRun.setBold(true);

            // Add paragraph with hyperlink
            XWPFParagraph para = document.createParagraph();
            XWPFRun run1 = para.createRun();
            run1.setText("Visit ");

            // Create hyperlink (simplified - actual hyperlink creation is more complex)
            XWPFRun linkRun = para.createRun();
            linkRun.setText("https://example.com");
            linkRun.setColor("0000FF");
            linkRun.setUnderline(UnderlinePatterns.SINGLE);

            XWPFRun run2 = para.createRun();
            run2.setText(" for more information.");

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with various text formatting
     */
    private void createDocxWithFormatting(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Text Formatting");
            titleRun.setBold(true);

            // Add paragraph with various formatting
            XWPFParagraph para = document.createParagraph();

            XWPFRun normalRun = para.createRun();
            normalRun.setText("Normal text, ");

            XWPFRun boldRun = para.createRun();
            boldRun.setText("bold text");
            boldRun.setBold(true);

            XWPFRun commaRun1 = para.createRun();
            commaRun1.setText(", ");

            XWPFRun italicRun = para.createRun();
            italicRun.setText("italic text");
            italicRun.setItalic(true);

            XWPFRun commaRun2 = para.createRun();
            commaRun2.setText(", ");

            XWPFRun underlineRun = para.createRun();
            underlineRun.setText("underlined text");
            underlineRun.setUnderline(UnderlinePatterns.SINGLE);

            XWPFRun commaRun3 = para.createRun();
            commaRun3.setText(", ");

            XWPFRun strikeRun = para.createRun();
            strikeRun.setText("strikethrough text");
            strikeRun.setStrikeThrough(true);

            XWPFRun commaRun4 = para.createRun();
            commaRun4.setText(", and ");

            XWPFRun codeRun = para.createRun();
            codeRun.setText("code text");
            codeRun.setFontFamily("Courier New");

            XWPFRun periodRun = para.createRun();
            periodRun.setText(".");

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with empty paragraphs
     */
    private void createDocxWithEmptyParagraphs(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Empty Paragraphs");
            titleRun.setBold(true);

            // Add normal paragraph
            XWPFParagraph para1 = document.createParagraph();
            XWPFRun run1 = para1.createRun();
            run1.setText("First paragraph with content.");

            // Add empty paragraphs
            document.createParagraph(); // Empty
            document.createParagraph(); // Empty
            document.createParagraph(); // Empty

            // Add another paragraph with content
            XWPFParagraph para2 = document.createParagraph();
            XWPFRun run2 = para2.createRun();
            run2.setText("Second paragraph with content after empty paragraphs.");

            // Add more empty paragraphs
            document.createParagraph(); // Empty
            document.createParagraph(); // Empty

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with page breaks
     */
    private void createDocxWithPageBreaks(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Page Breaks");
            titleRun.setBold(true);

            // Add content on first page
            XWPFParagraph para1 = document.createParagraph();
            XWPFRun run1 = para1.createRun();
            run1.setText("Content on the first page.");

            // Add page break
            XWPFParagraph pageBreak = document.createParagraph();
            XWPFRun breakRun = pageBreak.createRun();
            breakRun.addBreak(BreakType.PAGE);

            // Add content on second page
            XWPFParagraph para2 = document.createParagraph();
            XWPFRun run2 = para2.createRun();
            run2.setText("Content on the second page after page break.");

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with comments (simulated)
     */
    private void createDocxWithComments(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Comments");
            titleRun.setBold(true);

            // Add paragraph with comment reference
            XWPFParagraph para = document.createParagraph();
            XWPFRun run1 = para.createRun();
            run1.setText("This text has a comment");

            // Simulate comment marker
            XWPFRun commentRun = para.createRun();
            commentRun.setText(" [Comment: This is a comment]");
            commentRun.setColor("FF0000");
            commentRun.setFontSize(10);

            XWPFRun run2 = para.createRun();
            run2.setText(" attached to it.");

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with headers and footers
     */
    private void createDocxWithHeaderFooter(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Simulate header content in main document (simplified)
            XWPFParagraph headerSim = document.createParagraph();
            headerSim.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun headerRun = headerSim.createRun();
            headerRun.setText("--- Document Header ---");
            headerRun.setBold(true);
            headerRun.setFontSize(10);

            // Add main content
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Header and Footer");
            titleRun.setBold(true);

            XWPFParagraph content = document.createParagraph();
            XWPFRun contentRun = content.createRun();
            contentRun.setText("This document has both header and footer content.");

            // Simulate footer content
            XWPFParagraph footerSim = document.createParagraph();
            footerSim.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun footerRun = footerSim.createRun();
            footerRun.setText("--- Document Footer - Page 1 ---");
            footerRun.setFontSize(10);

            document.write(out);
        }
    }

    /**
     * Creates a large DOCX file for performance testing
     */
    private void createLargeDocxFile(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Large Document for Performance Testing");
            titleRun.setBold(true);
            titleRun.setFontSize(16);

            // Add many paragraphs
            for (int i = 1; i <= 100; i++) {
                XWPFParagraph para = document.createParagraph();
                XWPFRun run = para.createRun();
                run.setText("This is paragraph number " + i + ". It contains some sample text to make the document larger. " +
                           "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.");

                // Add some formatting variety
                if (i % 10 == 0) {
                    run.setBold(true);
                }
                if (i % 15 == 0) {
                    run.setItalic(true);
                }
            }

            // Add a table
            XWPFTable table = document.createTable(10, 5);
            for (int row = 0; row < 10; row++) {
                for (int col = 0; col < 5; col++) {
                    table.getRow(row).getCell(col).setText("Row " + row + ", Col " + col);
                }
            }

            document.write(out);
        }
    }

    /**
     * Creates a DOCX file with special characters
     */
    private void createDocxWithSpecialCharacters(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(file)) {

            // Add title
            XWPFParagraph title = document.createParagraph();
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Document with Special Characters");
            titleRun.setBold(true);

            // Add paragraph with special characters
            XWPFParagraph para = document.createParagraph();
            XWPFRun run = para.createRun();
            run.setText("Special characters: áéíóú àèìòù âêîôû ãñõ çß €£¥ ©®™ •‰ quotes dashes ellipsis");

            // Add paragraph with symbols
            XWPFParagraph symbols = document.createParagraph();
            XWPFRun symbolRun = symbols.createRun();
            symbolRun.setText("Mathematical symbols: ∑∏∫√∞≈≠≤≥±×÷ αβγδε λμπσω");

            // Add paragraph with emojis (if supported)
            XWPFParagraph emoji = document.createParagraph();
            XWPFRun emojiRun = emoji.createRun();
            emojiRun.setText("Unicode characters: ★☆♠♣♥♦ ☀☁☂☃ ✓✗✉✈");

            document.write(out);
        }
    }
}
