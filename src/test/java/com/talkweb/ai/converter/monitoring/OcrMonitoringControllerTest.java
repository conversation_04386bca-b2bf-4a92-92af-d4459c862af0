package com.talkweb.ai.converter.monitoring;

import com.talkweb.ai.converter.logging.OcrLogger;
import com.talkweb.ai.converter.metrics.OcrMetrics;
import com.talkweb.ai.converter.service.OcrCacheManager;
import com.talkweb.ai.converter.service.OcrService;
import com.talkweb.ai.converter.service.OcrThreadPoolManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * OCR监控控制器测试类
 */
@ExtendWith(MockitoExtension.class)
class OcrMonitoringControllerTest {

    @Mock
    private OcrService ocrService;
    
    @Mock
    private OcrMetrics ocrMetrics;
    
    @Mock
    private OcrLogger ocrLogger;

    private OcrMonitoringController controller;

    @BeforeEach
    void setUp() {
        controller = new OcrMonitoringController();
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field serviceField = OcrMonitoringController.class.getDeclaredField("ocrService");
            serviceField.setAccessible(true);
            serviceField.set(controller, ocrService);

            java.lang.reflect.Field metricsField = OcrMonitoringController.class.getDeclaredField("ocrMetrics");
            metricsField.setAccessible(true);
            metricsField.set(controller, ocrMetrics);

            java.lang.reflect.Field loggerField = OcrMonitoringController.class.getDeclaredField("ocrLogger");
            loggerField.setAccessible(true);
            loggerField.set(controller, ocrLogger);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set up test", e);
        }
    }

    @Test
    void testGetSystemStatus_WhenServiceAvailable() {
        // Given
        when(ocrService.isAvailable()).thenReturn(true);
        when(ocrLogger.getActiveContextCount()).thenReturn(3);
        
        OcrMetrics.MetricsSummary mockMetrics = createMockMetricsSummary();
        when(ocrMetrics.getMetricsSummary()).thenReturn(mockMetrics);
        
        OcrThreadPoolManager.ThreadPoolStats mockThreadPoolStats = createMockThreadPoolStats();
        when(ocrService.getThreadPoolStats()).thenReturn(mockThreadPoolStats);
        
        OcrCacheManager.CacheStats mockCacheStats = createMockCacheStats();
        when(ocrService.getCacheStats()).thenReturn(mockCacheStats);

        // When
        ResponseEntity<Map<String, Object>> response = controller.getSystemStatus();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertTrue((Boolean) body.get("available"));
        assertEquals(3, body.get("activeContexts"));
        assertNotNull(body.get("metrics"));
        assertNotNull(body.get("threadPool"));
        assertNotNull(body.get("cache"));
        assertEquals("HEALTHY", body.get("health"));
    }

    @Test
    void testGetSystemStatus_WhenServiceUnavailable() {
        // Given
        when(ocrService.isAvailable()).thenReturn(false);
        when(ocrLogger.getActiveContextCount()).thenReturn(0);
        
        OcrMetrics.MetricsSummary mockMetrics = createMockMetricsSummary();
        when(ocrMetrics.getMetricsSummary()).thenReturn(mockMetrics);

        // When
        ResponseEntity<Map<String, Object>> response = controller.getSystemStatus();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertFalse((Boolean) body.get("available"));
        assertEquals("DOWN", body.get("health"));
    }

    @Test
    void testGetMetrics() {
        // Given
        OcrMetrics.MetricsSummary mockMetrics = createMockMetricsSummary();
        when(ocrMetrics.getMetricsSummary()).thenReturn(mockMetrics);

        // When
        ResponseEntity<OcrMetrics.MetricsSummary> response = controller.getMetrics();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(mockMetrics, response.getBody());
    }

    @Test
    void testGetRealTimeStats() {
        // Given
        when(ocrMetrics.getSuccessRate()).thenReturn(0.95);
        when(ocrMetrics.getErrorRate()).thenReturn(0.05);
        when(ocrMetrics.getCacheHitRate()).thenReturn(0.8);
        when(ocrMetrics.getThroughput()).thenReturn(10.5);
        when(ocrLogger.getActiveContextCount()).thenReturn(2);
        when(ocrService.isAvailable()).thenReturn(true);

        // Create minimal mocks - only stub methods that are actually called
        OcrThreadPoolManager.ThreadPoolStats mockThreadPoolStats = mock(OcrThreadPoolManager.ThreadPoolStats.class);
        when(mockThreadPoolStats.getActiveThreads()).thenReturn(3);
        when(mockThreadPoolStats.getQueueSize()).thenReturn(5);
        when(ocrService.getThreadPoolStats()).thenReturn(mockThreadPoolStats);

        // Cache stats are retrieved but not used in getRealTimeStats, so just return a mock
        OcrCacheManager.CacheStats mockCacheStats = mock(OcrCacheManager.CacheStats.class);
        when(ocrService.getCacheStats()).thenReturn(mockCacheStats);

        // When
        ResponseEntity<Map<String, Object>> response = controller.getRealTimeStats();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());

        Map<String, Object> body = response.getBody();
        assertEquals(0.95, body.get("successRate"));
        assertEquals(0.05, body.get("errorRate"));
        assertEquals(0.8, body.get("cacheHitRate"));
        assertEquals(10.5, body.get("throughput"));
        assertEquals(2, body.get("activeProcessing"));
        assertTrue((Boolean) body.get("systemAvailable"));

        // Verify the actual methods called by the controller
        assertEquals(3, body.get("activeThreads"));
        assertEquals(5, body.get("queueSize"));

        // Verify that the mocked stats are used
        verify(ocrService).getThreadPoolStats();
        verify(ocrService).getCacheStats();
        verify(mockThreadPoolStats).getActiveThreads();
        verify(mockThreadPoolStats).getQueueSize();
    }

    @Test
    void testHealthCheck_Healthy() {
        // Given
        when(ocrService.isAvailable()).thenReturn(true);

        OcrMetrics.MetricsSummary mockMetrics = createMockMetricsSummary();
        when(ocrMetrics.getMetricsSummary()).thenReturn(mockMetrics);

        // Create minimal mock - only stub methods that are actually called
        OcrThreadPoolManager.ThreadPoolStats mockThreadPoolStats = mock(OcrThreadPoolManager.ThreadPoolStats.class);
        when(mockThreadPoolStats.getActiveThreads()).thenReturn(3); // Only this method is called in healthCheck
        when(ocrService.getThreadPoolStats()).thenReturn(mockThreadPoolStats);

        // When
        ResponseEntity<Map<String, Object>> response = controller.healthCheck();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());

        Map<String, Object> body = response.getBody();
        assertEquals("UP", body.get("status"));
        assertEquals("HEALTHY", body.get("overall"));
        assertNotNull(body.get("checks"));

        // Verify that the mocked stats are used
        verify(ocrService).getThreadPoolStats();
        verify(ocrMetrics).getMetricsSummary();
        verify(mockThreadPoolStats).getActiveThreads(); // Only this method is called in healthCheck
    }

    @Test
    void testHealthCheck_Unhealthy() {
        // Given
        when(ocrService.isAvailable()).thenReturn(false);

        // When
        ResponseEntity<Map<String, Object>> response = controller.healthCheck();

        // Then
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertEquals("DOWN", body.get("status"));
        assertEquals("UNHEALTHY", body.get("overall"));
    }

    @Test
    void testGetConfiguration() {
        // Given
        when(ocrService.isAvailable()).thenReturn(true);
        Map<String, Object> mockConfig = Map.of(
            "languages", "eng+chi_sim",
            "pageSegmentationMode", 6,
            "ocrEngineMode", 3
        );
        when(ocrService.getConfigInfo()).thenReturn(mockConfig);

        // When
        ResponseEntity<Map<String, Object>> response = controller.getConfiguration();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(mockConfig, response.getBody());
    }

    @Test
    void testResetMetrics() {
        // Given
        doNothing().when(ocrService).clearPerformanceMetrics();

        // When
        ResponseEntity<Map<String, String>> response = controller.resetMetrics();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().get("message").contains("reset successfully"));
        verify(ocrService).clearPerformanceMetrics();
    }

    @Test
    void testClearCache() {
        // Given
        doNothing().when(ocrService).clearCache();

        // When
        ResponseEntity<Map<String, String>> response = controller.clearCache();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().get("message").contains("cleared successfully"));
        verify(ocrService).clearCache();
    }

    @Test
    void testGetPerformanceReport() {
        // Given
        String mockReport = "Performance Report:\nTotal processed: 100\nSuccess rate: 95%";
        when(ocrService.generatePerformanceReport()).thenReturn(mockReport);
        
        OcrMetrics.MetricsSummary mockMetrics = createMockMetricsSummary();
        when(ocrMetrics.getMetricsSummary()).thenReturn(mockMetrics);

        // When
        ResponseEntity<Map<String, Object>> response = controller.getPerformanceReport();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertEquals(mockReport, body.get("report"));
        assertNotNull(body.get("timestamp"));
        assertEquals(mockMetrics, body.get("metrics"));
    }

    @Test
    void testGetSystemStatus_WithException() {
        // Given
        when(ocrService.isAvailable()).thenThrow(new RuntimeException("Service error"));

        // When
        ResponseEntity<Map<String, Object>> response = controller.getSystemStatus();

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertTrue(body.get("error").toString().contains("Service error"));
        assertEquals("ERROR", body.get("health"));
    }

    // Helper methods

    private OcrMetrics.MetricsSummary createMockMetricsSummary() {
        return new OcrMetrics.MetricsSummary(
            100L,    // totalProcessed
            95L,     // totalSuccessful
            5L,      // totalErrors
            0.95,    // successRate
            0.05,    // errorRate
            0.8,     // cacheHitRate
            1500.0,  // avgProcessingTimeMs
            10.5,    // throughput
            3L,      // activeThreads
            5L       // queueSize
        );
    }

    private OcrThreadPoolManager.ThreadPoolStats createMockThreadPoolStats() {
        OcrThreadPoolManager.ThreadPoolStats stats = mock(OcrThreadPoolManager.ThreadPoolStats.class);
        when(stats.getActiveThreads()).thenReturn(3);
        when(stats.getQueueSize()).thenReturn(5);
        return stats;
    }

    private OcrCacheManager.CacheStats createMockCacheStats() {
        OcrCacheManager.CacheStats stats = mock(OcrCacheManager.CacheStats.class);
        when(stats.getMaxSize()).thenReturn(100);
        when(stats.getHitRate()).thenReturn(0.8);
        return stats;
    }
}
