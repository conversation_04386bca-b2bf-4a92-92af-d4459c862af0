package com.talkweb.ai.converter.integration;

import com.talkweb.ai.converter.util.image.TableDetector;
import com.talkweb.ai.converter.util.image.TableExtractor;
import com.talkweb.ai.converter.util.image.TableToMarkdownConverter;
import com.talkweb.ai.converter.util.image.LayoutAnalyzer;
import com.talkweb.ai.converter.util.ai.AiTextPostProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 增强OCR功能集成测试
 * 
 * 测试第二阶段实现的表格识别、布局分析和AI辅助功能的集成效果
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@ExtendWith(SpringExtension.class)
class EnhancedOcrIntegrationTest {

    private TableDetector tableDetector;
    private TableExtractor tableExtractor;
    private TableToMarkdownConverter tableToMarkdownConverter;
    private LayoutAnalyzer layoutAnalyzer;
    private AiTextPostProcessor aiTextPostProcessor;

    @BeforeEach
    void setUp() {
        // 初始化组件
        tableDetector = new TableDetector();
        tableExtractor = new TableExtractor();
        tableToMarkdownConverter = new TableToMarkdownConverter();
        layoutAnalyzer = new LayoutAnalyzer();
        aiTextPostProcessor = new AiTextPostProcessor();
    }

    @Test
    void testTableDetectionAndExtraction_IntegrationFlow() {
        // Given
        BufferedImage testImage = createComplexTableImage();
        
        // When - 表格检测
        TableDetector.TableDetectionResult detectionResult = tableDetector.detectTables(testImage);
        
        // Then - 验证检测结果
        assertNotNull(detectionResult);
        assertTrue(detectionResult.isSuccess());
        
        if (!detectionResult.getTables().isEmpty()) {
            // When - 表格提取
            TableExtractor.TableExtractionResult extractionResult = 
                tableExtractor.extractTables(testImage, detectionResult.getTables());
            
            // Then - 验证提取结果
            assertNotNull(extractionResult);
            
            if (extractionResult.isSuccess() && !extractionResult.getTables().isEmpty()) {
                // When - Markdown转换
                TableToMarkdownConverter.ConversionResult conversionResult = 
                    tableToMarkdownConverter.convertTables(extractionResult.getTables());
                
                // Then - 验证转换结果
                assertNotNull(conversionResult);
                if (conversionResult.isSuccess()) {
                    assertNotNull(conversionResult.getMarkdown());
                    assertFalse(conversionResult.getMarkdown().trim().isEmpty());
                }
            }
        }
    }

    @Test
    void testLayoutAnalysis_ComplexDocument() {
        // Given
        BufferedImage complexDocument = createMultiColumnDocument();
        
        // When
        LayoutAnalyzer.LayoutAnalysisResult result = layoutAnalyzer.analyzeLayout(complexDocument);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getRegions());
        assertNotNull(result.getStructure());
        
        // 验证布局结构
        LayoutAnalyzer.LayoutStructure structure = result.getStructure();
        assertNotNull(structure);
        assertTrue(structure.getColumns() >= 1);
        assertNotNull(structure.getColumnBounds());
    }

    @Test
    void testAiTextPostProcessing_ErrorCorrection() {
        // Given
        String ocrTextWithErrors = "Teh quick brown fox jumps 0ver the lazy d0g. This is a test 0f OCR err0rs.";
        double confidence = 0.75;
        
        // When
        AiTextPostProcessor.AiPostProcessingResult result = 
            aiTextPostProcessor.processText(ocrTextWithErrors, confidence);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getProcessedText());
        assertNotNull(result.getCorrections());
        
        // 验证文本改进
        String processedText = result.getProcessedText();
        assertNotEquals(ocrTextWithErrors, processedText);
        
        // 验证纠正记录
        List<AiTextPostProcessor.TextCorrection> corrections = result.getCorrections();
        assertFalse(corrections.isEmpty());
    }

    @Test
    void testIntegratedWorkflow_CompleteDocument() {
        // Given
        BufferedImage documentImage = createCompleteDocumentImage();
        
        // When - 执行完整的处理流程
        
        // 1. 布局分析
        LayoutAnalyzer.LayoutAnalysisResult layoutResult = layoutAnalyzer.analyzeLayout(documentImage);
        
        // 2. 表格检测
        TableDetector.TableDetectionResult tableResult = tableDetector.detectTables(documentImage);
        
        // 3. 模拟OCR文本
        String mockOcrText = "This is a sample document with some text and tables.";
        
        // 4. AI文本后处理
        AiTextPostProcessor.AiPostProcessingResult aiResult = 
            aiTextPostProcessor.processText(mockOcrText, 0.8);
        
        // Then - 验证整体流程
        assertTrue(layoutResult.isSuccess());
        assertTrue(tableResult.isSuccess());
        assertTrue(aiResult.isSuccess());
        
        // 验证各组件协同工作
        assertNotNull(layoutResult.getRegions());
        assertNotNull(tableResult.getTables());
        assertNotNull(aiResult.getProcessedText());
    }

    @Test
    void testPerformanceMetrics_AllComponents() {
        // Given
        BufferedImage testImage = createPerformanceTestImage();
        long startTime = System.currentTimeMillis();
        
        // When - 测试各组件性能
        TableDetector.TableDetectionResult tableResult = tableDetector.detectTables(testImage);
        long tableDetectionTime = System.currentTimeMillis() - startTime;
        
        startTime = System.currentTimeMillis();
        LayoutAnalyzer.LayoutAnalysisResult layoutResult = layoutAnalyzer.analyzeLayout(testImage);
        long layoutAnalysisTime = System.currentTimeMillis() - startTime;
        
        startTime = System.currentTimeMillis();
        AiTextPostProcessor.AiPostProcessingResult aiResult = 
            aiTextPostProcessor.processText("Sample text for processing", 0.8);
        long aiProcessingTime = System.currentTimeMillis() - startTime;
        
        // Then - 验证性能指标
        assertTrue(tableDetectionTime < 5000, "Table detection should complete within 5 seconds");
        assertTrue(layoutAnalysisTime < 3000, "Layout analysis should complete within 3 seconds");
        assertTrue(aiProcessingTime < 1000, "AI processing should complete within 1 second");
        
        // 验证结果质量
        assertTrue(tableResult.isSuccess());
        assertTrue(layoutResult.isSuccess());
        assertTrue(aiResult.isSuccess());
    }

    // Helper methods for creating test images

    private BufferedImage createComplexTableImage() {
        BufferedImage image = new BufferedImage(400, 300, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 白色背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 400, 300);
        
        // 绘制复杂表格
        g2d.setColor(Color.BLACK);
        g2d.setStroke(new BasicStroke(2));
        
        // 外边框
        g2d.drawRect(50, 50, 300, 200);
        
        // 内部网格
        for (int i = 1; i < 4; i++) {
            g2d.drawLine(50, 50 + i * 50, 350, 50 + i * 50); // 水平线
            g2d.drawLine(50 + i * 75, 50, 50 + i * 75, 250); // 垂直线
        }
        
        g2d.dispose();
        return image;
    }

    private BufferedImage createMultiColumnDocument() {
        BufferedImage image = new BufferedImage(600, 400, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 白色背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 600, 400);
        
        // 模拟多列文档
        g2d.setColor(Color.BLACK);
        
        // 左列文本区域
        g2d.fillRect(50, 50, 200, 300);
        g2d.setColor(Color.WHITE);
        g2d.fillRect(55, 55, 190, 290);
        
        // 右列文本区域
        g2d.setColor(Color.BLACK);
        g2d.fillRect(300, 50, 200, 300);
        g2d.setColor(Color.WHITE);
        g2d.fillRect(305, 55, 190, 290);
        
        g2d.dispose();
        return image;
    }

    private BufferedImage createCompleteDocumentImage() {
        BufferedImage image = new BufferedImage(800, 600, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 白色背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 800, 600);
        
        // 标题区域
        g2d.setColor(Color.GRAY);
        g2d.fillRect(50, 50, 700, 50);
        
        // 文本区域
        g2d.setColor(Color.BLACK);
        for (int i = 0; i < 5; i++) {
            g2d.fillRect(50, 120 + i * 30, 300, 20);
        }
        
        // 表格区域
        g2d.drawRect(400, 120, 300, 150);
        for (int i = 1; i < 4; i++) {
            g2d.drawLine(400, 120 + i * 37, 700, 120 + i * 37);
            g2d.drawLine(400 + i * 75, 120, 400 + i * 75, 270);
        }
        
        g2d.dispose();
        return image;
    }

    private BufferedImage createPerformanceTestImage() {
        BufferedImage image = new BufferedImage(1000, 800, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 白色背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 1000, 800);
        
        // 添加复杂内容用于性能测试
        g2d.setColor(Color.BLACK);
        
        // 多个表格
        for (int t = 0; t < 3; t++) {
            int x = 50 + t * 300;
            int y = 50;
            g2d.drawRect(x, y, 200, 150);
            for (int i = 1; i < 4; i++) {
                g2d.drawLine(x, y + i * 37, x + 200, y + i * 37);
                g2d.drawLine(x + i * 50, y, x + i * 50, y + 150);
            }
        }
        
        // 文本区域
        for (int i = 0; i < 20; i++) {
            g2d.fillRect(50, 250 + i * 25, 900, 15);
        }
        
        g2d.dispose();
        return image;
    }
}
