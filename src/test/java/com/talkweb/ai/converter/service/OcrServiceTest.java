package com.talkweb.ai.converter.service;

import com.talkweb.ai.converter.config.OcrConfiguration;
import com.talkweb.ai.converter.logging.OcrLogger;
import com.talkweb.ai.converter.metrics.OcrMetrics;
import com.talkweb.ai.converter.model.OcrResult;
import com.talkweb.ai.converter.util.PerformanceMonitor;
import io.micrometer.core.instrument.Timer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * OCR服务测试类
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class OcrServiceTest {

    @Mock
    private OcrConfiguration ocrConfig;

    @Mock
    private OcrThreadPoolManager threadPoolManager;

    @Mock
    private OcrCacheManager cacheManager;

    @Mock
    private PerformanceMonitor performanceMonitor;

    @Mock
    private OcrMetrics ocrMetrics;

    @Mock
    private OcrLogger ocrLogger;

    private OcrService ocrService;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        lenient().when(ocrConfig.isEnabled()).thenReturn(true);
        lenient().when(ocrConfig.getLanguageString()).thenReturn("eng+chi_sim");
        lenient().when(ocrConfig.getLanguages()).thenReturn(List.of("eng", "chi_sim"));
        lenient().when(ocrConfig.getPageSegmentationMode()).thenReturn(3);
        lenient().when(ocrConfig.getOcrEngineMode()).thenReturn(3);
        lenient().when(ocrConfig.getConfidenceThreshold()).thenReturn(60);
        lenient().when(ocrConfig.getHighQualityThreshold()).thenReturn(80);
        lenient().when(ocrConfig.getTimeoutSeconds()).thenReturn(30);
        lenient().when(ocrConfig.isPreprocessingEnabled()).thenReturn(true);
        lenient().when(ocrConfig.isVerboseLogging()).thenReturn(false);
        lenient().when(ocrConfig.getCustomVariables()).thenReturn(java.util.Map.of());
        lenient().when(ocrConfig.getDataPath()).thenReturn(null);
        lenient().when(ocrConfig.isAdaptivePageSegmentation()).thenReturn(true);
        lenient().when(ocrConfig.getMaxImageSize()).thenReturn(2048);
        lenient().when(ocrConfig.isImageCacheEnabled()).thenReturn(true);
        lenient().when(ocrConfig.getCacheSize()).thenReturn(100);
        lenient().when(ocrConfig.getThreadPoolSize()).thenReturn(0);

        // Mock thread pool manager
        lenient().when(threadPoolManager.getStats()).thenReturn(
            new OcrThreadPoolManager.ThreadPoolStats(2, 4, 1, 2, 0, 10, 8, 2, 1000)
        );

        // Mock cache manager
        lenient().when(cacheManager.getStats()).thenReturn(
            new OcrCacheManager.CacheStats(5, 100, 20, 5, 0.8, 2, 1024)
        );
        lenient().when(cacheManager.generateCacheKey(any(), any())).thenReturn("test_cache_key");
        lenient().when(cacheManager.get(anyString())).thenReturn(null);

        // Mock OCR metrics
        Timer.Sample mockTimerSample = mock(Timer.Sample.class);
        lenient().when(ocrMetrics.startProcessing()).thenReturn(mockTimerSample);

        // Mock OCR logger
        lenient().when(ocrLogger.startProcessing(any(BufferedImage.class), anyString())).thenReturn("test_request_id");

        ocrService = new OcrService(ocrConfig, threadPoolManager, cacheManager, performanceMonitor, ocrMetrics, ocrLogger);
    }

    @Test
    void testServiceInitializationWhenEnabled() {
        // Given - configuration already set in setUp()

        // When
        OcrService service = new OcrService(ocrConfig, threadPoolManager, cacheManager, performanceMonitor, ocrMetrics, ocrLogger);

        // Then
        assertNotNull(service);
    }

    @Test
    void testServiceInitializationWhenDisabled() {
        // Given
        when(ocrConfig.isEnabled()).thenReturn(false);

        // When
        OcrService service = new OcrService(ocrConfig, threadPoolManager, cacheManager, performanceMonitor, ocrMetrics, ocrLogger);

        // Then
        assertNotNull(service);
        assertFalse(service.isAvailable());
    }

    @Test
    void testRecognizeTextWithNullFile() {
        // When
        OcrResult result = ocrService.recognizeText((File) null);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
        assertTrue(result.getErrorMessage().contains("does not exist"));
    }

    @Test
    void testRecognizeTextWithNonExistentFile() {
        // Given
        File nonExistentFile = new File("non_existent_file.png");

        // When
        OcrResult result = ocrService.recognizeText(nonExistentFile);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
        assertTrue(result.getErrorMessage().contains("does not exist"));
    }

    @Test
    void testRecognizeTextWithNullBufferedImage() {
        // When
        OcrResult result = ocrService.recognizeText((BufferedImage) null);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
        assertTrue(result.getErrorMessage().contains("null"));
    }

    @Test
    void testRecognizeTextWhenServiceDisabled() {
        // Given
        when(ocrConfig.isEnabled()).thenReturn(false);
        OcrService disabledService = new OcrService(ocrConfig, threadPoolManager, cacheManager, performanceMonitor, ocrMetrics, ocrLogger);
        BufferedImage testImage = new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);

        // When
        OcrResult result = disabledService.recognizeText(testImage);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
        assertTrue(result.getErrorMessage().contains("disabled"));
    }

    @Test
    void testRecognizeTextAsyncWithNullFile() {
        // When
        CompletableFuture<OcrResult> future = ocrService.recognizeTextAsync((File) null);

        // Then
        assertNotNull(future);
        OcrResult result = future.join();
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
    }

    @Test
    void testRecognizeTextAsyncWithNullBufferedImage() {
        // When
        CompletableFuture<OcrResult> future = ocrService.recognizeTextAsync((BufferedImage) null);

        // Then
        assertNotNull(future);
        OcrResult result = future.join();
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
    }

    @Test
    void testIsAvailableWhenEnabled() {
        // Given - configuration already set in setUp()

        // When & Then
        // Note: This test may fail if Tesseract is not properly installed
        // In a real environment, we would mock the Tesseract initialization
        try {
            ocrService.initialize();
            assertTrue(ocrService.isAvailable());
        } catch (RuntimeException e) {
            // Expected if Tesseract is not installed
            assertFalse(ocrService.isAvailable());
        }
    }

    @Test
    void testIsAvailableWhenDisabled() {
        // Given
        when(ocrConfig.isEnabled()).thenReturn(false);
        OcrService disabledService = new OcrService(ocrConfig, threadPoolManager, cacheManager, performanceMonitor, ocrMetrics, ocrLogger);

        // When & Then
        assertFalse(disabledService.isAvailable());
    }

    @Test
    void testGetConfigInfo() {
        // Given - configuration already set in setUp()

        // When
        var configInfo = ocrService.getConfigInfo();

        // Then
        assertNotNull(configInfo);
        assertTrue(configInfo.containsKey("languages"));
        assertTrue(configInfo.containsKey("pageSegmentationMode"));
        assertTrue(configInfo.containsKey("ocrEngineMode"));
        assertTrue(configInfo.containsKey("confidenceThreshold"));
        assertTrue(configInfo.containsKey("highQualityThreshold"));
        assertTrue(configInfo.containsKey("preprocessingEnabled"));
        assertTrue(configInfo.containsKey("timeoutSeconds"));
        assertTrue(configInfo.containsKey("adaptivePageSegmentation"));
        assertTrue(configInfo.containsKey("maxImageSize"));
        assertTrue(configInfo.containsKey("imageCacheEnabled"));
        assertTrue(configInfo.containsKey("cacheSize"));
        assertTrue(configInfo.containsKey("threadPoolSize"));

        assertEquals(List.of("eng", "chi_sim"), configInfo.get("languages"));
        assertEquals(3, configInfo.get("pageSegmentationMode"));
        assertEquals(3, configInfo.get("ocrEngineMode"));
        assertEquals(60, configInfo.get("confidenceThreshold"));
        assertEquals(80, configInfo.get("highQualityThreshold"));
        assertEquals(true, configInfo.get("preprocessingEnabled"));
        assertEquals(30, configInfo.get("timeoutSeconds"));
        assertEquals(true, configInfo.get("adaptivePageSegmentation"));
        assertEquals(2048, configInfo.get("maxImageSize"));
        assertEquals(true, configInfo.get("imageCacheEnabled"));
        assertEquals(100, configInfo.get("cacheSize"));
        assertEquals(0, configInfo.get("threadPoolSize"));
    }

    @Test
    void testShutdown() {
        // When & Then
        assertDoesNotThrow(() -> ocrService.shutdown());
    }

    @Test
    void testRecognizeTextWithValidImageShouldHandleException() {
        // Given
        BufferedImage testImage = new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);

        // When
        OcrResult result = ocrService.recognizeText(testImage);

        // Then
        assertNotNull(result);
        // The result will likely be a failure due to Tesseract not being properly initialized in test environment
        // This is expected behavior for unit tests
    }

    @Test
    void testConfigurationValidation() {
        // Test that the service properly uses configuration values
        // Force some method calls to verify configuration usage
        ocrService.getConfigInfo();

        // Verify the methods that are actually called by getConfigInfo()
        verify(ocrConfig, atLeastOnce()).getLanguages();
        verify(ocrConfig, atLeastOnce()).getPageSegmentationMode();
        verify(ocrConfig, atLeastOnce()).getOcrEngineMode();
        verify(ocrConfig, atLeastOnce()).getConfidenceThreshold();
        verify(ocrConfig, atLeastOnce()).getHighQualityThreshold();
        verify(ocrConfig, atLeastOnce()).isPreprocessingEnabled();
        verify(ocrConfig, atLeastOnce()).getTimeoutSeconds();
        verify(ocrConfig, atLeastOnce()).isAdaptivePageSegmentation();
        verify(ocrConfig, atLeastOnce()).getMaxImageSize();
        verify(ocrConfig, atLeastOnce()).isImageCacheEnabled();
        verify(ocrConfig, atLeastOnce()).getCacheSize();
        verify(ocrConfig, atLeastOnce()).getThreadPoolSize();
    }

    @Test
    void testGetThreadPoolStats() {
        // When
        var stats = ocrService.getThreadPoolStats();

        // Then
        assertNotNull(stats);
        assertEquals(2, stats.getCorePoolSize());
        assertEquals(4, stats.getMaxPoolSize());
        assertEquals(1, stats.getActiveThreads());
        assertEquals(2, stats.getCurrentPoolSize());
        assertEquals(0, stats.getQueueSize());
        assertEquals(10, stats.getTotalTasksSubmitted());
        assertEquals(8, stats.getTotalTasksCompleted());
        assertEquals(2, stats.getTotalTasksFailed());
        assertEquals(1000, stats.getTotalProcessingTime());

        verify(threadPoolManager).getStats();
    }

    @Test
    void testGetCacheStats() {
        // When
        var stats = ocrService.getCacheStats();

        // Then
        assertNotNull(stats);
        assertEquals(5, stats.getCurrentSize());
        assertEquals(100, stats.getMaxSize());
        assertEquals(20, stats.getHits());
        assertEquals(5, stats.getMisses());
        assertEquals(0.8, stats.getHitRate(), 0.01);
        assertEquals(2, stats.getEvictions());
        assertEquals(1024, stats.getMemoryUsage());

        verify(cacheManager).getStats();
    }

    @Test
    void testClearCache() {
        // When
        ocrService.clearCache();

        // Then
        verify(cacheManager).clear();
    }

    @Test
    void testGetServiceStatus() {
        // When
        var status = ocrService.getServiceStatus();

        // Then
        assertNotNull(status);
        assertTrue(status.containsKey("enabled"));
        assertTrue(status.containsKey("threadPoolStats"));
        assertTrue(status.containsKey("cacheStats"));
        assertTrue(status.containsKey("configuration"));

        assertEquals(true, status.get("enabled"));
        assertNotNull(status.get("threadPoolStats"));
        assertNotNull(status.get("cacheStats"));
        assertNotNull(status.get("configuration"));

        verify(threadPoolManager).getStats();
        verify(cacheManager).getStats();
    }
}
