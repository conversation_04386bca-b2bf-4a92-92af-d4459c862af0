package com.talkweb.ai.converter.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

class CacheManagerTest {

    private CacheManager cacheManager;

    @BeforeEach
    void setUp() {
        cacheManager = new CacheManager(Duration.ofSeconds(5), 100);
    }

    @Test
    void testGetWithValueLoader() {
        // Arrange
        AtomicInteger counter = new AtomicInteger(0);
        
        // Act
        String value1 = cacheManager.get("key1", key -> {
            counter.incrementAndGet();
            return "value1";
        });
        
        String value2 = cacheManager.get("key1", key -> {
            counter.incrementAndGet();
            return "value2"; // This should not be returned
        });
        
        // Assert
        assertEquals("value1", value1, "First call should return computed value");
        assertEquals("value1", value2, "Second call should return cached value");
        assertEquals(1, counter.get(), "Value loader should be called only once");
    }

    @Test
    void testGetWithExpiration() throws InterruptedException {
        // Arrange
        CacheManager shortTtlCache = new CacheManager(Duration.ofMillis(100), 100);
        AtomicInteger counter = new AtomicInteger(0);
        
        // Act
        String value1 = shortTtlCache.get("key1", key -> {
            counter.incrementAndGet();
            return "value1";
        });
        
        // Wait for cache to expire
        Thread.sleep(200);
        
        String value2 = shortTtlCache.get("key1", key -> {
            counter.incrementAndGet();
            return "value2";
        });
        
        // Assert
        assertEquals("value1", value1, "First call should return first computed value");
        assertEquals("value2", value2, "After expiration, should return new computed value");
        assertEquals(2, counter.get(), "Value loader should be called twice due to expiration");
    }

    @Test
    void testPutAndGet() {
        // Act
        cacheManager.put("key1", "value1");
        Optional<String> value = cacheManager.get("key1");
        
        // Assert
        assertTrue(value.isPresent(), "Value should be present in cache");
        assertEquals("value1", value.get(), "Retrieved value should match stored value");
    }

    @Test
    void testRemove() {
        // Arrange
        cacheManager.put("key1", "value1");
        
        // Act
        boolean removed = cacheManager.remove("key1");
        Optional<String> value = cacheManager.get("key1");
        
        // Assert
        assertTrue(removed, "Remove should return true for existing key");
        assertFalse(value.isPresent(), "Value should not be present after removal");
        
        // Act - Try to remove non-existent key
        boolean removedAgain = cacheManager.remove("key1");
        
        // Assert
        assertFalse(removedAgain, "Remove should return false for non-existent key");
    }

    @Test
    void testClear() {
        // Arrange
        cacheManager.put("key1", "value1");
        cacheManager.put("key2", "value2");
        
        // Act
        cacheManager.clear();
        
        // Assert
        assertEquals(0, cacheManager.size(), "Cache should be empty after clear");
        assertFalse(cacheManager.get("key1").isPresent(), "Value should not be present after clear");
        assertFalse(cacheManager.get("key2").isPresent(), "Value should not be present after clear");
    }

    @Test
    void testSize() {
        // Act
        cacheManager.put("key1", "value1");
        cacheManager.put("key2", "value2");
        
        // Assert
        assertEquals(2, cacheManager.size(), "Cache size should be 2");
        
        // Act
        cacheManager.remove("key1");
        
        // Assert
        assertEquals(1, cacheManager.size(), "Cache size should be 1 after removal");
    }

    @Test
    void testCleanup() throws InterruptedException {
        // Arrange
        CacheManager shortTtlCache = new CacheManager(Duration.ofMillis(100), 100);
        shortTtlCache.put("key1", "value1");
        shortTtlCache.put("key2", "value2");
        
        // Wait for cache to expire
        Thread.sleep(200);
        
        // Act
        shortTtlCache.cleanup();
        
        // Assert
        assertEquals(0, shortTtlCache.size(), "Cache should be empty after cleanup");
    }

    @Test
    void testMaxEntries() {
        // Arrange
        CacheManager smallCache = new CacheManager(Duration.ofSeconds(5), 2);
        
        // Act - Add entries up to max
        smallCache.put("key1", "value1");
        smallCache.put("key2", "value2");
        
        // Assert
        assertEquals(2, smallCache.size(), "Cache should have 2 entries");
        
        // Act - Add one more entry, which should trigger cleanup
        smallCache.put("key3", "value3");
        
        // Assert - Since no entries are expired, all should still be present
        assertEquals(3, smallCache.size(), "Cache should have 3 entries");
        
        // Now let's test with expired entries
        CacheManager smallCacheWithExpired = new CacheManager(Duration.ofMillis(1), 2);
        smallCacheWithExpired.put("key1", "value1");
        smallCacheWithExpired.put("key2", "value2");
        
        try {
            // Wait for entries to expire
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Add new entry, which should trigger cleanup of expired entries
        smallCacheWithExpired.put("key3", "value3");
        
        // Only the new entry should remain
        assertEquals(1, smallCacheWithExpired.size(), "Cache should have only the new entry");
        assertTrue(smallCacheWithExpired.get("key3").isPresent(), "New entry should be present");
    }

    @Test
    void testCustomTtl() throws InterruptedException {
        // Arrange
        CacheManager cache = new CacheManager(Duration.ofSeconds(10), 100);
        
        // Act - Add entry with custom short TTL
        cache.put("key1", "value1", Duration.ofMillis(100));
        
        // Assert
        assertTrue(cache.get("key1").isPresent(), "Value should be present initially");
        
        // Wait for custom TTL to expire
        Thread.sleep(200);
        
        // Assert
        assertFalse(cache.get("key1").isPresent(), "Value should be expired after custom TTL");
        
        // Act - Add entry with default TTL
        cache.put("key2", "value2");
        
        // Wait for the same amount of time
        Thread.sleep(200);
        
        // Assert
        assertTrue(cache.get("key2").isPresent(), "Value with default TTL should still be present");
    }
}