package com.talkweb.ai.converter.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class FileScannerServiceTest {

    @TempDir
    Path tempDir;
    
    private FileScannerService fileScannerService;
    
    @BeforeEach
    void setUp() {
        fileScannerService = new FileScannerService();
    }
    
    @Test
    void testScanWithoutRecursion() throws IOException {
        // Arrange
        Path file1 = tempDir.resolve("file1.txt");
        Path file2 = tempDir.resolve("file2.pdf");
        Path subDir = tempDir.resolve("subdir");
        Path file3 = subDir.resolve("file3.txt");
        
        Files.writeString(file1, "Content 1");
        Files.writeString(file2, "Content 2");
        Files.createDirectory(subDir);
        Files.writeString(file3, "Content 3");
        
        // Act
        List<Path> result = fileScannerService.scan(tempDir, false, null, null);
        
        // Assert
        assertEquals(2, result.size(), "Should find 2 files in the root directory");
        assertTrue(result.contains(file1), "Should contain file1.txt");
        assertTrue(result.contains(file2), "Should contain file2.pdf");
        assertFalse(result.contains(file3), "Should not contain file3.txt from subdirectory");
    }
    
    @Test
    void testScanWithRecursion() throws IOException {
        // Arrange
        Path file1 = tempDir.resolve("file1.txt");
        Path file2 = tempDir.resolve("file2.pdf");
        Path subDir = tempDir.resolve("subdir");
        Path file3 = subDir.resolve("file3.txt");
        
        Files.writeString(file1, "Content 1");
        Files.writeString(file2, "Content 2");
        Files.createDirectory(subDir);
        Files.writeString(file3, "Content 3");
        
        // Act
        List<Path> result = fileScannerService.scan(tempDir, true, null, null);
        
        // Assert
        assertEquals(3, result.size(), "Should find 3 files including subdirectory");
        assertTrue(result.contains(file1), "Should contain file1.txt");
        assertTrue(result.contains(file2), "Should contain file2.pdf");
        assertTrue(result.contains(file3), "Should contain file3.txt from subdirectory");
    }
    
    @Test
    void testScanWithIncludePatterns() throws IOException {
        // Arrange
        Path file1 = tempDir.resolve("file1.txt");
        Path file2 = tempDir.resolve("file2.pdf");
        Path file3 = tempDir.resolve("file3.docx");
        
        Files.writeString(file1, "Content 1");
        Files.writeString(file2, "Content 2");
        Files.writeString(file3, "Content 3");
        
        // Act
        List<Path> result = fileScannerService.scan(tempDir, false, new String[]{"*.txt", "*.pdf"}, null);
        
        // Assert
        assertEquals(2, result.size(), "Should find 2 files matching the include patterns");
        assertTrue(result.contains(file1), "Should contain file1.txt");
        assertTrue(result.contains(file2), "Should contain file2.pdf");
        assertFalse(result.contains(file3), "Should not contain file3.docx");
    }
    
    @Test
    void testScanWithExcludePatterns() throws IOException {
        // Arrange
        Path file1 = tempDir.resolve("file1.txt");
        Path file2 = tempDir.resolve("file2.pdf");
        Path file3 = tempDir.resolve("file3.docx");
        
        Files.writeString(file1, "Content 1");
        Files.writeString(file2, "Content 2");
        Files.writeString(file3, "Content 3");
        
        // Act
        List<Path> result = fileScannerService.scan(tempDir, false, null, new String[]{"*.pdf"});
        
        // Assert
        assertEquals(2, result.size(), "Should find 2 files not matching the exclude pattern");
        assertTrue(result.contains(file1), "Should contain file1.txt");
        assertFalse(result.contains(file2), "Should not contain file2.pdf");
        assertTrue(result.contains(file3), "Should contain file3.docx");
    }
    
    @Test
    void testScanWithIncludeAndExcludePatterns() throws IOException {
        // Arrange
        Path file1 = tempDir.resolve("file1.txt");
        Path file2 = tempDir.resolve("file2.pdf");
        Path file3 = tempDir.resolve("file3.docx");
        Path file4 = tempDir.resolve("file4.txt");
        
        Files.writeString(file1, "Content 1");
        Files.writeString(file2, "Content 2");
        Files.writeString(file3, "Content 3");
        Files.writeString(file4, "Content 4");
        
        // Act
        List<Path> result = fileScannerService.scan(tempDir, false, 
                new String[]{"*.txt", "*.pdf"}, 
                new String[]{"file4.txt"});
        
        // Assert
        assertEquals(2, result.size(), "Should find 2 files matching include but not exclude patterns");
        assertTrue(result.contains(file1), "Should contain file1.txt");
        assertTrue(result.contains(file2), "Should contain file2.pdf");
        assertFalse(result.contains(file3), "Should not contain file3.docx");
        assertFalse(result.contains(file4), "Should not contain file4.txt");
    }
    
    @Test
    void testShouldProcessFile() {
        // Arrange
        Path file1 = tempDir.resolve("file1.txt");
        Path file2 = tempDir.resolve("file2.pdf");
        Path file3 = tempDir.resolve("file3.docx");
        
        // Act & Assert
        assertTrue(fileScannerService.shouldProcessFile(file1, new String[]{"*.txt"}, null), 
                "Should process file1.txt with *.txt include pattern");
        
        assertFalse(fileScannerService.shouldProcessFile(file2, new String[]{"*.txt"}, null), 
                "Should not process file2.pdf with *.txt include pattern");
        
        assertTrue(fileScannerService.shouldProcessFile(file1, null, new String[]{"*.pdf"}), 
                "Should process file1.txt with *.pdf exclude pattern");
        
        assertFalse(fileScannerService.shouldProcessFile(file2, null, new String[]{"*.pdf"}), 
                "Should not process file2.pdf with *.pdf exclude pattern");
        
        assertTrue(fileScannerService.shouldProcessFile(file1, new String[]{"*.txt", "*.pdf"}, new String[]{"file2.txt"}), 
                "Should process file1.txt with include and exclude patterns");
        
        assertFalse(fileScannerService.shouldProcessFile(file3, new String[]{"*.txt", "*.pdf"}, null), 
                "Should not process file3.docx with *.txt, *.pdf include patterns");
    }
    
    @Test
    void testScanWithInvalidPath() {
        // Arrange
        Path nonExistentPath = tempDir.resolve("non-existent");
        
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            fileScannerService.scan(nonExistentPath, false, null, null);
        }, "Should throw IllegalArgumentException for non-existent directory");
    }
}