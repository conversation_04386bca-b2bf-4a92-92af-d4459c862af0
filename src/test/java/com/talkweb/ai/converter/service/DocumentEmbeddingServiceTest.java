package com.talkweb.ai.converter.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.ai.embedding.Embedding;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Test class for DocumentEmbeddingService
 */
@ExtendWith(MockitoExtension.class)
class DocumentEmbeddingServiceTest {

    @Mock
    private EmbeddingModel embeddingModel;

    @Mock
    private EmbeddingResponse embeddingResponse;

    @Mock
    private Embedding embedding;

    private DocumentEmbeddingService documentEmbeddingService;

    @BeforeEach
    void setUp() {
        documentEmbeddingService = new DocumentEmbeddingService(embeddingModel);
    }

    @Test
    void testGenerateEmbeddings_WithValidContent() {
        // Given
        String content = "This is a test document for embedding generation.";
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("source", "test");
        
        float[] mockEmbedding = new float[]{0.1f, 0.2f, 0.3f};
        when(embedding.getOutput()).thenReturn(mockEmbedding);
        when(embeddingResponse.getResults()).thenReturn(List.of(embedding));
        when(embeddingModel.call(any(EmbeddingRequest.class))).thenReturn(embeddingResponse);

        // When
        List<DocumentChunk> result = documentEmbeddingService.generateEmbeddings(content, metadata);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        
        DocumentChunk chunk = result.get(0);
        assertEquals(content, chunk.getContent());
        assertArrayEquals(mockEmbedding, chunk.getEmbedding());
        assertTrue(chunk.getMetadata().containsKey("chunk_index"));
        assertTrue(chunk.getMetadata().containsKey("total_chunks"));
        assertTrue(chunk.getMetadata().containsKey("chunk_size"));
    }

    @Test
    void testGenerateEmbeddings_WithEmptyContent() {
        // When
        List<DocumentChunk> result = documentEmbeddingService.generateEmbeddings("", new HashMap<>());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGenerateEmbeddings_WithNullContent() {
        // When
        List<DocumentChunk> result = documentEmbeddingService.generateEmbeddings(null, new HashMap<>());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGenerateSingleEmbedding_WithValidText() {
        // Given
        String text = "Test text for single embedding";
        float[] mockEmbedding = new float[]{0.1f, 0.2f, 0.3f};
        
        when(embedding.getOutput()).thenReturn(mockEmbedding);
        when(embeddingResponse.getResults()).thenReturn(List.of(embedding));
        when(embeddingModel.call(any(EmbeddingRequest.class))).thenReturn(embeddingResponse);

        // When
        float[] result = documentEmbeddingService.generateSingleEmbedding(text);

        // Then
        assertNotNull(result);
        assertArrayEquals(mockEmbedding, result);
    }

    @Test
    void testGenerateSingleEmbedding_WithEmptyText() {
        // When
        float[] result = documentEmbeddingService.generateSingleEmbedding("");

        // Then
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void testCalculateSimilarity() {
        // Given
        float[] embedding1 = new float[]{1.0f, 0.0f, 0.0f};
        float[] embedding2 = new float[]{0.0f, 1.0f, 0.0f};
        float[] embedding3 = new float[]{1.0f, 0.0f, 0.0f}; // Same as embedding1

        // When
        double similarity1 = documentEmbeddingService.calculateSimilarity(embedding1, embedding2);
        double similarity2 = documentEmbeddingService.calculateSimilarity(embedding1, embedding3);

        // Then
        assertEquals(0.0, similarity1, 0.001); // Orthogonal vectors
        assertEquals(1.0, similarity2, 0.001); // Identical vectors
    }

    @Test
    void testGenerateEmbeddingsAsync() {
        // Given
        String content = "Test content for async processing";
        Map<String, Object> metadata = new HashMap<>();
        
        float[] mockEmbedding = new float[]{0.1f, 0.2f, 0.3f};
        when(embedding.getOutput()).thenReturn(mockEmbedding);
        when(embeddingResponse.getResults()).thenReturn(List.of(embedding));
        when(embeddingModel.call(any(EmbeddingRequest.class))).thenReturn(embeddingResponse);

        // When
        List<DocumentChunk> result = documentEmbeddingService.generateEmbeddingsAsync(content, metadata).join();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
