package com.talkweb.ai.converter.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.model.ChatModel;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Test class for DocumentSummaryService
 */
@ExtendWith(MockitoExtension.class)
class DocumentSummaryServiceTest {

    @Mock
    private ChatModel chatModel;

    private DocumentSummaryService documentSummaryService;

    @BeforeEach
    void setUp() {
        documentSummaryService = new DocumentSummaryService(chatModel);
    }

    @Test
    void testGenerateSummary_WithValidContent() {
        // Given
        String content = "This is a test document with some content to summarize.";
        int maxLength = 100;
        String expectedSummary = "Test document summary";
        
        when(chatModel.call(anyString())).thenReturn(expectedSummary);

        // When
        String result = documentSummaryService.generateSummary(content, maxLength);

        // Then
        assertNotNull(result);
        assertEquals(expectedSummary, result);
    }

    @Test
    void testGenerateSummary_WithEmptyContent() {
        // When
        String result = documentSummaryService.generateSummary("", 100);

        // Then
        assertEquals("No content to summarize.", result);
    }

    @Test
    void testGenerateSummary_WithNullContent() {
        // When
        String result = documentSummaryService.generateSummary(null, 100);

        // Then
        assertEquals("No content to summarize.", result);
    }

    @Test
    void testExtractKeyPoints_WithValidContent() {
        // Given
        String content = "This is a test document with multiple key points to extract.";
        int maxPoints = 3;
        String expectedKeyPoints = "1. Key point one\n2. Key point two\n3. Key point three";
        
        when(chatModel.call(anyString())).thenReturn(expectedKeyPoints);

        // When
        String result = documentSummaryService.extractKeyPoints(content, maxPoints);

        // Then
        assertNotNull(result);
        assertEquals(expectedKeyPoints, result);
    }

    @Test
    void testAnalyzeContent_WithValidContent() {
        // Given
        String content = "This is a test document for content analysis.";
        String expectedAnalysis = "Document Analysis: This appears to be a test document.";
        
        when(chatModel.call(anyString())).thenReturn(expectedAnalysis);

        // When
        String result = documentSummaryService.analyzeContent(content);

        // Then
        assertNotNull(result);
        assertEquals(expectedAnalysis, result);
    }

    @Test
    void testGenerateSummaryAsync() {
        // Given
        String content = "Test content for async processing";
        int maxLength = 50;
        String expectedSummary = "Async summary";
        
        when(chatModel.call(anyString())).thenReturn(expectedSummary);

        // When
        String result = documentSummaryService.generateSummaryAsync(content, maxLength).join();

        // Then
        assertNotNull(result);
        assertEquals(expectedSummary, result);
    }
}
