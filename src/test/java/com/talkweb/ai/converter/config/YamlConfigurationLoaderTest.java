package com.talkweb.ai.converter.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class YamlConfigurationLoaderTest {

    private YamlConfigurationLoader loader;

    @BeforeEach
    void setUp() {
        loader = new YamlConfigurationLoader();
    }

    @Test
    void testLoadFromString() throws IOException, ConfigurationException {
        String yaml = """
        app:
          name: Test App
          version: 1.0.0
          enabled: true
          count: 42
          pi: 3.14159
          tags:
            - one
            - two
            - three
        nested:
          value: test
          enabled: false
        """;

        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(yaml.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }

        assertEquals("Test App", config.getString("app.name", null));
        assertEquals("1.0.0", config.getString("app.version", null));
        assertTrue(config.getBoolean("app.enabled", false));
        assertEquals(42, config.getInt("app.count", 0));
        assertEquals(3.14159, config.getDouble("app.pi", 0.0), 0.00001);

        List<String> tags = config.getStringList("app.tags");
        assertEquals(3, tags.size());
        assertEquals("one", tags.get(0));
        assertEquals("two", tags.get(1));
        assertEquals("three", tags.get(2));

        AppConfiguration nested = config.getConfig("nested").orElseThrow();
        assertEquals("test", nested.getString("value", null));
        assertFalse(nested.getBoolean("enabled", true));
    }

    @Test
    void testLoadFromFile(@TempDir Path tempDir) throws IOException, ConfigurationException {
        Path yamlFile = tempDir.resolve("config.yaml");
        String yaml = """
        database:
          host: localhost
          port: 5432
          name: testdb
          users:
            - admin
            - user1
            - user2
        """;

        Files.writeString(yamlFile, yaml, StandardCharsets.UTF_8);

        AppConfiguration config = loader.load(Files.newInputStream(yamlFile));

        AppConfiguration dbConfig = config.getConfig("database").orElseThrow();
        assertEquals("localhost", dbConfig.getString("host", null));
        assertEquals(5432, dbConfig.getInt("port", 0));

        List<String> users = dbConfig.getStringList("users");
        assertEquals(3, users.size());
        assertEquals("admin", users.get(0));
        assertEquals("user1", users.get(1));
        assertEquals("user2", users.get(2));
    }

    @Test
    void testLoadWithComments() throws IOException, ConfigurationException {
        String yaml = """
        # This is a comment
        app:
          # Another comment
          name: Test
          value: 123
          # Multi-line comment
          # continues here
          enabled: true
        """;

        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(yaml.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }

        assertEquals("Test", config.getString("app.name", null));
        assertEquals(123, config.getInt("app.value", 0));
        assertTrue(config.getBoolean("app.enabled", false));
    }

    @Test
    void testLoadInvalidYaml() {
        String invalidYaml = "app: name: Test App"; // Invalid YAML syntax

        assertThrows(ConfigurationException.class, () -> {
            try (InputStream input = new ByteArrayInputStream(invalidYaml.getBytes(StandardCharsets.UTF_8))) {
                loader.load(input);
            }
        });
    }

    @Test
    void testLoadEmptyYaml() throws IOException, ConfigurationException {
        String emptyYaml = "";
        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(emptyYaml.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }
        assertTrue(!config.getKeys().iterator().hasNext());
    }

    @Test
    void testLoadListRoot() throws IOException, ConfigurationException {
        String listYaml = """
        - name: one
        - name: two
        """;

        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(listYaml.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }

        // Should be converted to a config with a single key "_array" containing the array
        assertTrue(config.getKeys().iterator().hasNext());
        List<AppConfiguration> items = config.getConfigList("_array");
        assertEquals(2, items.size());
        assertEquals("one", items.get(0).getString("name", ""));
        assertEquals("two", items.get(1).getString("name", ""));
    }

    @Test
    void testSaveAndReload(@TempDir Path tempDir) throws IOException, ConfigurationException {
        // Create initial config
        MapConfiguration config = new MapConfiguration();
        config.set("app.name", "Test App");
        config.set("app.version", "1.0.0");
        config.set("app.enabled", true);
        config.set("app.tags", List.of("tag1", "tag2"));

        // Save to file
        Path yamlFile = tempDir.resolve("output.yaml");
        loader.save(config, yamlFile);

        // Reload
        AppConfiguration loaded = loader.load(Files.newInputStream(yamlFile));

        // Verify
        assertEquals("Test App", loaded.getString("app.name", null));
        assertEquals("1.0.0", loaded.getString("app.version", null));
        assertTrue(loaded.getBoolean("app.enabled", false));

        List<String> tags = loaded.getStringList("app.tags");
        assertEquals(2, tags.size());
        assertEquals("tag1", tags.get(0));
        assertEquals("tag2", tags.get(1));
    }

    @Test
    void testSaveAndReloadNested(@TempDir Path tempDir) throws IOException, ConfigurationException {
        // Create nested config
        MapConfiguration nested = new MapConfiguration();
        nested.set("host", "localhost");
        nested.set("port", 5432);

        MapConfiguration config = new MapConfiguration();
        config.set("database", nested);
        config.set("enabled", true);

        // Save and reload
        Path yamlFile = tempDir.resolve("nested.yaml");
        loader.save(config, yamlFile);
        AppConfiguration loaded = loader.load(Files.newInputStream(yamlFile));

        // Verify
        assertTrue(loaded.getBoolean("enabled", false));

        AppConfiguration loadedDb = loaded.getConfig("database").orElseThrow();
        assertEquals("localhost", loadedDb.getString("host", null));
        assertEquals(5432, loadedDb.getInt("port", 0));
    }

    @Test
    @org.junit.jupiter.api.Disabled("YAML anchors not supported yet")
    void testLoadYamlWithAnchors() throws IOException, ConfigurationException {
        String yaml = """
        defaults: &defaults
          adapter: postgres
          host: localhost

        development:
          <<: *defaults
          database: myapp_development

        test:
          <<: *defaults
          database: myapp_test
        """;

        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(yaml.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }

        // Verify anchors and aliases are resolved
        AppConfiguration devConfig = config.getConfig("development").orElseThrow();
        assertEquals("postgres", devConfig.getString("adapter", null));
        assertEquals("localhost", devConfig.getString("host", null));
        assertEquals("myapp_development", devConfig.getString("database", null));

        AppConfiguration testConfig = config.getConfig("test").orElseThrow();
        assertEquals("postgres", testConfig.getString("adapter", null));
        assertEquals("localhost", testConfig.getString("host", null));
        assertEquals("myapp_test", testConfig.getString("database", null));
    }

    @Test
    void testLoadYamlWithMultiLineStrings() throws IOException, ConfigurationException {
        String yaml = """
        description: |
          This is a multi-line
          string that spans
          multiple lines.

        literal: |-
          This has no newline at the end

        folded: >
          This is a long line
          that will be folded
          into a single line.
        """;

        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(yaml.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }

        assertEquals("This is a multi-line\nstring that spans\nmultiple lines.\n",
            config.getString("description", ""));
        assertEquals("This has no newline at the end",
            config.getString("literal", ""));
        assertEquals("This is a long line that will be folded into a single line.\n",
            config.getString("folded", ""));
    }
}
