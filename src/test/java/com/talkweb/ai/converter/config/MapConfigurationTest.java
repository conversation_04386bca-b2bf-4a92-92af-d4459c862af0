package com.talkweb.ai.converter.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;


import static org.junit.jupiter.api.Assertions.*;

class MapConfigurationTest {

    private MapConfiguration config;

    @BeforeEach
    void setUp() {
        config = new MapConfiguration();
    }

    @Test
    void testSetAndGetString() {
        config.set("test.string", "value");
        assertTrue(config.hasKey("test.string"));
        assertEquals("value", config.getString("test.string", null));
        assertEquals("default", config.getString("nonexistent", "default"));
    }

    @Test
    void testSetAndGetInt() {
        config.set("test.int", 42);
        assertEquals(42, config.getInt("test.int", 0));
        assertEquals(100, config.getInt("nonexistent", 100));
    }

    @Test
    void testSetAndGetLong() {
        config.set("test.long", 1234567890123L);
        assertEquals(1234567890123L, config.getLong("test.long", 0L));
        assertEquals(999L, config.getLong("nonexistent", 999L));
    }

    @Test
    void testSetAndGetDouble() {
        config.set("test.double", 3.14159);
        assertEquals(3.14159, config.getDouble("test.double", 0.0), 0.00001);
        assertEquals(1.234, config.getDouble("nonexistent", 1.234), 0.00001);
    }

    @Test
    void testSetAndGetBoolean() {
        config.set("test.boolean", true);
        assertTrue(config.getBoolean("test.boolean", false));
        assertTrue(config.getBoolean("nonexistent", true));
    }

    @Test
    void testSetAndGetStringList() {
        List<String> values = Arrays.asList("one", "two", "three");
        config.set("test.list", values);

        List<String> result = config.getStringList("test.list");
        assertEquals(3, result.size());
        assertIterableEquals(values, result);

        // Test with single value
        config.set("test.single", "single");
        List<String> singleResult = config.getStringList("test.single");
        assertEquals(1, singleResult.size());
        assertEquals("single", singleResult.get(0));
    }

    @Test
    void testNestedConfig() {
        Map<String, Object> nested = Map.of(
            "enabled", true,
            "count", 10,
            "name", "test"
        );

        config.set("nested", nested);

        AppConfiguration nestedConfig = config.getConfig("nested").orElseThrow();
        assertTrue(nestedConfig.getBoolean("enabled", false));
        assertEquals(10, nestedConfig.getInt("count", 0));
        assertEquals("test", nestedConfig.getString("name", ""));
    }

    @Test
    void testNestedConfigList() {
        List<Map<String, Object>> items = Arrays.asList(
            Map.of("id", 1, "name", "item1"),
            Map.of("id", 2, "name", "item2")
        );

        config.set("items", items);

        List<AppConfiguration> configList = config.getConfigList("items");
        assertEquals(2, configList.size());
        assertEquals("item1", configList.get(0).getString("name", ""));
        assertEquals(2, configList.get(1).getInt("id", 0));
    }

    @Test
    @SuppressWarnings("unchecked")
    void testToMap() {
        config.set("test.string", "value");
        config.set("test.int", 42);

        Map<String, Object> map = config.toMap();
        assertTrue(map.containsKey("test"));
        Object nested = map.get("test");
        assertTrue(nested instanceof Map);
        Map<String, Object> nestedMap = (Map<String, Object>) nested;
        assertEquals("value", nestedMap.get("string"));
        assertEquals(42, nestedMap.get("int"));
    }

    @Test
    void testGetValueType() {
        config.set("string", "value");
        config.set("int", 42);
        config.set("double", 3.14);
        config.set("boolean", true);
        config.set("list", Arrays.asList(1, 2, 3));
        config.set("map", Map.of("key", "value"));

        assertEquals(ConfigValueType.STRING, config.getValueType("string"));
        assertEquals(ConfigValueType.NUMBER, config.getValueType("int"));
        assertEquals(ConfigValueType.NUMBER, config.getValueType("double"));
        assertEquals(ConfigValueType.BOOLEAN, config.getValueType("boolean"));
        assertEquals(ConfigValueType.LIST, config.getValueType("list"));
        assertEquals(ConfigValueType.MAP, config.getValueType("map"));
        assertEquals(ConfigValueType.UNKNOWN, config.getValueType("nonexistent"));
    }

    @Test
    void testBuilder() {
        AppConfiguration builtConfig = MapConfiguration.builder()
            .put("key1", "value1")
            .put("nested.key", 123)
            .put("enabled", true)
            .build();

        assertEquals("value1", builtConfig.getString("key1", ""));
        assertEquals(123, builtConfig.getInt("nested.key", 0));
        assertTrue(builtConfig.getBoolean("enabled", false));
    }

    @Test
    void testRemove() {
        config.set("test", "value");
        assertTrue(config.hasKey("test"));

        config.remove("test");
        assertFalse(config.hasKey("test"));
    }

    @Test
    void testClear() {
        config.set("key1", "value1");
        config.set("key2", "value2");

        assertEquals(2, config.toMap().size());
        config.clear();
        assertTrue(config.toMap().isEmpty());
    }
}
