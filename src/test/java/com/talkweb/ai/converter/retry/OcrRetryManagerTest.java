package com.talkweb.ai.converter.retry;

import com.talkweb.ai.converter.logging.OcrLogger;
import com.talkweb.ai.converter.metrics.OcrMetrics;
import com.talkweb.ai.converter.model.OcrResult;
import com.talkweb.ai.converter.recovery.OcrErrorHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OCR重试管理器测试类
 */
@ExtendWith(MockitoExtension.class)
class OcrRetryManagerTest {

    @Mock
    private OcrErrorHandler errorHandler;
    
    @Mock
    private OcrLogger ocrLogger;
    
    @Mock
    private OcrMetrics ocrMetrics;

    private OcrRetryManager retryManager;

    @BeforeEach
    void setUp() {
        retryManager = new OcrRetryManager();
        
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field errorHandlerField = OcrRetryManager.class.getDeclaredField("errorHandler");
            errorHandlerField.setAccessible(true);
            errorHandlerField.set(retryManager, errorHandler);
            
            java.lang.reflect.Field loggerField = OcrRetryManager.class.getDeclaredField("ocrLogger");
            loggerField.setAccessible(true);
            loggerField.set(retryManager, ocrLogger);
            
            java.lang.reflect.Field metricsField = OcrRetryManager.class.getDeclaredField("ocrMetrics");
            metricsField.setAccessible(true);
            metricsField.set(retryManager, ocrMetrics);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set up test", e);
        }
        
        // 重置统计
        retryManager.resetRetryStatistics();
        retryManager.resetCircuitBreaker();
    }

    @Test
    void testExecuteWithRetry_SuccessOnFirstAttempt() {
        // Given
        String operationName = "test_operation";
        String expectedResult = "success";
        Supplier<String> operation = () -> expectedResult;
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();

        // When
        String result = retryManager.executeWithRetry(operationName, operation, config);

        // Then
        assertEquals(expectedResult, result);
        
        Map<String, OcrRetryManager.RetryStatistics> stats = retryManager.getRetryStatistics();
        assertTrue(stats.containsKey(operationName));
        assertEquals(1, stats.get(operationName).getTotalAttempts());
        assertEquals(1, stats.get(operationName).getSuccessfulRetries());
        assertEquals(0, stats.get(operationName).getFailedRetries());
    }

    @Test
    void testExecuteWithRetry_SuccessOnSecondAttempt() {
        // Given
        String operationName = "test_operation";
        String expectedResult = "success";
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        Supplier<String> operation = () -> {
            int attempt = attemptCount.incrementAndGet();
            if (attempt == 1) {
                throw new RuntimeException("First attempt failed");
            }
            return expectedResult;
        };
        
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setBaseDelayMs(10); // 减少测试时间

        // When
        String result = retryManager.executeWithRetry(operationName, operation, config);

        // Then
        assertEquals(expectedResult, result);
        assertEquals(2, attemptCount.get());
        
        Map<String, OcrRetryManager.RetryStatistics> stats = retryManager.getRetryStatistics();
        assertEquals(2, stats.get(operationName).getTotalAttempts());
        assertEquals(1, stats.get(operationName).getSuccessfulRetries());
        assertEquals(1, stats.get(operationName).getFailedRetries());
    }

    @Test
    void testExecuteWithRetry_FailAfterMaxAttempts() {
        // Given
        String operationName = "test_operation";
        Supplier<String> operation = () -> {
            throw new RuntimeException("Always fails");
        };
        
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setMaxAttempts(2);
        config.setBaseDelayMs(10);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            retryManager.executeWithRetry(operationName, operation, config);
        });
        
        assertTrue(exception.getMessage().contains("Operation failed after 2 attempts"));
        
        Map<String, OcrRetryManager.RetryStatistics> stats = retryManager.getRetryStatistics();
        assertEquals(2, stats.get(operationName).getTotalAttempts());
        assertEquals(0, stats.get(operationName).getSuccessfulRetries());
        assertEquals(2, stats.get(operationName).getFailedRetries());
    }

    @Test
    void testExecuteOcrWithRetry_Success() {
        // Given
        String operationName = "ocr_operation";
        OcrResult expectedResult = OcrResult.success("Test text", 85.0f);
        Supplier<OcrResult> ocrOperation = () -> expectedResult;

        // When
        OcrResult result = retryManager.executeOcrWithRetry(operationName, ocrOperation);

        // Then
        assertEquals(expectedResult, result);
        assertTrue(result.isSuccess());
        assertEquals("Test text", result.getText());
        assertEquals(85.0f, result.getConfidence(), 0.01);
    }

    @Test
    void testCircuitBreaker_OpenState() {
        // Given
        String operationName = "failing_operation";
        Supplier<String> operation = () -> {
            throw new RuntimeException("Always fails");
        };
        
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setMaxAttempts(1);
        config.setBaseDelayMs(10);
        config.setEnableCircuitBreaker(true);

        // When - 触发足够多的失败来打开熔断器
        for (int i = 0; i < 12; i++) { // 超过阈值10
            try {
                retryManager.executeWithRetry(operationName + "_" + i, operation, config);
            } catch (RuntimeException e) {
                // 预期的失败
            }
        }

        // Then - 熔断器应该是打开状态
        assertEquals(OcrRetryManager.CircuitBreakerState.OPEN, retryManager.getCircuitBreakerState());
        
        // 新的请求应该立即失败
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            retryManager.executeWithRetry("new_operation", operation, config);
        });
        
        assertTrue(exception.getMessage().contains("Circuit breaker is OPEN"));
    }

    @Test
    void testCircuitBreaker_Reset() {
        // Given
        String operationName = "failing_operation";
        Supplier<String> operation = () -> {
            throw new RuntimeException("Always fails");
        };
        
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setMaxAttempts(1);
        config.setEnableCircuitBreaker(true);

        // When - 触发失败打开熔断器
        for (int i = 0; i < 12; i++) {
            try {
                retryManager.executeWithRetry(operationName + "_" + i, operation, config);
            } catch (RuntimeException e) {
                // 预期的失败
            }
        }
        
        assertEquals(OcrRetryManager.CircuitBreakerState.OPEN, retryManager.getCircuitBreakerState());
        
        // 手动重置熔断器
        retryManager.resetCircuitBreaker();

        // Then
        assertEquals(OcrRetryManager.CircuitBreakerState.CLOSED, retryManager.getCircuitBreakerState());
    }

    @Test
    void testRetryStatistics() {
        // Given
        String operationName = "test_operation";
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        Supplier<String> operation = () -> {
            int attempt = attemptCount.incrementAndGet();
            if (attempt <= 2) {
                throw new RuntimeException("Fail on first two attempts");
            }
            return "success";
        };
        
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setBaseDelayMs(10);

        // When
        String result = retryManager.executeWithRetry(operationName, operation, config);

        // Then
        assertEquals("success", result);
        
        Map<String, OcrRetryManager.RetryStatistics> stats = retryManager.getRetryStatistics();
        OcrRetryManager.RetryStatistics opStats = stats.get(operationName);
        
        assertNotNull(opStats);
        assertEquals(3, opStats.getTotalAttempts());
        assertEquals(1, opStats.getSuccessfulRetries());
        assertEquals(2, opStats.getFailedRetries());
        assertEquals(1.0/3.0, opStats.getSuccessRate(), 0.01);
        assertTrue(opStats.getAvgRetryTime() > 0);
        assertNotNull(opStats.getLastRetryTime());
    }

    @Test
    void testResetRetryStatistics() {
        // Given
        String operationName = "test_operation";
        Supplier<String> operation = () -> "success";
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();

        // When
        retryManager.executeWithRetry(operationName, operation, config);
        assertFalse(retryManager.getRetryStatistics().isEmpty());
        
        retryManager.resetRetryStatistics();

        // Then
        assertTrue(retryManager.getRetryStatistics().isEmpty());
    }

    @Test
    void testGetRetryStatisticsReport() {
        // Given
        String operationName = "test_operation";
        Supplier<String> operation = () -> "success";
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();

        // When
        retryManager.executeWithRetry(operationName, operation, config);
        String report = retryManager.getRetryStatisticsReport();

        // Then
        assertNotNull(report);
        assertTrue(report.contains("OCR重试统计报告"));
        assertTrue(report.contains("熔断器状态"));
        assertTrue(report.contains(operationName));
        assertTrue(report.contains("总尝试次数"));
        assertTrue(report.contains("成功重试"));
        assertTrue(report.contains("失败重试"));
        assertTrue(report.contains("成功率"));
    }

    @Test
    void testRetryConfig_DefaultValues() {
        // When
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();

        // Then
        assertEquals(3, config.getMaxAttempts());
        assertEquals(1000, config.getBaseDelayMs());
        assertEquals(30000, config.getMaxDelayMs());
        assertEquals(2.0, config.getBackoffMultiplier(), 0.01);
        assertEquals(0.1, config.getJitterFactor(), 0.01);
        assertTrue(config.isEnableCircuitBreaker());
    }

    @Test
    void testRetryConfig_SettersAndGetters() {
        // Given
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();

        // When
        config.setMaxAttempts(5);
        config.setBaseDelayMs(2000);
        config.setMaxDelayMs(60000);
        config.setBackoffMultiplier(3.0);
        config.setJitterFactor(0.2);
        config.setEnableCircuitBreaker(false);

        // Then
        assertEquals(5, config.getMaxAttempts());
        assertEquals(2000, config.getBaseDelayMs());
        assertEquals(60000, config.getMaxDelayMs());
        assertEquals(3.0, config.getBackoffMultiplier(), 0.01);
        assertEquals(0.2, config.getJitterFactor(), 0.01);
        assertFalse(config.isEnableCircuitBreaker());
    }

    @Test
    void testRetryStatistics_Methods() {
        // Given
        OcrRetryManager.RetryStatistics stats = new OcrRetryManager.RetryStatistics();

        // When
        stats.recordAttempt();
        stats.recordAttempt();
        stats.recordSuccess(1000);
        stats.recordFailure();

        // Then
        assertEquals(2, stats.getTotalAttempts());
        assertEquals(1, stats.getSuccessfulRetries());
        assertEquals(1, stats.getFailedRetries());
        assertEquals(1000, stats.getTotalRetryTime());
        assertEquals(0.5, stats.getSuccessRate(), 0.01);
        assertEquals(1000.0, stats.getAvgRetryTime(), 0.01);
        assertNotNull(stats.getLastRetryTime());
    }

    @Test
    void testCircuitBreakerStates() {
        // Test all circuit breaker states
        assertEquals(OcrRetryManager.CircuitBreakerState.CLOSED, retryManager.getCircuitBreakerState());
        
        // The circuit breaker state changes are tested in other methods
        // This test just verifies the enum values exist
        assertNotNull(OcrRetryManager.CircuitBreakerState.CLOSED);
        assertNotNull(OcrRetryManager.CircuitBreakerState.OPEN);
        assertNotNull(OcrRetryManager.CircuitBreakerState.HALF_OPEN);
    }
}
