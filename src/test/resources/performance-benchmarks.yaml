# OCR性能基准配置
# 定义各种OCR操作的性能基准和期望值

benchmarks:
  # 小图像处理基准 (200x100像素)
  small_image_ocr:
    max_average_time_ms: 3000      # 平均处理时间不超过3秒
    max_max_time_ms: 5000          # 最大处理时间不超过5秒
    min_throughput_ops_sec: 0.2    # 最小吞吐量每秒0.2次操作
    description: "小图像OCR处理性能基准"
    
  # 中等图像处理基准 (800x600像素)
  medium_image_ocr:
    max_average_time_ms: 8000      # 平均处理时间不超过8秒
    max_max_time_ms: 12000         # 最大处理时间不超过12秒
    min_throughput_ops_sec: 0.1    # 最小吞吐量每秒0.1次操作
    description: "中等图像OCR处理性能基准"
    
  # 大图像处理基准 (1920x1080像素)
  large_image_ocr:
    max_average_time_ms: 15000     # 平均处理时间不超过15秒
    max_max_time_ms: 25000         # 最大处理时间不超过25秒
    min_throughput_ops_sec: 0.05   # 最小吞吐量每秒0.05次操作
    description: "大图像OCR处理性能基准"
    
  # 并发处理基准
  concurrent_processing:
    max_average_time_ms: 10000     # 并发任务平均完成时间不超过10秒
    max_max_time_ms: 20000         # 并发任务最大完成时间不超过20秒
    min_throughput_ops_sec: 0.5    # 并发处理最小吞吐量每秒0.5次操作
    description: "并发OCR处理性能基准"
    
  # 缓存性能基准
  cache_hit:
    max_average_time_ms: 100       # 缓存命中平均时间不超过100毫秒
    max_max_time_ms: 500           # 缓存命中最大时间不超过500毫秒
    min_throughput_ops_sec: 10.0   # 缓存命中最小吞吐量每秒10次操作
    description: "缓存命中性能基准"

# 性能测试配置
test_config:
  # 预热配置
  warmup:
    iterations: 3                  # 预热迭代次数
    enabled: true                  # 是否启用预热
    
  # 测试图像配置
  test_images:
    small:
      width: 200
      height: 100
      text: "Small Test Image"
      
    medium:
      width: 800
      height: 600
      text: "Medium Test Image with More Content"
      
    large:
      width: 1920
      height: 1080
      text: "Large Test Image with Extensive Text Content for Performance Testing"
      
  # 并发测试配置
  concurrent_test:
    thread_count: 10               # 并发线程数
    tasks_per_thread: 5            # 每个线程的任务数
    timeout_seconds: 60            # 超时时间
    
  # 内存测试配置
  memory_test:
    image_count: 50                # 测试图像数量
    max_memory_increase_mb: 500    # 最大内存增长（MB）
    gc_interval: 10                # 垃圾回收间隔（处理多少图像后触发）
    
  # 缓存测试配置
  cache_test:
    test_iterations: 10            # 缓存测试迭代次数
    expected_hit_rate: 0.9         # 期望缓存命中率
    cache_improvement_factor: 2.0  # 缓存性能提升倍数

# 监控配置
monitoring:
  # 性能指标收集
  metrics:
    enabled: true                  # 是否启用指标收集
    collection_interval_ms: 1000   # 指标收集间隔
    
  # 报告配置
  reporting:
    enabled: true                  # 是否启用报告
    format: "detailed"             # 报告格式：simple, detailed, json
    include_charts: false          # 是否包含图表
    
  # 告警配置
  alerts:
    enabled: true                  # 是否启用告警
    performance_degradation_threshold: 1.5  # 性能下降阈值倍数
    memory_usage_threshold_mb: 1000          # 内存使用告警阈值（MB）

# 环境配置
environment:
  # 测试环境要求
  requirements:
    min_memory_mb: 2048            # 最小内存要求（MB）
    min_cpu_cores: 2               # 最小CPU核心数
    tesseract_required: true       # 是否需要Tesseract
    
  # OCR配置优化
  ocr_optimization:
    page_segmentation_mode: 6      # 页面分割模式
    ocr_engine_mode: 1             # OCR引擎模式
    confidence_threshold: 50       # 置信度阈值
    high_quality_threshold: 80     # 高质量阈值
    max_image_size: 2048           # 最大图像尺寸
    cache_enabled: true            # 是否启用缓存
    cache_size: 100                # 缓存大小
    thread_pool_size: 4            # 线程池大小

# 基准历史记录（用于性能回归检测）
baseline:
  version: "1.0.0"
  date: "2025-06-23"
  environment: "test"
  
  # 历史基准数据
  historical_benchmarks:
    small_image_ocr:
      average_time_ms: 2500
      max_time_ms: 4000
      throughput_ops_sec: 0.25
      
    medium_image_ocr:
      average_time_ms: 7000
      max_time_ms: 10000
      throughput_ops_sec: 0.12
      
    large_image_ocr:
      average_time_ms: 12000
      max_time_ms: 18000
      throughput_ops_sec: 0.06
      
    concurrent_processing:
      average_time_ms: 8000
      max_time_ms: 15000
      throughput_ops_sec: 0.6
      
    cache_hit:
      average_time_ms: 50
      max_time_ms: 200
      throughput_ops_sec: 15.0

# 性能优化建议
optimization_tips:
  - "使用适当的页面分割模式以提高识别准确性"
  - "启用图像缓存以减少重复处理"
  - "调整线程池大小以匹配系统资源"
  - "对大图像进行预处理以减少处理时间"
  - "使用LSTM引擎模式以获得更好的准确性"
  - "定期清理缓存以避免内存泄漏"
  - "监控内存使用情况以防止OOM错误"
  - "使用异步处理以提高并发性能"
