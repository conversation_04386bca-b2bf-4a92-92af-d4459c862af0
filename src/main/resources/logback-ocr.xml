<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    
    <!-- 定义日志文件路径 -->
    <property name="LOG_HOME" value="${LOG_HOME:-./logs}" />
    <property name="APP_NAME" value="ocr-indexer" />
    
    <!-- 控制台输出配置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId:-}] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 主应用日志文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId:-}] [%X{imageType:-}] [%X{imageSize:-}] [%X{processingStage:-}] %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- OCR处理过程日志 -->
    <appender name="OCR_PROCESS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/ocr-process.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId:-}] [%X{imageType:-}] [%X{processingStage:-}] - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/ocr-process.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>15</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- OCR性能日志 -->
    <appender name="OCR_PERFORMANCE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/ocr-performance.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{requestId:-}] [%X{imageType:-}] [%X{imageSize:-}] - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/ocr-performance.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- OCR错误日志 -->
    <appender name="OCR_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/ocr-error.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId:-}] [%X{imageType:-}] [%X{imageSize:-}] %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/ocr-error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>60</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- JSON格式的结构化日志（用于日志分析） -->
    <appender name="JSON_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/ocr-structured.json</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <message/>
                <stackTrace/>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/ocr-structured.%d{yyyy-MM-dd}.%i.json</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步日志配置 -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
    </appender>
    
    <appender name="ASYNC_OCR_PROCESS" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="OCR_PROCESS"/>
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
    </appender>
    
    <appender name="ASYNC_OCR_PERFORMANCE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="OCR_PERFORMANCE"/>
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
    </appender>
    
    <appender name="ASYNC_JSON" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="JSON_FILE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
    </appender>
    
    <!-- 特定Logger配置 -->
    
    <!-- OCR处理过程日志 -->
    <logger name="OCR_PROCESS" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_OCR_PROCESS"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- OCR性能日志 -->
    <logger name="OCR_PERFORMANCE" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_OCR_PERFORMANCE"/>
    </logger>
    
    <!-- OCR错误日志 -->
    <logger name="OCR_ERROR" level="ERROR" additivity="false">
        <appender-ref ref="OCR_ERROR"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- OCR相关包的日志级别 -->
    <logger name="com.talkweb.ai.converter.service.OcrService" level="DEBUG"/>
    <logger name="com.talkweb.ai.converter.service.BatchOcrProcessor" level="INFO"/>
    <logger name="com.talkweb.ai.converter.metrics" level="INFO"/>
    <logger name="com.talkweb.ai.converter.logging" level="DEBUG"/>
    
    <!-- Tesseract相关日志 -->
    <logger name="net.sourceforge.tess4j" level="WARN"/>
    
    <!-- Spring相关日志 -->
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.springframework.scheduling" level="WARN"/>
    
    <!-- Micrometer相关日志 -->
    <logger name="io.micrometer" level="WARN"/>
    
    <!-- 根Logger配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
        <appender-ref ref="ASYNC_JSON"/>
    </root>
    
    <!-- 开发环境配置 -->
    <springProfile name="dev">
        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </root>
        <logger name="com.talkweb.ai.converter" level="DEBUG"/>
    </springProfile>
    
    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="ASYNC_FILE"/>
            <appender-ref ref="ASYNC_JSON"/>
        </root>
        <logger name="com.talkweb.ai.converter.service" level="INFO"/>
        <logger name="com.talkweb.ai.converter.logging" level="INFO"/>
    </springProfile>
    
    <!-- 测试环境配置 -->
    <springProfile name="test">
        <root level="WARN">
            <appender-ref ref="CONSOLE"/>
        </root>
        <logger name="com.talkweb.ai.converter" level="INFO"/>
    </springProfile>
    
</configuration>
