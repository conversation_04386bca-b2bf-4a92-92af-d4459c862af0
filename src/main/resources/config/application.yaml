# Application configuration
doc-converter:
  # General settings
  app:
    name: "Document Converter"
    version: "1.0.0"
    max-concurrent-tasks: 4
    temp-dir: "${TEMP:/tmp/doc-converter}"
  
  # File processing settings
  file:
    # Supported file extensions (without leading .)
    supported-formats:
      - pdf
      - docx
      - doc
      - xlsx
      - pptx
      - html
      - htm
      - txt
      - md
    
    # Default encoding for text files
    default-encoding: "UTF-8"
    
    # File size limits (in bytes)
    max-file-size: 104857600  # 100MB
    
    # Should skip files with unsupported extensions
    skip-unsupported: true
  
  # Output settings
  output:
    # Default output directory (relative to working directory)
    directory: "output"
    
    # Whether to preserve directory structure relative to input
    preserve-structure: true
    
    # File naming pattern (supports {name}, {ext}, {timestamp}, {counter})
    filename-pattern: "{name}.md"
    
    # Whether to overwrite existing files
    overwrite-existing: false
  
  # Logging settings
  logging:
    level: INFO
    file: "logs/application.log"
    max-size: "10MB"
    max-backups: 5
    
    # Format pattern
    pattern: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  
  # Plugin settings
  plugins:
    # Directory containing plugin J<PERSON> files
    directory: "plugins"
    
    # Auto-scan for plugins on startup
    auto-scan: true
    
    # List of plugins to load (empty = load all)
    enabled-plugins:
      - pdf-converter
      - docx-converter
      - excel-converter
      - pptx-converter
      - html-converter
      - image-converter
    
    # Plugin-specific configurations
    config:
      pdf-converter:
        extract-images: true
        image-quality: 0.8
        ocr-enabled: true
        ocr-language: "eng+chi_sim"
      
      docx-converter:
        extract-comments: true
        extract-track-changes: false
      
      excel-converter:
        include-sheets: "*"  # Comma-separated list or "*" for all
        include-hidden-sheets: false
        include-formulas: true
      
      pptx-converter:
        extract-speaker-notes: true
        extract-slide-notes: true
        extract-slide-titles: true
      
      html-converter:
        extract-links: true
        extract-images: true
        base-href: ""

      image-converter:
        ocr-enabled: true
        preprocessing-enabled: true
        confidence-threshold: 50  # 降低阈值以获得更多识别结果
        include-metadata: true
        include-processing-info: true  # 启用处理信息以便调试
        structure-level: "BASIC"  # NONE, BASIC, ADVANCED
        output-format: "MARKDOWN"  # MARKDOWN, MARKDOWN_WITH_META, STRUCTURED_TEXT
        timeout-seconds: 30
        enable-async-processing: true

        # 增强的图像预处理选项
        preprocessing-options:
          enable-denoising: true
          enable-binarization: true
          enable-skew-correction: true
          enable-resolution-optimization: true
          enable-contrast-enhancement: true
          target-dpi: 300
          contrast-factor: 1.2
          brightness-factor: 1.1
          binarization-threshold: 128

          # 新增高级选项
          enable-adaptive-thresholding: true
          enable-morphological-operations: true
          enable-edge-enhancement: false
          enable-noise-reduction: true
          enable-image-sharpening: false
          enable-auto-contrast: true

          # 高级参数
          denoising-method: "GAUSSIAN"  # GAUSSIAN, MEDIAN, BILATERAL, NON_LOCAL_MEANS, ADAPTIVE
          binarization-method: "ADAPTIVE_OTSU"  # SIMPLE_THRESHOLD, ADAPTIVE_MEAN, ADAPTIVE_GAUSSIAN, OTSU, ADAPTIVE_OTSU, SAUVOLA
          morphology-kernel-size: 3
          sharpening-strength: 1.0
          adaptive-block-size: 11
          adaptive-c: 2.0

  # OCR settings - 优化后的配置
  ocr:
    enabled: true
    languages:
      - eng
      - chi_sim
    page-segmentation-mode: 6  # SINGLE_BLOCK - 优化后的默认值，适合大多数文档图像
    adaptive-page-segmentation: true  # 启用自适应页面分割
    ocr-engine-mode: 1         # NEURAL_NETS_LSTM_ONLY - 使用LSTM引擎提高准确率
    confidence-threshold: 50   # 降低阈值以获得更多识别结果
    high-quality-threshold: 80 # 高质量识别阈值
    data-path: ""              # Empty = use system default
    preprocessing-enabled: true
    timeout-seconds: 30
    verbose-logging: false

    # 性能优化参数
    max-image-size: 2048       # 最大图像尺寸（像素）
    image-cache-enabled: true  # 启用图像缓存
    cache-size: 100           # 缓存大小（MB）
    thread-pool-size: 0       # 线程池大小，0表示使用CPU核心数

    custom-variables:
      # 优化OCR性能的自定义变量
      tessedit_pageseg_mode: "6"
      tessedit_ocr_engine_mode: "1"
      # 提高识别速度的参数
      tessedit_enable_doc_dict: "0"
      tessedit_enable_bigram_correction: "1"
      # Example: tessedit_char_whitelist: "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"

  # Performance settings
  performance:
    # Thread pool settings
    thread-pool:
      core-size: 4
      max-size: 8
      queue-capacity: 100
      keep-alive-seconds: 60
    
    # Memory management
    memory:
      max-memory-usage: 0.8  # 80% of available heap
      check-interval: 5000    # 5 seconds
  
  # Security settings
  security:
    # File path validation
    validate-file-paths: true
    
    # Prevent directory traversal attacks
    prevent-directory-traversal: true
    
    # Allowed file protocols (file, http, https, etc.)
    allowed-protocols:
      - file
    
    # Maximum number of files to process in a single operation
    max-files-per-operation: 1000

# Spring Boot specific settings
spring:
  application:
    name: doc-converter
  
  # Actuator endpoints for monitoring
  management:
    endpoints:
      web:
        exposure:
          include: health,info,metrics
    endpoint:
      health:
        show-details: when_authorized
      metrics:
        enabled: true
    
    # Health check settings
    health:
      defaults:
        enabled: true
      diskspace:
        enabled: true
      diskSpace:
        path: .
        threshold: 10MB

# Logging configuration (can be overridden by application-logging.yml)
logging:
  level:
    root: INFO
    com.talkweb.ai.converter: DEBUG
  file:
    name: ${doc-converter.logging.file:logs/application.log}
    max-size: ${doc-converter.logging.max-size:10MB}
  pattern:
    console: "%clr(%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
    file: "%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
