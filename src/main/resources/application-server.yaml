# Document Converter Web Server Configuration
# This configuration is activated when running with -server command

server:
  port: 8080
  servlet:
    context-path: /doc-converter
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
  error:
    include-message: always
    include-binding-errors: always

spring:
  profiles:
    active: server
  
  # Database Configuration (H2 in-memory for development)
  datasource:
    url: jdbc:h2:mem:docconverter;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: false
    open-in-view: false
  
  # H2 Console (for development)
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: false
  
  # Web Configuration
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 3600
  
  # Thymeleaf Configuration
  thymeleaf:
    cache: false
    prefix: classpath:/templates/
    suffix: .html
    encoding: UTF-8
    mode: HTML
  
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null

# Document Converter Web-specific Configuration
doc-converter:
  web:
    # File Storage Configuration
    upload-dir: temp/uploads
    result-dir: temp/results
    temp-cleanup-interval: 3600  # seconds
    max-file-age: 86400          # seconds (24 hours)
    
    # Task Management
    max-concurrent-tasks: 10
    task-timeout: 300            # seconds (5 minutes)
    task-cleanup-interval: 1800  # seconds (30 minutes)
    max-task-history: 1000       # maximum number of completed tasks to keep
    
    # WebSocket Configuration
    websocket:
      endpoint: /ws
      allowed-origins: "*"
      heartbeat-interval: 30     # seconds
    
    # Security Configuration
    security:
      max-upload-size: 104857600 # 100MB in bytes
      allowed-file-types:
        - pdf
        - docx
        - doc
        - xlsx
        - xls
        - pptx
        - ppt
        - html
        - htm
        - txt
        - rtf
        - odt
        - png
        - jpg
        - jpeg
        - tiff
        - bmp
        - gif
      blocked-file-types:
        - exe
        - bat
        - sh
        - cmd
        - scr
      
      # Rate Limiting
      rate-limit:
        enabled: true
        requests-per-minute: 60
        requests-per-hour: 1000
    
    # UI Configuration
    ui:
      title: "Document Converter"
      theme: "bootstrap"
      pagination:
        default-page-size: 20
        max-page-size: 100
      
      # Feature Flags
      features:
        batch-upload: true
        real-time-progress: true
        task-history: true
        system-monitoring: true

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operationsSorter: method
    tagsSorter: alpha
    displayRequestDuration: true
    displayOperationId: true
  info:
    title: "Document Converter API"
    description: "REST API for document conversion services"
    version: "1.0.0"
    contact:
      name: "Document Converter Team"
      email: "<EMAIL>"
  servers:
    - url: "http://localhost:8080/doc-converter"
      description: "Development Server"

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: when_authorized
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  health:
    defaults:
      enabled: true
    diskspace:
      enabled: true
      path: .
      threshold: 100MB
    db:
      enabled: true
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

# Logging Configuration for Web Mode
logging:
  level:
    root: INFO
    com.talkweb.ai.converter: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
  file:
    name: logs/doc-converter-server.log
    max-size: 10MB
    max-history: 10

# Performance and Monitoring
performance:
  monitoring:
    enabled: true
    metrics-collection-interval: 60  # seconds
    memory-threshold: 0.8            # 80% of max heap
    cpu-threshold: 0.8               # 80% CPU usage
  
  async:
    core-pool-size: 4
    max-pool-size: 20
    queue-capacity: 100
    thread-name-prefix: "doc-converter-async-"
    keep-alive-seconds: 60
