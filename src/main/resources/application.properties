# Logging configuration
logging.level.root=INFO
logging.level.com.talkweb.ai.converter=DEBUG
logging.level.org.springframework.ai=DEBUG

# Spring application name
spring.application.name=ai-indexer

# AI Configuration
ai.enabled=true

# Spring AI OpenAI Configuration
spring.ai.openai.api-key=${OPENAI_API_KEY:acde}
spring.ai.openai.base-url=https://api.openai.com
spring.ai.openai.chat.options.model=gpt-3.5-turbo
spring.ai.openai.chat.options.temperature=0.7
spring.ai.openai.chat.options.max-tokens=2000
spring.ai.openai.embedding.options.model=text-embedding-ada-002