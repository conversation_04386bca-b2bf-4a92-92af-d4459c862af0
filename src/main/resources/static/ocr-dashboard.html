<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR 系统监控仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
        }

        .metric-label {
            font-weight: 500;
            color: #666;
        }

        .metric-value {
            font-weight: bold;
            font-size: 1.1em;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-critical { background-color: #F44336; }
        .status-down { background-color: #9E9E9E; }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
        }

        .chart-container {
            grid-column: 1 / -1;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background-color: #5a6fd8;
        }

        .btn-danger {
            background-color: #F44336;
            color: white;
        }

        .btn-danger:hover {
            background-color: #d32f2f;
        }

        .timestamp {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }

        .error-message {
            background-color: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            border-left: 4px solid #f44336;
        }

        .success-message {
            background-color: #e8f5e8;
            color: #2e7d32;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            border-left: 4px solid #4caf50;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>OCR 系统监控仪表板</h1>
        <div class="subtitle">实时监控 OCR 处理性能和系统状态</div>
    </div>

    <div class="container">
        <div class="controls">
            <button class="btn btn-primary" onclick="refreshData()">刷新数据</button>
            <button class="btn btn-primary" onclick="toggleAutoRefresh()">
                <span id="autoRefreshText">开启自动刷新</span>
            </button>
            <button class="btn btn-danger" onclick="clearCache()">清空缓存</button>
            <button class="btn btn-danger" onclick="resetMetrics()">重置指标</button>
        </div>

        <div id="errorContainer"></div>

        <div class="dashboard-grid">
            <!-- 系统状态卡片 -->
            <div class="card">
                <div class="card-title">系统状态</div>
                <div id="systemStatus" class="loading">加载中...</div>
            </div>

            <!-- 性能指标卡片 -->
            <div class="card">
                <div class="card-title">性能指标</div>
                <div id="performanceMetrics" class="loading">加载中...</div>
            </div>

            <!-- 线程池状态卡片 -->
            <div class="card">
                <div class="card-title">线程池状态</div>
                <div id="threadPoolStatus" class="loading">加载中...</div>
            </div>

            <!-- 缓存状态卡片 -->
            <div class="card">
                <div class="card-title">缓存状态</div>
                <div id="cacheStatus" class="loading">加载中...</div>
            </div>
        </div>

        <div class="timestamp" id="lastUpdate">最后更新: --</div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefreshEnabled = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
        });

        // 刷新数据
        async function refreshData() {
            try {
                showError('');
                await Promise.all([
                    updateSystemStatus(),
                    updatePerformanceMetrics(),
                    updateThreadPoolStatus(),
                    updateCacheStatus()
                ]);
                updateTimestamp();
            } catch (error) {
                showError('获取数据失败: ' + error.message);
            }
        }

        // 更新系统状态
        async function updateSystemStatus() {
            try {
                const response = await fetch('/api/ocr/monitoring/status');
                const data = await response.json();
                
                const container = document.getElementById('systemStatus');
                container.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">
                            <span class="status-indicator status-${getStatusClass(data.health)}"></span>
                            系统健康状态
                        </span>
                        <span class="metric-value">${data.health || 'UNKNOWN'}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">服务可用性</span>
                        <span class="metric-value">${data.available ? '可用' : '不可用'}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">活跃处理数</span>
                        <span class="metric-value">${data.activeContexts || 0}</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('systemStatus').innerHTML = '<div class="error-message">加载失败</div>';
            }
        }

        // 更新性能指标
        async function updatePerformanceMetrics() {
            try {
                const response = await fetch('/api/ocr/monitoring/metrics');
                const data = await response.json();
                
                const container = document.getElementById('performanceMetrics');
                container.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">总处理数</span>
                        <span class="metric-value">${data.totalProcessed || 0}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">成功率</span>
                        <span class="metric-value">${(data.successRate * 100).toFixed(1)}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${data.successRate * 100}%"></div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">错误率</span>
                        <span class="metric-value">${(data.errorRate * 100).toFixed(1)}%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">平均处理时间</span>
                        <span class="metric-value">${data.avgProcessingTimeMs?.toFixed(0) || 0} ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">吞吐量</span>
                        <span class="metric-value">${data.throughput?.toFixed(2) || 0} req/s</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('performanceMetrics').innerHTML = '<div class="error-message">加载失败</div>';
            }
        }

        // 更新线程池状态
        async function updateThreadPoolStatus() {
            try {
                const response = await fetch('/api/ocr/monitoring/status');
                const data = await response.json();
                
                if (data.threadPool) {
                    const container = document.getElementById('threadPoolStatus');
                    const utilization = (data.threadPool.activeThreads / data.threadPool.totalThreads) * 100;
                    
                    container.innerHTML = `
                        <div class="metric">
                            <span class="metric-label">活跃线程</span>
                            <span class="metric-value">${data.threadPool.activeThreads}/${data.threadPool.totalThreads}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${utilization}%"></div>
                        </div>
                        <div class="metric">
                            <span class="metric-label">队列大小</span>
                            <span class="metric-value">${data.threadPool.queueSize}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">已完成任务</span>
                            <span class="metric-value">${data.threadPool.completedTasks}</span>
                        </div>
                    `;
                } else {
                    document.getElementById('threadPoolStatus').innerHTML = '<div class="metric">服务不可用</div>';
                }
            } catch (error) {
                document.getElementById('threadPoolStatus').innerHTML = '<div class="error-message">加载失败</div>';
            }
        }

        // 更新缓存状态
        async function updateCacheStatus() {
            try {
                const response = await fetch('/api/ocr/monitoring/status');
                const data = await response.json();
                
                if (data.cache) {
                    const container = document.getElementById('cacheStatus');
                    const utilization = (data.cache.size / data.cache.maxSize) * 100;
                    
                    container.innerHTML = `
                        <div class="metric">
                            <span class="metric-label">缓存大小</span>
                            <span class="metric-value">${data.cache.size}/${data.cache.maxSize}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${utilization}%"></div>
                        </div>
                        <div class="metric">
                            <span class="metric-label">命中率</span>
                            <span class="metric-value">${(data.cache.hitRate * 100).toFixed(1)}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">命中次数</span>
                            <span class="metric-value">${data.cache.hitCount}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">未命中次数</span>
                            <span class="metric-value">${data.cache.missCount}</span>
                        </div>
                    `;
                } else {
                    document.getElementById('cacheStatus').innerHTML = '<div class="metric">服务不可用</div>';
                }
            } catch (error) {
                document.getElementById('cacheStatus').innerHTML = '<div class="error-message">加载失败</div>';
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            if (isAutoRefreshEnabled) {
                clearInterval(autoRefreshInterval);
                isAutoRefreshEnabled = false;
                document.getElementById('autoRefreshText').textContent = '开启自动刷新';
            } else {
                autoRefreshInterval = setInterval(refreshData, 5000); // 每5秒刷新
                isAutoRefreshEnabled = true;
                document.getElementById('autoRefreshText').textContent = '关闭自动刷新';
            }
        }

        // 清空缓存
        async function clearCache() {
            try {
                const response = await fetch('/api/ocr/monitoring/cache/clear', { method: 'POST' });
                const data = await response.json();
                if (response.ok) {
                    showError('缓存已清空', 'success');
                    refreshData();
                } else {
                    showError('清空缓存失败: ' + data.error);
                }
            } catch (error) {
                showError('清空缓存失败: ' + error.message);
            }
        }

        // 重置指标
        async function resetMetrics() {
            try {
                const response = await fetch('/api/ocr/monitoring/metrics/reset', { method: 'POST' });
                const data = await response.json();
                if (response.ok) {
                    showError('指标已重置', 'success');
                    refreshData();
                } else {
                    showError('重置指标失败: ' + data.error);
                }
            } catch (error) {
                showError('重置指标失败: ' + error.message);
            }
        }

        // 显示错误信息
        function showError(message, type = 'error') {
            const container = document.getElementById('errorContainer');
            if (message) {
                const className = type === 'success' ? 'success-message' : 'error-message';
                container.innerHTML = `<div class="${className}">${message}</div>`;
                setTimeout(() => container.innerHTML = '', 5000);
            } else {
                container.innerHTML = '';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 'HEALTHY': return 'healthy';
                case 'WARNING': return 'warning';
                case 'CRITICAL': return 'critical';
                case 'DOWN': return 'down';
                default: return 'down';
            }
        }

        // 更新时间戳
        function updateTimestamp() {
            const now = new Date();
            document.getElementById('lastUpdate').textContent = 
                `最后更新: ${now.toLocaleString('zh-CN')}`;
        }
    </script>
</body>
</html>
