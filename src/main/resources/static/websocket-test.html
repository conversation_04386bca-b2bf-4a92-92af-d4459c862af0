<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test - Document Converter</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .status-connecting { background-color: #ffc107; }
        
        .message-log {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .task-progress {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        
        .progress-bar-container {
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>WebSocket Test Page</h1>
        
        <!-- Connection Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Connection Status</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <span id="status-indicator" class="status-indicator status-disconnected"></span>
                    <span id="connection-status">Disconnected</span>
                    <button id="connect-btn" class="btn btn-success btn-sm ms-3">Connect</button>
                    <button id="disconnect-btn" class="btn btn-danger btn-sm ms-2" disabled>Disconnect</button>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <small class="text-muted">Reconnect Attempts:</small>
                        <div id="reconnect-attempts">0</div>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">Active Subscriptions:</small>
                        <div id="active-subscriptions">0</div>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">Messages Received:</small>
                        <div id="messages-received">0</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Task Subscription -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Task Subscription</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <input type="text" id="task-id-input" class="form-control" placeholder="Enter Task ID">
                    </div>
                    <div class="col-md-4">
                        <button id="subscribe-btn" class="btn btn-primary me-2">Subscribe</button>
                        <button id="unsubscribe-btn" class="btn btn-secondary">Unsubscribe</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Task Progress Display -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Task Progress</h5>
            </div>
            <div class="card-body">
                <div id="task-progress-container">
                    <p class="text-muted">No active tasks</p>
                </div>
            </div>
        </div>
        
        <!-- Test Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Test Actions</h5>
            </div>
            <div class="card-body">
                <button id="send-ping" class="btn btn-info me-2">Send Ping</button>
                <button id="test-notification" class="btn btn-warning me-2">Test Notification</button>
                <button id="clear-log" class="btn btn-secondary">Clear Log</button>
            </div>
        </div>
        
        <!-- Message Log -->
        <div class="card">
            <div class="card-header">
                <h5>Message Log</h5>
            </div>
            <div class="card-body">
                <div id="message-log" class="message-log"></div>
            </div>
        </div>
        
        <!-- Notifications Container -->
        <div id="notifications" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
    </div>

    <script src="/js/websocket-client.js"></script>
    <script>
        let messageCount = 0;
        const messageLog = document.getElementById('message-log');
        const statusIndicator = document.getElementById('status-indicator');
        const connectionStatus = document.getElementById('connection-status');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const taskProgressContainer = document.getElementById('task-progress-container');
        
        // Log function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `text-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            messageLog.appendChild(logEntry);
            messageLog.scrollTop = messageLog.scrollHeight;
        }
        
        // Update connection status
        function updateConnectionStatus(connected, attempts = 0) {
            if (connected) {
                statusIndicator.className = 'status-indicator status-connected';
                connectionStatus.textContent = 'Connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                connectionStatus.textContent = 'Disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
            
            document.getElementById('reconnect-attempts').textContent = attempts;
            document.getElementById('messages-received').textContent = messageCount;
            
            const status = window.docConverterWS.getStatus();
            document.getElementById('active-subscriptions').textContent = status.subscriptions.length;
        }
        
        // WebSocket event listeners
        document.addEventListener('websocket-connected', (event) => {
            log('WebSocket connected', 'success');
            updateConnectionStatus(true);
        });
        
        document.addEventListener('websocket-disconnected', (event) => {
            log('WebSocket disconnected', 'warning');
            updateConnectionStatus(false);
        });
        
        document.addEventListener('websocket-error', (event) => {
            log('WebSocket error: ' + JSON.stringify(event.detail), 'danger');
        });
        
        document.addEventListener('websocket-taskProgress', (event) => {
            messageCount++;
            log('Task progress: ' + JSON.stringify(event.detail), 'info');
            updateTaskProgress(event.detail);
            updateConnectionStatus(window.docConverterWS.isConnected);
        });
        
        document.addEventListener('websocket-systemNotification', (event) => {
            messageCount++;
            log('System notification: ' + JSON.stringify(event.detail), 'primary');
            updateConnectionStatus(window.docConverterWS.isConnected);
        });
        
        // Update task progress display
        function updateTaskProgress(progressData) {
            let taskElement = document.querySelector(`[data-task-id="${progressData.taskId}"]`);
            
            if (!taskElement) {
                taskElement = document.createElement('div');
                taskElement.className = 'task-progress';
                taskElement.setAttribute('data-task-id', progressData.taskId);
                taskElement.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <strong>Task: ${progressData.taskId}</strong>
                        <span class="task-status badge">-</span>
                    </div>
                    <div class="task-message text-muted">-</div>
                    <div class="current-step text-muted small">-</div>
                    <div class="progress-bar-container">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%">0%</div>
                        </div>
                    </div>
                `;
                
                if (taskProgressContainer.querySelector('p')) {
                    taskProgressContainer.innerHTML = '';
                }
                taskProgressContainer.appendChild(taskElement);
            }
            
            // Update elements (this will be handled by the WebSocket client)
        }
        
        // Button event listeners
        connectBtn.addEventListener('click', () => {
            window.docConverterWS.connect();
        });
        
        disconnectBtn.addEventListener('click', () => {
            window.docConverterWS.disconnect();
            updateConnectionStatus(false);
        });
        
        document.getElementById('subscribe-btn').addEventListener('click', () => {
            const taskId = document.getElementById('task-id-input').value.trim();
            if (taskId) {
                window.docConverterWS.subscribeToTask(taskId);
                log(`Subscribed to task: ${taskId}`, 'success');
                updateConnectionStatus(window.docConverterWS.isConnected);
            }
        });
        
        document.getElementById('unsubscribe-btn').addEventListener('click', () => {
            const taskId = document.getElementById('task-id-input').value.trim();
            if (taskId) {
                window.docConverterWS.unsubscribeFromTask(taskId);
                log(`Unsubscribed from task: ${taskId}`, 'warning');
                updateConnectionStatus(window.docConverterWS.isConnected);
            }
        });
        
        document.getElementById('send-ping').addEventListener('click', () => {
            window.docConverterWS.sendMessage('PING', null);
            log('Sent PING message', 'info');
        });
        
        document.getElementById('test-notification').addEventListener('click', () => {
            fetch('/api/v1/websocket/test', { method: 'POST' })
                .then(response => response.text())
                .then(data => log('Test notification sent: ' + data, 'success'))
                .catch(error => log('Error sending test notification: ' + error, 'danger'));
        });
        
        document.getElementById('clear-log').addEventListener('click', () => {
            messageLog.innerHTML = '';
            messageCount = 0;
            updateConnectionStatus(window.docConverterWS.isConnected);
        });
        
        // Initialize
        updateConnectionStatus(false);
        log('WebSocket test page loaded', 'info');
    </script>
</body>
</html>
