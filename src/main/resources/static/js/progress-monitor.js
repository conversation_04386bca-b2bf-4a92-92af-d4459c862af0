/**
 * Progress Monitor Component
 * Handles real-time progress updates, notifications, and animations
 */

class ProgressMonitor {
    constructor() {
        this.activeMonitors = new Map();
        this.notificationQueue = [];
        this.isNotificationVisible = false;
        this.animationDuration = 300;
        
        this.init();
    }

    init() {
        this.createNotificationContainer();
        this.setupWebSocketListeners();
        this.setupGlobalStyles();
    }

    createNotificationContainer() {
        // Create notification container if it doesn't exist
        if (!document.getElementById('progress-notifications')) {
            const container = document.createElement('div');
            container.id = 'progress-notifications';
            container.className = 'progress-notifications-container';
            document.body.appendChild(container);
        }
    }

    setupGlobalStyles() {
        // Add CSS styles for progress monitoring
        const style = document.createElement('style');
        style.textContent = `
            .progress-notifications-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1060;
                max-width: 400px;
            }
            
            .progress-notification {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                margin-bottom: 10px;
                padding: 1rem;
                transform: translateX(100%);
                transition: all 0.3s ease;
                opacity: 0;
            }
            
            .progress-notification.show {
                transform: translateX(0);
                opacity: 1;
            }
            
            .progress-notification.success {
                border-left: 4px solid #28a745;
            }
            
            .progress-notification.error {
                border-left: 4px solid #dc3545;
            }
            
            .progress-notification.info {
                border-left: 4px solid #007bff;
            }
            
            .progress-notification.warning {
                border-left: 4px solid #ffc107;
            }
            
            .progress-notification-header {
                display: flex;
                justify-content-between;
                align-items: center;
                margin-bottom: 0.5rem;
            }
            
            .progress-notification-title {
                font-weight: 600;
                margin: 0;
                font-size: 0.9rem;
            }
            
            .progress-notification-close {
                background: none;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                color: #6c757d;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .progress-notification-body {
                font-size: 0.85rem;
                color: #6c757d;
                margin-bottom: 0.75rem;
            }
            
            .progress-notification-progress {
                margin-bottom: 0.5rem;
            }
            
            .progress-notification-progress .progress {
                height: 6px;
                border-radius: 3px;
            }
            
            .progress-notification-meta {
                display: flex;
                justify-content: between;
                align-items: center;
                font-size: 0.75rem;
                color: #6c757d;
            }
            
            .progress-pulse {
                animation: progressPulse 2s infinite;
            }
            
            @keyframes progressPulse {
                0% { opacity: 1; }
                50% { opacity: 0.7; }
                100% { opacity: 1; }
            }
            
            .progress-glow {
                box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
                animation: progressGlow 1.5s ease-in-out infinite alternate;
            }
            
            @keyframes progressGlow {
                from { box-shadow: 0 0 5px rgba(0, 123, 255, 0.3); }
                to { box-shadow: 0 0 15px rgba(0, 123, 255, 0.7); }
            }
            
            .progress-bounce {
                animation: progressBounce 0.6s ease;
            }
            
            @keyframes progressBounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                60% { transform: translateY(-5px); }
            }
        `;
        document.head.appendChild(style);
    }

    setupWebSocketListeners() {
        if (window.docConverterWS) {
            // Listen for task progress updates
            window.docConverterWS.onMessageType('TASK_PROGRESS_UPDATE', (data) => {
                this.handleProgressUpdate(data);
            });

            // Listen for system notifications
            window.docConverterWS.onMessageType('SYSTEM_NOTIFICATION', (data) => {
                this.handleSystemNotification(data);
            });
        }
    }

    /**
     * Create a progress monitor for a specific task
     */
    createTaskMonitor(taskId, options = {}) {
        const monitor = {
            taskId: taskId,
            element: null,
            options: {
                showNotifications: true,
                autoHide: true,
                hideDelay: 5000,
                showProgress: true,
                showSteps: true,
                ...options
            },
            lastUpdate: Date.now()
        };

        this.activeMonitors.set(taskId, monitor);
        
        if (monitor.options.showNotifications) {
            this.showTaskStartNotification(taskId);
        }

        return monitor;
    }

    /**
     * Update progress for a specific task
     */
    updateTaskProgress(taskId, progressData) {
        const monitor = this.activeMonitors.get(taskId);
        if (!monitor) return;

        monitor.lastUpdate = Date.now();

        // Update any existing progress elements
        this.updateProgressElements(taskId, progressData);

        // Show notification if enabled
        if (monitor.options.showNotifications) {
            this.showProgressNotification(taskId, progressData);
        }

        // Trigger custom events
        this.triggerProgressEvent(taskId, progressData);
    }

    /**
     * Handle WebSocket progress updates
     */
    handleProgressUpdate(data) {
        const taskId = data.taskId;
        
        // Create monitor if it doesn't exist
        if (!this.activeMonitors.has(taskId)) {
            this.createTaskMonitor(taskId);
        }

        this.updateTaskProgress(taskId, data);
    }

    /**
     * Handle system notifications
     */
    handleSystemNotification(data) {
        this.showSystemNotification(data);
    }

    /**
     * Update all progress elements for a task
     */
    updateProgressElements(taskId, progressData) {
        // Update progress bars
        const progressBars = document.querySelectorAll(`[data-task-id="${taskId}"] .progress-bar`);
        progressBars.forEach(bar => {
            if (progressData.progress !== null) {
                bar.style.width = `${progressData.progress}%`;
                bar.setAttribute('aria-valuenow', progressData.progress);
                
                // Add animation classes
                bar.classList.add('progress-pulse');
                setTimeout(() => bar.classList.remove('progress-pulse'), 1000);
            }
            
            // Update color based on status
            bar.className = `progress-bar ${this.getProgressBarClass(progressData.status)}`;
        });

        // Update status badges
        const statusBadges = document.querySelectorAll(`[data-task-id="${taskId}"] .task-status, [data-task-id="${taskId}"] .status-badge`);
        statusBadges.forEach(badge => {
            badge.className = `badge ${this.getStatusClass(progressData.status)} status-badge`;
            badge.innerHTML = `<i class="${this.getStatusIcon(progressData.status)} me-1"></i>${progressData.status}`;
        });

        // Update message elements
        const messageElements = document.querySelectorAll(`[data-task-id="${taskId}"] .task-message`);
        messageElements.forEach(element => {
            if (progressData.message) {
                element.textContent = progressData.message;
            }
        });

        // Update step elements
        const stepElements = document.querySelectorAll(`[data-task-id="${taskId}"] .current-step`);
        stepElements.forEach(element => {
            if (progressData.currentStep) {
                element.textContent = progressData.currentStep;
            }
        });

        // Add bounce animation to task cards
        const taskCards = document.querySelectorAll(`[data-task-id="${taskId}"]`);
        taskCards.forEach(card => {
            card.classList.add('progress-bounce');
            setTimeout(() => card.classList.remove('progress-bounce'), 600);
        });
    }

    /**
     * Show task start notification
     */
    showTaskStartNotification(taskId) {
        this.showNotification({
            type: 'info',
            title: 'Task Started',
            message: `Conversion task ${taskId.substring(0, 8)}... has started`,
            duration: 3000
        });
    }

    /**
     * Show progress notification
     */
    showProgressNotification(taskId, progressData) {
        const { status, progress, message } = progressData;
        
        let notificationType = 'info';
        let title = 'Task Progress';
        let notificationMessage = message || 'Processing...';

        switch (status) {
            case 'COMPLETED':
                notificationType = 'success';
                title = 'Task Completed';
                notificationMessage = 'Conversion completed successfully!';
                break;
            case 'FAILED':
                notificationType = 'error';
                title = 'Task Failed';
                notificationMessage = message || 'Conversion failed';
                break;
            case 'CANCELLED':
                notificationType = 'warning';
                title = 'Task Cancelled';
                notificationMessage = 'Task was cancelled';
                break;
        }

        // Only show notification for significant progress changes or status changes
        if (status === 'COMPLETED' || status === 'FAILED' || status === 'CANCELLED' || 
            (progress && progress % 25 === 0)) {
            
            this.showNotification({
                type: notificationType,
                title: title,
                message: notificationMessage,
                taskId: taskId,
                progress: progress,
                duration: status === 'PROCESSING' ? 2000 : 5000
            });
        }
    }

    /**
     * Show system notification
     */
    showSystemNotification(data) {
        this.showNotification({
            type: 'info',
            title: 'System Notification',
            message: JSON.stringify(data.data),
            duration: 4000
        });
    }

    /**
     * Show a notification
     */
    showNotification(options) {
        const notification = this.createNotificationElement(options);
        const container = document.getElementById('progress-notifications');
        
        container.appendChild(notification);
        
        // Trigger show animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Auto-hide notification
        if (options.duration) {
            setTimeout(() => {
                this.hideNotification(notification);
            }, options.duration);
        }

        return notification;
    }

    /**
     * Create notification element
     */
    createNotificationElement(options) {
        const notification = document.createElement('div');
        notification.className = `progress-notification ${options.type}`;
        
        let progressHTML = '';
        if (options.progress !== undefined) {
            progressHTML = `
                <div class="progress-notification-progress">
                    <div class="progress">
                        <div class="progress-bar ${this.getProgressBarClass('PROCESSING')}" 
                             style="width: ${options.progress}%"></div>
                    </div>
                </div>
            `;
        }

        notification.innerHTML = `
            <div class="progress-notification-header">
                <h6 class="progress-notification-title">
                    <i class="${this.getNotificationIcon(options.type)} me-2"></i>
                    ${options.title}
                </h6>
                <button class="progress-notification-close" onclick="progressMonitor.hideNotification(this.closest('.progress-notification'))">
                    ×
                </button>
            </div>
            <div class="progress-notification-body">
                ${options.message}
            </div>
            ${progressHTML}
            <div class="progress-notification-meta">
                <span>${new Date().toLocaleTimeString()}</span>
                ${options.taskId ? `<span>Task: ${options.taskId.substring(0, 8)}...</span>` : ''}
            </div>
        `;

        return notification;
    }

    /**
     * Hide notification
     */
    hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, this.animationDuration);
    }

    /**
     * Trigger custom progress events
     */
    triggerProgressEvent(taskId, progressData) {
        const event = new CustomEvent('taskProgressUpdate', {
            detail: { taskId, progressData }
        });
        document.dispatchEvent(event);
    }

    /**
     * Clean up completed monitors
     */
    cleanup() {
        const now = Date.now();
        const maxAge = 5 * 60 * 1000; // 5 minutes

        for (const [taskId, monitor] of this.activeMonitors.entries()) {
            if (now - monitor.lastUpdate > maxAge) {
                this.activeMonitors.delete(taskId);
            }
        }
    }

    // Utility methods
    getStatusClass(status) {
        switch (status) {
            case 'COMPLETED': return 'bg-success';
            case 'PROCESSING': return 'bg-primary';
            case 'FAILED': return 'bg-danger';
            case 'CANCELLED': return 'bg-secondary';
            default: return 'bg-warning';
        }
    }

    getStatusIcon(status) {
        switch (status) {
            case 'COMPLETED': return 'fas fa-check';
            case 'PROCESSING': return 'fas fa-spinner fa-spin';
            case 'FAILED': return 'fas fa-times';
            case 'CANCELLED': return 'fas fa-ban';
            default: return 'fas fa-clock';
        }
    }

    getProgressBarClass(status) {
        switch (status) {
            case 'COMPLETED': return 'bg-success progress-glow';
            case 'PROCESSING': return 'bg-primary';
            case 'FAILED': return 'bg-danger';
            default: return 'bg-warning';
        }
    }

    getNotificationIcon(type) {
        switch (type) {
            case 'success': return 'fas fa-check-circle';
            case 'error': return 'fas fa-exclamation-circle';
            case 'warning': return 'fas fa-exclamation-triangle';
            default: return 'fas fa-info-circle';
        }
    }

    // Public API methods
    subscribeToTask(taskId, options = {}) {
        const monitor = this.createTaskMonitor(taskId, options);
        
        // Subscribe via WebSocket if available
        if (window.docConverterWS) {
            window.docConverterWS.subscribeToTask(taskId);
        }
        
        return monitor;
    }

    unsubscribeFromTask(taskId) {
        this.activeMonitors.delete(taskId);
        
        // Unsubscribe via WebSocket if available
        if (window.docConverterWS) {
            window.docConverterWS.unsubscribeFromTask(taskId);
        }
    }

    showCustomNotification(title, message, type = 'info', duration = 5000) {
        return this.showNotification({
            type: type,
            title: title,
            message: message,
            duration: duration
        });
    }
}

// Initialize progress monitor when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.progressMonitor = new ProgressMonitor();
    
    // Clean up old monitors every 5 minutes
    setInterval(() => {
        window.progressMonitor.cleanup();
    }, 5 * 60 * 1000);
});
