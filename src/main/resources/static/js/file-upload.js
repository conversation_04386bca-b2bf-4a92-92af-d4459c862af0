/**
 * File Upload JavaScript
 * Handles drag & drop file upload, progress tracking, and validation
 */

class FileUploader {
    constructor() {
        this.selectedFile = null;
        this.uploadInProgress = false;
        this.maxFileSize = 100 * 1024 * 1024; // 100MB
        this.supportedFormats = [
            'pdf', 'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt',
            'html', 'htm', 'txt', 'rtf', 'odt',
            'png', 'jpg', 'jpeg', 'tiff', 'tif', 'bmp', 'gif'
        ];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadRecentUploads();
        this.setupWebSocket();
    }

    setupEventListeners() {
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');

        // Click to upload
        uploadArea.addEventListener('click', () => {
            if (!this.uploadInProgress) {
                fileInput.click();
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelect(e.target.files[0]);
            }
        });

        // Drag and drop events
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            if (!this.uploadInProgress && e.dataTransfer.files.length > 0) {
                this.handleFileSelect(e.dataTransfer.files[0]);
            }
        });

        // Prevent default drag behaviors on document
        document.addEventListener('dragover', (e) => e.preventDefault());
        document.addEventListener('drop', (e) => e.preventDefault());
    }

    setupWebSocket() {
        if (window.docConverterWS) {
            // Listen for task progress updates
            window.docConverterWS.onMessageType('TASK_PROGRESS_UPDATE', (data) => {
                this.handleTaskUpdate(data);
            });
        }
    }

    handleFileSelect(file) {
        // Validate file
        const validation = this.validateFile(file);
        if (!validation.valid) {
            this.showError(validation.message);
            return;
        }

        this.selectedFile = file;
        this.showFilePreview(file);
    }

    validateFile(file) {
        // Check file size
        if (file.size > this.maxFileSize) {
            return {
                valid: false,
                message: `File size (${this.formatFileSize(file.size)}) exceeds the maximum limit of ${this.formatFileSize(this.maxFileSize)}.`
            };
        }

        // Check file type
        const extension = this.getFileExtension(file.name).toLowerCase();
        if (!this.supportedFormats.includes(extension)) {
            return {
                valid: false,
                message: `File type "${extension}" is not supported. Please upload a supported file format.`
            };
        }

        return { valid: true };
    }

    showFilePreview(file) {
        const uploadArea = document.getElementById('upload-area');
        const filePreview = document.getElementById('file-preview');
        const conversionOptions = document.getElementById('conversion-options');
        
        // Hide upload area and show preview
        uploadArea.style.display = 'none';
        filePreview.style.display = 'block';
        conversionOptions.style.display = 'block';

        // Update file info
        document.getElementById('file-name').textContent = file.name;
        document.getElementById('file-size').textContent = this.formatFileSize(file.size);
        
        // Update file icon
        const fileIcon = document.getElementById('file-icon');
        fileIcon.className = `fas ${this.getFileIcon(file.name)} file-icon text-primary`;

        // Set default options based on file type
        this.setDefaultOptions(file);
    }

    setDefaultOptions(file) {
        const extension = this.getFileExtension(file.name).toLowerCase();
        const outputFormat = document.getElementById('output-format');
        const ocrEnabled = document.getElementById('ocr-enabled');

        // Set default output format
        outputFormat.value = 'markdown';

        // Enable OCR for image files
        if (['png', 'jpg', 'jpeg', 'tiff', 'tif', 'bmp', 'gif'].includes(extension)) {
            ocrEnabled.checked = true;
            ocrEnabled.closest('.form-check').style.display = 'block';
        } else {
            ocrEnabled.checked = false;
            ocrEnabled.closest('.form-check').style.display = 'none';
        }
    }

    async startUpload() {
        if (!this.selectedFile || this.uploadInProgress) {
            return;
        }

        this.uploadInProgress = true;
        this.showProgress(true);

        try {
            // Prepare form data
            const formData = new FormData();
            formData.append('file', this.selectedFile);
            
            // Add conversion options
            const options = this.getConversionOptions();
            Object.keys(options).forEach(key => {
                formData.append(key, options[key]);
            });

            // Upload file with progress tracking
            const response = await this.uploadWithProgress(formData);
            const result = await response.json();

            if (result.success) {
                this.handleUploadSuccess(result.data);
            } else {
                this.showError('Upload failed: ' + result.message);
            }

        } catch (error) {
            console.error('Upload error:', error);
            this.showError('Upload failed: ' + error.message);
        } finally {
            this.uploadInProgress = false;
        }
    }

    uploadWithProgress(formData) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            // Track upload progress
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    this.updateProgress(percentComplete, 'Uploading...');
                }
            });

            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    resolve(xhr);
                } else {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new Error('Network error occurred'));
            });

            xhr.open('POST', '/api/v1/files/upload');
            xhr.send(formData);
        });
    }

    getConversionOptions() {
        return {
            outputFormat: document.getElementById('output-format').value,
            qualitySetting: document.getElementById('quality-setting').value,
            preserveFormatting: document.getElementById('preserve-formatting').checked,
            extractImages: document.getElementById('extract-images').checked,
            ocrEnabled: document.getElementById('ocr-enabled').checked
        };
    }

    handleUploadSuccess(taskData) {
        // Show success modal
        const modal = new bootstrap.Modal(document.getElementById('successModal'));
        document.getElementById('success-filename').textContent = this.selectedFile.name;
        document.getElementById('success-task-id').textContent = taskData.id;
        modal.show();

        // Subscribe to task updates via WebSocket
        if (window.docConverterWS) {
            window.docConverterWS.subscribeToTask(taskData.id);
        }

        // Add to recent uploads
        this.addToRecentUploads(taskData);

        // Reset form after a delay
        setTimeout(() => {
            this.resetForm();
        }, 2000);
    }

    handleTaskUpdate(taskData) {
        // Update progress if this is our current upload
        if (this.selectedFile && taskData.fileName === this.selectedFile.name) {
            const progress = taskData.progress || 0;
            const status = taskData.status;
            const message = taskData.message || 'Processing...';

            if (status === 'PROCESSING') {
                this.updateProgress(progress, message);
            } else if (status === 'COMPLETED') {
                this.updateProgress(100, 'Conversion completed!');
                setTimeout(() => this.resetForm(), 3000);
            } else if (status === 'FAILED') {
                this.showError('Conversion failed: ' + message);
            }
        }

        // Update recent uploads
        this.updateRecentUpload(taskData);
    }

    showProgress(show) {
        const progressContainer = document.getElementById('progress-container');
        const uploadBtn = document.getElementById('upload-btn');
        
        progressContainer.style.display = show ? 'block' : 'none';
        uploadBtn.disabled = show;
        
        if (show) {
            this.updateProgress(0, 'Preparing upload...');
        }
    }

    updateProgress(percent, status) {
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        const uploadStatus = document.getElementById('upload-status');

        progressBar.style.width = `${percent}%`;
        progressText.textContent = `${Math.round(percent)}%`;
        uploadStatus.textContent = status;

        // Update progress bar color based on status
        if (percent === 100) {
            progressBar.className = 'progress-bar bg-success';
        } else if (percent > 0) {
            progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-primary';
        }
    }

    resetForm() {
        this.selectedFile = null;
        this.uploadInProgress = false;

        // Reset UI
        document.getElementById('upload-area').style.display = 'flex';
        document.getElementById('file-preview').style.display = 'none';
        document.getElementById('file-input').value = '';
        
        this.showProgress(false);
    }

    async loadRecentUploads() {
        try {
            const response = await fetch('/api/v1/tasks?size=5&sort=createdAt,desc');
            const data = await response.json();
            
            if (data.success) {
                this.displayRecentUploads(data.data || []);
            }
        } catch (error) {
            console.error('Error loading recent uploads:', error);
        }
    }

    displayRecentUploads(tasks) {
        const container = document.getElementById('recent-uploads');
        
        if (tasks.length === 0) {
            container.innerHTML = '<p class="text-muted">No recent uploads</p>';
            return;
        }

        container.innerHTML = tasks.map(task => this.createRecentUploadItem(task)).join('');
    }

    createRecentUploadItem(task) {
        const statusClass = this.getStatusClass(task.status);
        const statusIcon = this.getStatusIcon(task.status);

        return `
            <div class="history-item" data-task-id="${task.id}">
                <div class="d-flex align-items-center">
                    <i class="fas ${this.getFileIcon(task.fileName)} text-primary me-2"></i>
                    <div>
                        <div class="fw-bold">${this.truncateText(task.fileName || 'Unknown', 20)}</div>
                        <small class="text-muted">${this.formatDate(task.createdAt)}</small>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <span class="badge ${statusClass} me-2">
                        <i class="${statusIcon} me-1"></i>
                        ${task.status}
                    </span>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewTask('${task.id}')" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${task.status === 'COMPLETED' ? 
                            `<button class="btn btn-outline-success" onclick="downloadResult('${task.id}')" title="Download">
                                <i class="fas fa-download"></i>
                            </button>` : ''
                        }
                    </div>
                </div>
            </div>
        `;
    }

    addToRecentUploads(taskData) {
        const container = document.getElementById('recent-uploads');
        const newItem = this.createRecentUploadItem(taskData);
        
        // Add to top of list
        container.insertAdjacentHTML('afterbegin', newItem);
        
        // Remove excess items (keep only 5)
        const items = container.querySelectorAll('.history-item');
        if (items.length > 5) {
            items[items.length - 1].remove();
        }
    }

    updateRecentUpload(taskData) {
        const item = document.querySelector(`[data-task-id="${taskData.taskId}"]`);
        if (item) {
            // Update status badge
            const badge = item.querySelector('.badge');
            if (badge) {
                badge.className = `badge ${this.getStatusClass(taskData.status)} me-2`;
                badge.innerHTML = `<i class="${this.getStatusIcon(taskData.status)} me-1"></i>${taskData.status}`;
            }

            // Add download button if completed
            if (taskData.status === 'COMPLETED') {
                const btnGroup = item.querySelector('.btn-group');
                if (btnGroup && !btnGroup.querySelector('.btn-outline-success')) {
                    btnGroup.insertAdjacentHTML('beforeend', `
                        <button class="btn btn-outline-success" onclick="downloadResult('${taskData.taskId}')" title="Download">
                            <i class="fas fa-download"></i>
                        </button>
                    `);
                }
            }
        }
    }

    // Utility methods
    getFileExtension(filename) {
        return filename.split('.').pop() || '';
    }

    getFileIcon(filename) {
        const extension = this.getFileExtension(filename).toLowerCase();
        
        switch (extension) {
            case 'pdf': return 'fa-file-pdf';
            case 'docx':
            case 'doc': return 'fa-file-word';
            case 'xlsx':
            case 'xls': return 'fa-file-excel';
            case 'pptx':
            case 'ppt': return 'fa-file-powerpoint';
            case 'html':
            case 'htm': return 'fa-file-code';
            case 'txt': return 'fa-file-alt';
            case 'png':
            case 'jpg':
            case 'jpeg':
            case 'gif':
            case 'bmp':
            case 'tiff':
            case 'tif': return 'fa-file-image';
            default: return 'fa-file';
        }
    }

    getStatusClass(status) {
        switch (status) {
            case 'COMPLETED': return 'bg-success';
            case 'PROCESSING': return 'bg-primary';
            case 'FAILED': return 'bg-danger';
            case 'CANCELLED': return 'bg-secondary';
            default: return 'bg-warning';
        }
    }

    getStatusIcon(status) {
        switch (status) {
            case 'COMPLETED': return 'fas fa-check';
            case 'PROCESSING': return 'fas fa-spinner fa-spin';
            case 'FAILED': return 'fas fa-times';
            case 'CANCELLED': return 'fas fa-ban';
            default: return 'fas fa-clock';
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        if (!dateString) return 'Unknown';
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    showError(message) {
        // Create error alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert after upload area
        const uploadArea = document.getElementById('upload-area');
        uploadArea.parentNode.insertBefore(alertDiv, uploadArea.nextSibling);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Global functions for UI interactions
function removeFile() {
    if (window.fileUploader) {
        window.fileUploader.resetForm();
    }
}

function startUpload() {
    if (window.fileUploader) {
        window.fileUploader.startUpload();
    }
}

function viewTask(taskId) {
    window.location.href = `/tasks#${taskId}`;
}

function downloadResult(taskId) {
    window.open(`/api/v1/files/download/${taskId}`, '_blank');
}

// Initialize file uploader when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.fileUploader = new FileUploader();
});
