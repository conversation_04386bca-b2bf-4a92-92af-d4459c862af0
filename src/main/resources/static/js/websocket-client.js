/**
 * WebSocket client for real-time task progress updates
 */
class DocumentConverterWebSocket {
    constructor(baseUrl = '') {
        this.baseUrl = baseUrl || window.location.origin;
        this.socket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 1000; // Start with 1 second
        this.maxReconnectInterval = 30000; // Max 30 seconds
        this.isConnected = false;
        this.subscriptions = new Set();
        this.messageHandlers = new Map();
        this.heartbeatInterval = null;
        this.heartbeatTimeout = null;
        
        // Bind methods to preserve 'this' context
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onError = this.onError.bind(this);
        this.onClose = this.onClose.bind(this);
    }

    /**
     * Connect to WebSocket server
     */
    connect(endpoint = '/ws/tasks') {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            console.log('WebSocket already connected');
            return;
        }

        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}${this.baseUrl}${endpoint}`;
            
            console.log('Connecting to WebSocket:', wsUrl);
            
            this.socket = new WebSocket(wsUrl);
            this.socket.onopen = this.onOpen;
            this.socket.onmessage = this.onMessage;
            this.socket.onerror = this.onError;
            this.socket.onclose = this.onClose;
            
        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * Disconnect from WebSocket server
     */
    disconnect() {
        this.reconnectAttempts = this.maxReconnectAttempts; // Prevent reconnection
        
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }
        
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        
        this.isConnected = false;
        this.subscriptions.clear();
    }

    /**
     * Subscribe to task updates
     */
    subscribeToTask(taskId) {
        if (this.isConnected) {
            this.sendMessage('SUBSCRIBE', taskId);
            this.subscriptions.add(taskId);
        } else {
            // Queue subscription for when connected
            this.subscriptions.add(taskId);
        }
    }

    /**
     * Unsubscribe from task updates
     */
    unsubscribeFromTask(taskId) {
        if (this.isConnected) {
            this.sendMessage('UNSUBSCRIBE', taskId);
        }
        this.subscriptions.delete(taskId);
    }

    /**
     * Add message handler for specific message types
     */
    onMessageType(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, []);
        }
        this.messageHandlers.get(type).push(handler);
    }

    /**
     * Remove message handler
     */
    removeMessageHandler(type, handler) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Send message to server
     */
    sendMessage(type, data) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            const message = {
                type: type,
                data: data,
                timestamp: Date.now()
            };
            this.socket.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket not connected, cannot send message:', type);
        }
    }

    /**
     * Handle WebSocket open event
     */
    onOpen(event) {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectInterval = 1000;
        
        // Resubscribe to all tasks
        this.subscriptions.forEach(taskId => {
            this.sendMessage('SUBSCRIBE', taskId);
        });
        
        // Start heartbeat
        this.startHeartbeat();
        
        // Trigger connection event
        this.triggerEvent('connected', event);
    }

    /**
     * Handle WebSocket message event
     */
    onMessage(event) {
        try {
            const message = JSON.parse(event.data);
            console.log('Received WebSocket message:', message);
            
            // Handle specific message types
            switch (message.type) {
                case 'WELCOME':
                    console.log('Welcome message:', message.data);
                    break;
                    
                case 'TASK_PROGRESS_UPDATE':
                    this.handleTaskProgressUpdate(message.data);
                    break;
                    
                case 'SYSTEM_NOTIFICATION':
                    this.handleSystemNotification(message.data);
                    break;
                    
                case 'PONG':
                    this.handlePong();
                    break;
                    
                case 'ERROR':
                    console.error('WebSocket error message:', message.data);
                    break;
                    
                default:
                    console.log('Unknown message type:', message.type);
                    break;
            }
            
            // Trigger custom handlers
            this.triggerMessageHandlers(message.type, message.data);
            
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }

    /**
     * Handle WebSocket error event
     */
    onError(event) {
        console.error('WebSocket error:', event);
        this.triggerEvent('error', event);
    }

    /**
     * Handle WebSocket close event
     */
    onClose(event) {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnected = false;
        
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        this.triggerEvent('disconnected', event);
        
        // Attempt to reconnect if not intentionally closed
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }

    /**
     * Handle task progress update
     */
    handleTaskProgressUpdate(progressData) {
        console.log('Task progress update:', progressData);
        
        // Update UI elements
        this.updateTaskProgress(progressData);
        
        // Trigger custom event
        this.triggerEvent('taskProgress', progressData);
    }

    /**
     * Handle system notification
     */
    handleSystemNotification(notification) {
        console.log('System notification:', notification);
        
        // Show system notification
        this.showSystemNotification(notification);
        
        // Trigger custom event
        this.triggerEvent('systemNotification', notification);
    }

    /**
     * Handle pong response
     */
    handlePong() {
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }
    }

    /**
     * Start heartbeat mechanism
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.sendMessage('PING', null);
                
                // Set timeout for pong response
                this.heartbeatTimeout = setTimeout(() => {
                    console.warn('Heartbeat timeout, closing connection');
                    this.socket.close();
                }, 5000);
            }
        }, 30000); // Send ping every 30 seconds
    }

    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.triggerEvent('maxReconnectAttemptsReached');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectInterval);
        
        console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }

    /**
     * Update task progress in UI
     */
    updateTaskProgress(progressData) {
        const taskId = progressData.taskId;
        
        // Update progress bar
        const progressBar = document.querySelector(`[data-task-id="${taskId}"] .progress-bar`);
        if (progressBar && progressData.progress !== null) {
            progressBar.style.width = `${progressData.progress}%`;
            progressBar.textContent = `${progressData.progress}%`;
        }
        
        // Update status
        const statusElement = document.querySelector(`[data-task-id="${taskId}"] .task-status`);
        if (statusElement) {
            statusElement.textContent = progressData.status;
            statusElement.className = `task-status status-${progressData.status.toLowerCase()}`;
        }
        
        // Update message
        const messageElement = document.querySelector(`[data-task-id="${taskId}"] .task-message`);
        if (messageElement && progressData.message) {
            messageElement.textContent = progressData.message;
        }
        
        // Update current step
        const stepElement = document.querySelector(`[data-task-id="${taskId}"] .current-step`);
        if (stepElement && progressData.currentStep) {
            stepElement.textContent = progressData.currentStep;
        }
    }

    /**
     * Show system notification
     */
    showSystemNotification(notification) {
        // Create notification element
        const notificationElement = document.createElement('div');
        notificationElement.className = 'system-notification alert alert-info';
        notificationElement.innerHTML = `
            <strong>${notification.type}:</strong> ${JSON.stringify(notification.data)}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        // Add to notification container
        const container = document.getElementById('notifications') || document.body;
        container.appendChild(notificationElement);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notificationElement.parentElement) {
                notificationElement.remove();
            }
        }, 5000);
    }

    /**
     * Trigger custom event handlers
     */
    triggerMessageHandlers(type, data) {
        if (this.messageHandlers.has(type)) {
            this.messageHandlers.get(type).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error('Error in message handler:', error);
                }
            });
        }
    }

    /**
     * Trigger custom events
     */
    triggerEvent(eventType, data) {
        const event = new CustomEvent(`websocket-${eventType}`, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Get connection status
     */
    getStatus() {
        return {
            connected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            subscriptions: Array.from(this.subscriptions)
        };
    }
}

// Global instance
window.docConverterWS = new DocumentConverterWebSocket();

// Auto-connect when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.docConverterWS.connect();
});

// Reconnect when page becomes visible
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && !window.docConverterWS.isConnected) {
        window.docConverterWS.connect();
    }
});
