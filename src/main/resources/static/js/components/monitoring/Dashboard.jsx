import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic } from 'antd';

export default function MonitoringDashboard() {
  const [systemStatus, setSystemStatus] = useState('loading');
  const [moduleMetrics, setModuleMetrics] = useState({});

  useEffect(() => {
    // TODO: 从API获取监控数据
    fetchMonitoringData();
  }, []);

  const fetchMonitoringData = async () => {
    try {
      const response = await fetch('/api/monitoring/metrics');
      const data = await response.json();
      setModuleMetrics(data);
      setSystemStatus('normal');
    } catch (error) {
      setSystemStatus('error');
    }
  };

  return (
    <div className="monitoring-dashboard">
      <Row gutter={16}>
        <Col span={8}>
          <Card title="系统状态">
            <Statistic 
              value={systemStatus} 
              valueStyle={{ color: systemStatus === 'normal' ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        {/* 其他监控卡片 */}
      </Row>
    </div>
  );
}
