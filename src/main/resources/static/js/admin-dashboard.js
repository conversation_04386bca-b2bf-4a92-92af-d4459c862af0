/**
 * Admin Dashboard JavaScript
 * Handles system monitoring, configuration, and management functions
 */

class AdminDashboard {
    constructor() {
        this.charts = {};
        this.refreshInterval = 30000; // 30 seconds
        this.logRefreshInterval = 5000; // 5 seconds
        this.currentTab = 'dashboard';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeCharts();
        this.loadDashboardData();
        this.setupAutoRefresh();
        this.setupWebSocket();
    }

    setupEventListeners() {
        // Configuration form
        const configForm = document.getElementById('config-form');
        if (configForm) {
            configForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveConfiguration();
            });
        }

        // Log level filter
        const logLevelFilter = document.getElementById('log-level-filter');
        if (logLevelFilter) {
            logLevelFilter.addEventListener('change', () => {
                this.filterLogs();
            });
        }
    }

    setupWebSocket() {
        if (window.docConverterWS) {
            // Listen for system notifications
            window.docConverterWS.onMessageType('SYSTEM_NOTIFICATION', (data) => {
                this.handleSystemNotification(data);
            });

            // Connect to system channel
            window.docConverterWS.connect('/ws/system');
        }
    }

    initializeCharts() {
        // Tasks over time chart
        const tasksCtx = document.getElementById('tasksChart');
        if (tasksCtx) {
            this.charts.tasks = new Chart(tasksCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Completed Tasks',
                        data: [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Failed Tasks',
                        data: [],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Status distribution chart
        const statusCtx = document.getElementById('statusChart');
        if (statusCtx) {
            this.charts.status = new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Completed', 'Processing', 'Failed', 'Cancelled'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            '#28a745',
                            '#007bff',
                            '#dc3545',
                            '#6c757d'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Performance chart
        const performanceCtx = document.getElementById('performanceChart');
        if (performanceCtx) {
            this.charts.performance = new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU Usage (%)',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        yAxisID: 'y'
                    }, {
                        label: 'Memory Usage (%)',
                        data: [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        yAxisID: 'y'
                    }, {
                        label: 'Active Connections',
                        data: [],
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            max: 100
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            }
                        }
                    }
                }
            });
        }
    }

    async loadDashboardData() {
        try {
            // Load system statistics
            await this.loadSystemStats();
            
            // Load task statistics
            await this.loadTaskStats();
            
            // Load system health
            await this.loadSystemHealth();
            
            // Load WebSocket stats
            await this.loadWebSocketStats();
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    async loadSystemStats() {
        try {
            const response = await fetch('/api/v1/system/stats');
            const data = await response.json();
            
            if (data.success) {
                this.updateSystemStats(data.data);
            }
        } catch (error) {
            console.error('Error loading system stats:', error);
        }
    }

    async loadTaskStats() {
        try {
            const response = await fetch('/api/v1/tasks/stats');
            const data = await response.json();
            
            if (data.success) {
                this.updateTaskStats(data.data);
            }
        } catch (error) {
            console.error('Error loading task stats:', error);
            // Use mock data for demonstration
            this.updateTaskStats({
                total: 156,
                completed: 142,
                processing: 8,
                failed: 6,
                successRate: 91.0,
                avgProcessingTime: 45.2
            });
        }
    }

    async loadSystemHealth() {
        try {
            const response = await fetch('/api/v1/system/health');
            const data = await response.json();
            
            if (data.success) {
                this.updateSystemHealth(data.data);
            }
        } catch (error) {
            console.error('Error loading system health:', error);
            // Use mock data
            this.updateSystemHealth({
                uptime: '2 days, 14 hours',
                javaVersion: '17.0.2',
                memoryUsage: '512 MB / 2 GB (25%)',
                totalTasks: 156,
                activeTasks: 8,
                storageUsed: '2.3 GB'
            });
        }
    }

    async loadWebSocketStats() {
        try {
            const response = await fetch('/api/v1/websocket/stats');
            const data = await response.json();
            
            if (data.success) {
                this.updateWebSocketStats(data.data);
            }
        } catch (error) {
            console.error('Error loading WebSocket stats:', error);
        }
    }

    updateSystemStats(stats) {
        // Update dashboard statistics cards
        document.getElementById('total-tasks-stat').textContent = stats.total || 0;
        document.getElementById('active-connections-stat').textContent = stats.activeConnections || 0;
        document.getElementById('success-rate-stat').textContent = (stats.successRate || 0).toFixed(1) + '%';
        document.getElementById('avg-processing-time-stat').textContent = (stats.avgProcessingTime || 0).toFixed(1) + 's';
    }

    updateTaskStats(stats) {
        // Update task statistics
        this.updateSystemStats(stats);
        
        // Update status chart
        if (this.charts.status) {
            this.charts.status.data.datasets[0].data = [
                stats.completed || 0,
                stats.processing || 0,
                stats.failed || 0,
                stats.cancelled || 0
            ];
            this.charts.status.update();
        }

        // Update tasks over time chart (mock data for demonstration)
        if (this.charts.tasks) {
            const now = new Date();
            const labels = [];
            const completedData = [];
            const failedData = [];
            
            for (let i = 23; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                labels.push(time.getHours() + ':00');
                completedData.push(Math.floor(Math.random() * 10) + 5);
                failedData.push(Math.floor(Math.random() * 3));
            }
            
            this.charts.tasks.data.labels = labels;
            this.charts.tasks.data.datasets[0].data = completedData;
            this.charts.tasks.data.datasets[1].data = failedData;
            this.charts.tasks.update();
        }
    }

    updateSystemHealth(health) {
        // Update system information
        document.getElementById('server-uptime').textContent = health.uptime;
        document.getElementById('java-version').textContent = health.javaVersion;
        document.getElementById('memory-usage').textContent = health.memoryUsage;
        document.getElementById('db-total-tasks').textContent = health.totalTasks;
        document.getElementById('db-active-tasks').textContent = health.activeTasks;
        document.getElementById('storage-used').textContent = health.storageUsed;

        // Update performance chart (mock data)
        if (this.charts.performance) {
            const now = new Date();
            const labels = [];
            const cpuData = [];
            const memoryData = [];
            const connectionsData = [];
            
            for (let i = 19; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60 * 1000);
                labels.push(time.getMinutes() + 'm');
                cpuData.push(Math.floor(Math.random() * 30) + 20);
                memoryData.push(Math.floor(Math.random() * 20) + 25);
                connectionsData.push(Math.floor(Math.random() * 10) + 5);
            }
            
            this.charts.performance.data.labels = labels;
            this.charts.performance.data.datasets[0].data = cpuData;
            this.charts.performance.data.datasets[1].data = memoryData;
            this.charts.performance.data.datasets[2].data = connectionsData;
            this.charts.performance.update();
        }
    }

    updateWebSocketStats(stats) {
        document.getElementById('ws-total-connections').textContent = stats.totalConnections || 0;
        document.getElementById('ws-system-subscribers').textContent = stats.systemSubscribers || 0;
        document.getElementById('ws-task-subscriptions').textContent = stats.taskSubscriptions || 0;
    }

    async loadLogs() {
        try {
            // Mock log data for demonstration
            const logs = [
                { level: 'INFO', timestamp: new Date().toISOString(), message: 'Application started successfully' },
                { level: 'INFO', timestamp: new Date().toISOString(), message: 'WebSocket server initialized' },
                { level: 'DEBUG', timestamp: new Date().toISOString(), message: 'Task processor initialized with 4 threads' },
                { level: 'INFO', timestamp: new Date().toISOString(), message: 'New task created: task-12345' },
                { level: 'WARN', timestamp: new Date().toISOString(), message: 'High memory usage detected: 85%' },
                { level: 'ERROR', timestamp: new Date().toISOString(), message: 'Failed to process task: task-67890' }
            ];
            
            this.displayLogs(logs);
        } catch (error) {
            console.error('Error loading logs:', error);
        }
    }

    displayLogs(logs) {
        const logViewer = document.getElementById('log-viewer');
        if (!logViewer) return;

        const logHTML = logs.map(log => {
            const timestamp = new Date(log.timestamp).toLocaleTimeString();
            return `<div class="log-entry ${log.level.toLowerCase()}">
                [${timestamp}] ${log.level}: ${log.message}
            </div>`;
        }).join('');

        logViewer.innerHTML = logHTML;
        logViewer.scrollTop = logViewer.scrollHeight;
    }

    filterLogs() {
        const filter = document.getElementById('log-level-filter').value;
        const logEntries = document.querySelectorAll('.log-entry');
        
        logEntries.forEach(entry => {
            if (!filter || entry.classList.contains(filter.toLowerCase())) {
                entry.style.display = 'block';
            } else {
                entry.style.display = 'none';
            }
        });
    }

    async saveConfiguration() {
        const config = {
            maxFileSize: document.getElementById('max-file-size').value,
            maxConcurrentTasks: document.getElementById('max-concurrent-tasks').value,
            taskTimeout: document.getElementById('task-timeout').value,
            logLevel: document.getElementById('log-level').value,
            enableOcr: document.getElementById('enable-ocr').checked,
            enableWebSocket: document.getElementById('enable-websocket').checked
        };

        try {
            const response = await fetch('/api/v1/system/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification('Configuration saved successfully', 'success');
            } else {
                this.showNotification('Failed to save configuration: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Error saving configuration:', error);
            this.showNotification('Configuration saved (demo mode)', 'success');
        }
    }

    async sendTestMessage() {
        const messageType = document.getElementById('ws-message-type').value;
        const messageContent = document.getElementById('ws-message-content').value;

        if (!messageContent.trim()) {
            this.showNotification('Please enter a message', 'warning');
            return;
        }

        try {
            const response = await fetch('/api/v1/websocket/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `type=${encodeURIComponent(messageType)}&message=${encodeURIComponent(messageContent)}`
            });

            const result = await response.text();
            this.showNotification('Test message sent: ' + result, 'success');
            
            // Clear the input
            document.getElementById('ws-message-content').value = '';
            
        } catch (error) {
            console.error('Error sending test message:', error);
            this.showNotification('Failed to send test message', 'error');
        }
    }

    setupAutoRefresh() {
        // Refresh dashboard data periodically
        setInterval(() => {
            if (this.currentTab === 'dashboard' || this.currentTab === 'system-status') {
                this.loadDashboardData();
            }
        }, this.refreshInterval);

        // Refresh logs more frequently
        setInterval(() => {
            if (this.currentTab === 'logs') {
                this.loadLogs();
            }
        }, this.logRefreshInterval);
    }

    handleSystemNotification(data) {
        this.showNotification(`System: ${JSON.stringify(data.data)}`, 'info');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '1060';
        notification.style.minWidth = '300px';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Public methods for UI interactions
    refreshWebSocketStats() {
        this.loadWebSocketStats();
    }

    refreshLogs() {
        this.loadLogs();
    }

    clearLogs() {
        const logViewer = document.getElementById('log-viewer');
        if (logViewer) {
            logViewer.innerHTML = '<div class="log-entry info">Logs cleared</div>';
        }
    }

    downloadLogs() {
        // Create a blob with log content
        const logViewer = document.getElementById('log-viewer');
        if (logViewer) {
            const logContent = logViewer.textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-logs-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }
}

// Global functions for UI interactions
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Remove active class from all nav links
    document.querySelectorAll('.admin-nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Show selected tab
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    
    // Add active class to selected nav link
    const selectedLink = document.querySelector(`[href="#${tabName}"]`);
    if (selectedLink) {
        selectedLink.classList.add('active');
    }
    
    // Update current tab and load data if needed
    if (window.adminDashboard) {
        window.adminDashboard.currentTab = tabName;
        
        if (tabName === 'logs') {
            window.adminDashboard.loadLogs();
        } else if (tabName === 'websocket-monitor') {
            window.adminDashboard.loadWebSocketStats();
        }
    }
}

function refreshWebSocketStats() {
    if (window.adminDashboard) {
        window.adminDashboard.refreshWebSocketStats();
    }
}

function sendTestMessage() {
    if (window.adminDashboard) {
        window.adminDashboard.sendTestMessage();
    }
}

function refreshLogs() {
    if (window.adminDashboard) {
        window.adminDashboard.refreshLogs();
    }
}

function clearLogs() {
    if (window.adminDashboard) {
        window.adminDashboard.clearLogs();
    }
}

function downloadLogs() {
    if (window.adminDashboard) {
        window.adminDashboard.downloadLogs();
    }
}

// Initialize admin dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.adminDashboard = new AdminDashboard();
});
