<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Converter - Upload File</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 12px;
            padding: 3rem;
            text-align: center;
            transition: all 0.3s ease;
            background: #f8f9fa;
            cursor: pointer;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .upload-area.dragover {
            border-color: #28a745;
            background: #d4edda;
            transform: scale(1.02);
        }
        
        .upload-area.uploading {
            border-color: #ffc107;
            background: #fff3cd;
        }
        
        .upload-icon {
            font-size: 4rem;
            color: #6c757d;
            margin-bottom: 1rem;
            transition: color 0.3s ease;
        }
        
        .upload-area:hover .upload-icon {
            color: #007bff;
        }
        
        .upload-area.dragover .upload-icon {
            color: #28a745;
        }
        
        .file-preview {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            display: none;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        
        .file-icon {
            font-size: 2rem;
            margin-right: 1rem;
        }
        
        .progress-container {
            margin-top: 1rem;
        }
        
        .upload-options {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .supported-formats {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .format-category {
            margin-bottom: 1rem;
        }
        
        .format-badge {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            margin: 0.125rem;
        }
        
        .upload-history {
            margin-top: 2rem;
        }
        
        .history-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .conversion-options {
            display: none;
            background: #e3f2fd;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .option-group {
            margin-bottom: 1rem;
        }
        
        .option-group:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-file-alt me-2"></i>
                Document Converter
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/tasks">
                    <i class="fas fa-tasks me-1"></i>Tasks
                </a>
                <a class="nav-link active" href="/upload">
                    <i class="fas fa-upload me-1"></i>Upload
                </a>
                <a class="nav-link" href="/websocket-test.html">
                    <i class="fas fa-plug me-1"></i>WebSocket Test
                </a>
                <a class="nav-link" href="/swagger-ui.html" target="_blank">
                    <i class="fas fa-code me-1"></i>API Docs
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-8">
                <h2 class="mb-4">
                    <i class="fas fa-cloud-upload-alt me-2"></i>
                    Upload Document
                </h2>

                <!-- Upload Area -->
                <div class="upload-area" id="upload-area">
                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                    <h4>Drag & Drop your file here</h4>
                    <p class="text-muted mb-3">or click to browse files</p>
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-folder-open me-2"></i>
                        Choose File
                    </button>
                    <input type="file" id="file-input" style="display: none;" accept=".pdf,.docx,.doc,.xlsx,.xls,.pptx,.ppt,.html,.htm,.txt,.rtf,.odt,.png,.jpg,.jpeg,.tiff,.tif,.bmp,.gif">
                </div>

                <!-- File Preview -->
                <div class="file-preview" id="file-preview">
                    <div class="file-info">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file file-icon text-primary" id="file-icon"></i>
                            <div>
                                <h6 class="mb-0" id="file-name">filename.pdf</h6>
                                <small class="text-muted" id="file-size">2.5 MB</small>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeFile()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <!-- Conversion Options -->
                    <div class="conversion-options" id="conversion-options">
                        <h6><i class="fas fa-cog me-2"></i>Conversion Options</h6>
                        
                        <div class="option-group">
                            <label class="form-label">Output Format</label>
                            <select class="form-select" id="output-format">
                                <option value="markdown">Markdown (.md)</option>
                                <option value="html">HTML (.html)</option>
                                <option value="text">Plain Text (.txt)</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label class="form-label">Quality Settings</label>
                            <select class="form-select" id="quality-setting">
                                <option value="standard">Standard Quality</option>
                                <option value="high">High Quality (slower)</option>
                                <option value="fast">Fast Processing (lower quality)</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="preserve-formatting" checked>
                                <label class="form-check-label" for="preserve-formatting">
                                    Preserve original formatting
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="extract-images">
                                <label class="form-check-label" for="extract-images">
                                    Extract and include images
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="ocr-enabled">
                                <label class="form-check-label" for="ocr-enabled">
                                    Enable OCR for scanned documents
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="progress-container" id="progress-container" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">Uploading...</span>
                            <span class="text-muted" id="progress-text">0%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 style="width: 0%" 
                                 id="progress-bar">
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted" id="upload-status">Preparing upload...</small>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <button type="button" class="btn btn-outline-secondary" onclick="removeFile()">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-success" id="upload-btn" onclick="startUpload()">
                            <i class="fas fa-rocket me-2"></i>Start Conversion
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Supported Formats -->
                <div class="supported-formats">
                    <h5><i class="fas fa-check-circle me-2 text-success"></i>Supported Formats</h5>
                    
                    <div class="format-category">
                        <h6 class="text-primary">Documents</h6>
                        <span class="format-badge">PDF</span>
                        <span class="format-badge">DOCX</span>
                        <span class="format-badge">DOC</span>
                        <span class="format-badge">RTF</span>
                        <span class="format-badge">ODT</span>
                        <span class="format-badge">TXT</span>
                    </div>
                    
                    <div class="format-category">
                        <h6 class="text-success">Spreadsheets</h6>
                        <span class="format-badge">XLSX</span>
                        <span class="format-badge">XLS</span>
                    </div>
                    
                    <div class="format-category">
                        <h6 class="text-warning">Presentations</h6>
                        <span class="format-badge">PPTX</span>
                        <span class="format-badge">PPT</span>
                    </div>
                    
                    <div class="format-category">
                        <h6 class="text-info">Web & Markup</h6>
                        <span class="format-badge">HTML</span>
                        <span class="format-badge">HTM</span>
                    </div>
                    
                    <div class="format-category">
                        <h6 class="text-secondary">Images (OCR)</h6>
                        <span class="format-badge">PNG</span>
                        <span class="format-badge">JPG</span>
                        <span class="format-badge">JPEG</span>
                        <span class="format-badge">TIFF</span>
                        <span class="format-badge">BMP</span>
                        <span class="format-badge">GIF</span>
                    </div>
                </div>
                
                <!-- Upload Limits -->
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>Upload Limits</h6>
                    <ul class="mb-0">
                        <li>Maximum file size: <strong>100 MB</strong></li>
                        <li>Processing time: <strong>1-5 minutes</strong></li>
                        <li>Concurrent uploads: <strong>3 files</strong></li>
                    </ul>
                </div>
                
                <!-- Recent Uploads -->
                <div class="upload-history" id="upload-history">
                    <h5><i class="fas fa-history me-2"></i>Recent Uploads</h5>
                    <div id="recent-uploads">
                        <!-- Recent uploads will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-check-circle me-2"></i>
                        Upload Successful
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Your file has been uploaded successfully and conversion has started.</p>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-file-alt fa-2x text-primary me-3"></i>
                        <div>
                            <h6 class="mb-0" id="success-filename">filename.pdf</h6>
                            <small class="text-muted">Task ID: <span id="success-task-id">12345</span></small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="/tasks" class="btn btn-primary">
                        <i class="fas fa-tasks me-2"></i>View Tasks
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/websocket-client.js"></script>
    <script src="/js/progress-monitor.js"></script>
    <script src="/js/file-upload.js"></script>
</body>
</html>
