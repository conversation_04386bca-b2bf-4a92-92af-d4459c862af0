<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Converter - System Administration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            background: #343a40;
            min-height: calc(100vh - 56px);
            padding: 0;
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .admin-nav-item {
            border-bottom: 1px solid #495057;
        }
        
        .admin-nav-link {
            display: block;
            padding: 1rem 1.5rem;
            color: #adb5bd;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .admin-nav-link:hover,
        .admin-nav-link.active {
            background: #495057;
            color: white;
        }
        
        .admin-nav-link i {
            width: 20px;
            margin-right: 0.5rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .stat-change {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }
        
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .system-info-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .log-viewer {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            height: 400px;
            overflow-y: auto;
            margin-bottom: 1.5rem;
        }
        
        .log-entry {
            margin-bottom: 0.25rem;
            padding: 0.25rem 0;
        }
        
        .log-entry.error { color: #f48771; }
        .log-entry.warn { color: #dcdcaa; }
        .log-entry.info { color: #9cdcfe; }
        .log-entry.debug { color: #608b4e; }
        
        .config-section {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .admin-content {
            padding: 2rem;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-cogs me-2"></i>
                System Administration
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/tasks">
                    <i class="fas fa-tasks me-1"></i>Tasks
                </a>
                <a class="nav-link" href="/upload">
                    <i class="fas fa-upload me-1"></i>Upload
                </a>
                <a class="nav-link" href="/websocket-test.html">
                    <i class="fas fa-plug me-1"></i>WebSocket Test
                </a>
                <a class="nav-link" href="/swagger-ui.html" target="_blank">
                    <i class="fas fa-code me-1"></i>API Docs
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar">
                <ul class="admin-nav">
                    <li class="admin-nav-item">
                        <a href="#dashboard" class="admin-nav-link active" onclick="showTab('dashboard')">
                            <i class="fas fa-tachometer-alt"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#system-status" class="admin-nav-link" onclick="showTab('system-status')">
                            <i class="fas fa-server"></i>
                            System Status
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#logs" class="admin-nav-link" onclick="showTab('logs')">
                            <i class="fas fa-file-alt"></i>
                            Logs
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#configuration" class="admin-nav-link" onclick="showTab('configuration')">
                            <i class="fas fa-cog"></i>
                            Configuration
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#websocket-monitor" class="admin-nav-link" onclick="showTab('websocket-monitor')">
                            <i class="fas fa-plug"></i>
                            WebSocket Monitor
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#api-monitor" class="admin-nav-link" onclick="showTab('api-monitor')">
                            <i class="fas fa-chart-line"></i>
                            API Monitor
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <!-- Dashboard Tab -->
                <div id="dashboard" class="tab-content active">
                    <h2 class="mb-4">System Dashboard</h2>
                    
                    <!-- Statistics Cards -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number" id="total-tasks-stat">0</div>
                                <div class="stat-label">Total Tasks</div>
                                <div class="stat-change">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="tasks-change">+12%</span> from last week
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number" id="active-connections-stat">0</div>
                                <div class="stat-label">Active Connections</div>
                                <div class="stat-change">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="connections-change">+5</span> since last hour
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number" id="success-rate-stat">0%</div>
                                <div class="stat-label">Success Rate</div>
                                <div class="stat-change">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="success-rate-change">+2.1%</span> improvement
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number" id="avg-processing-time-stat">0s</div>
                                <div class="stat-label">Avg Processing Time</div>
                                <div class="stat-change">
                                    <i class="fas fa-arrow-down"></i>
                                    <span id="processing-time-change">-15%</span> faster
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <h5>Task Processing Over Time</h5>
                                <canvas id="tasksChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="chart-container">
                                <h5>Task Status Distribution</h5>
                                <canvas id="statusChart" width="200" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Status Tab -->
                <div id="system-status" class="tab-content">
                    <h2 class="mb-4">System Status</h2>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="system-info-card">
                                <h5><i class="fas fa-server me-2"></i>Server Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="status-indicator status-online"></span>
                                            Online
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Uptime:</strong></td>
                                        <td id="server-uptime">Loading...</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Version:</strong></td>
                                        <td id="server-version">1.0.0-SNAPSHOT</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Java Version:</strong></td>
                                        <td id="java-version">Loading...</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Memory Usage:</strong></td>
                                        <td id="memory-usage">Loading...</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="system-info-card">
                                <h5><i class="fas fa-database me-2"></i>Database Status</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="status-indicator status-online"></span>
                                            Connected
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Type:</strong></td>
                                        <td>H2 Database</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Tasks:</strong></td>
                                        <td id="db-total-tasks">Loading...</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Active Tasks:</strong></td>
                                        <td id="db-active-tasks">Loading...</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Storage Used:</strong></td>
                                        <td id="storage-used">Loading...</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="system-info-card">
                                <h5><i class="fas fa-chart-area me-2"></i>System Performance</h5>
                                <canvas id="performanceChart" width="800" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Logs Tab -->
                <div id="logs" class="tab-content">
                    <h2 class="mb-4">System Logs</h2>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <select class="form-select" id="log-level-filter">
                                <option value="">All Levels</option>
                                <option value="ERROR">Error</option>
                                <option value="WARN">Warning</option>
                                <option value="INFO">Info</option>
                                <option value="DEBUG">Debug</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group">
                                <button class="btn btn-outline-primary" onclick="refreshLogs()">
                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearLogs()">
                                    <i class="fas fa-trash me-1"></i>Clear
                                </button>
                                <button class="btn btn-outline-success" onclick="downloadLogs()">
                                    <i class="fas fa-download me-1"></i>Download
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="log-viewer" id="log-viewer">
                        <!-- Logs will be loaded here -->
                    </div>
                </div>

                <!-- Configuration Tab -->
                <div id="configuration" class="tab-content">
                    <h2 class="mb-4">System Configuration</h2>
                    
                    <div class="config-section">
                        <h5><i class="fas fa-cog me-2"></i>Application Settings</h5>
                        <form id="config-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Max File Size (MB)</label>
                                        <input type="number" class="form-control" id="max-file-size" value="100">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Max Concurrent Tasks</label>
                                        <input type="number" class="form-control" id="max-concurrent-tasks" value="10">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Task Timeout (minutes)</label>
                                        <input type="number" class="form-control" id="task-timeout" value="30">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Log Level</label>
                                        <select class="form-select" id="log-level">
                                            <option value="DEBUG">Debug</option>
                                            <option value="INFO" selected>Info</option>
                                            <option value="WARN">Warning</option>
                                            <option value="ERROR">Error</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-ocr" checked>
                                            <label class="form-check-label" for="enable-ocr">
                                                Enable OCR Processing
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-websocket" checked>
                                            <label class="form-check-label" for="enable-websocket">
                                                Enable WebSocket Real-time Updates
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Configuration
                            </button>
                        </form>
                    </div>
                </div>

                <!-- WebSocket Monitor Tab -->
                <div id="websocket-monitor" class="tab-content">
                    <h2 class="mb-4">WebSocket Monitor</h2>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="system-info-card">
                                <h6>Connection Statistics</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>Total Connections:</td>
                                        <td><span id="ws-total-connections">0</span></td>
                                    </tr>
                                    <tr>
                                        <td>System Subscribers:</td>
                                        <td><span id="ws-system-subscribers">0</span></td>
                                    </tr>
                                    <tr>
                                        <td>Task Subscriptions:</td>
                                        <td><span id="ws-task-subscriptions">0</span></td>
                                    </tr>
                                </table>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshWebSocketStats()">
                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                </button>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="system-info-card">
                                <h6>Test WebSocket Functions</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Message Type</label>
                                            <select class="form-select" id="ws-message-type">
                                                <option value="TEST">Test Message</option>
                                                <option value="SYSTEM_NOTIFICATION">System Notification</option>
                                                <option value="MAINTENANCE">Maintenance Notice</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Message Content</label>
                                            <input type="text" class="form-control" id="ws-message-content" 
                                                   placeholder="Enter message content">
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary" onclick="sendTestMessage()">
                                    <i class="fas fa-paper-plane me-1"></i>Send Test Message
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Monitor Tab -->
                <div id="api-monitor" class="tab-content">
                    <h2 class="mb-4">API Monitor</h2>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="system-info-card">
                                <h6>API Endpoints Status</h6>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Endpoint</th>
                                                <th>Method</th>
                                                <th>Status</th>
                                                <th>Response Time</th>
                                                <th>Last Check</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="api-endpoints-table">
                                            <!-- API endpoints will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="/js/websocket-client.js"></script>
    <script src="/js/admin-dashboard.js"></script>
</body>
</html>
