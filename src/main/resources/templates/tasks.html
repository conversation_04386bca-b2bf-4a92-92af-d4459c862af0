<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Converter - Task Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .task-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .task-card.status-completed { border-left-color: #28a745; }
        .task-card.status-processing { border-left-color: #007bff; }
        .task-card.status-failed { border-left-color: #dc3545; }
        .task-card.status-cancelled { border-left-color: #6c757d; }
        .task-card.status-pending { border-left-color: #ffc107; }
        
        .status-badge {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }
        .progress-container {
            height: 8px;
            border-radius: 4px;
        }
        .search-filters {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .task-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .task-actions {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .task-card:hover .task-actions {
            opacity: 1;
        }
        .filter-chip {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            margin: 0.25rem;
            background: #e9ecef;
            border-radius: 20px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .filter-chip.active {
            background: #007bff;
            color: white;
        }
        .filter-chip:hover {
            background: #dee2e6;
        }
        .filter-chip.active:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-file-alt me-2"></i>
                Document Converter
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link active" href="/tasks">
                    <i class="fas fa-tasks me-1"></i>Tasks
                </a>
                <a class="nav-link" href="/upload">
                    <i class="fas fa-upload me-1"></i>Upload
                </a>
                <a class="nav-link" href="/websocket-test.html">
                    <i class="fas fa-plug me-1"></i>WebSocket Test
                </a>
                <a class="nav-link" href="/swagger-ui.html" target="_blank">
                    <i class="fas fa-code me-1"></i>API Docs
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Task Statistics -->
        <div class="task-stats">
            <div class="row">
                <div class="col-md-3 stat-item">
                    <span class="stat-number" id="total-tasks">0</span>
                    <span class="stat-label">Total Tasks</span>
                </div>
                <div class="col-md-3 stat-item">
                    <span class="stat-number" id="completed-tasks">0</span>
                    <span class="stat-label">Completed</span>
                </div>
                <div class="col-md-3 stat-item">
                    <span class="stat-number" id="processing-tasks">0</span>
                    <span class="stat-label">Processing</span>
                </div>
                <div class="col-md-3 stat-item">
                    <span class="stat-number" id="failed-tasks">0</span>
                    <span class="stat-label">Failed</span>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="search-filters">
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search-input" 
                               placeholder="Search tasks by filename or ID...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="status-filter">
                        <option value="">All Status</option>
                        <option value="PENDING">Pending</option>
                        <option value="PROCESSING">Processing</option>
                        <option value="COMPLETED">Completed</option>
                        <option value="FAILED">Failed</option>
                        <option value="CANCELLED">Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="sort-select">
                        <option value="createdAt,desc">Newest First</option>
                        <option value="createdAt,asc">Oldest First</option>
                        <option value="fileName,asc">Name A-Z</option>
                        <option value="fileName,desc">Name Z-A</option>
                        <option value="status,asc">Status</option>
                    </select>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted me-2">Quick Filters:</span>
                            <span class="filter-chip" data-filter="today">Today</span>
                            <span class="filter-chip" data-filter="week">This Week</span>
                            <span class="filter-chip" data-filter="month">This Month</span>
                            <span class="filter-chip" data-filter="processing">Processing</span>
                            <span class="filter-chip" data-filter="completed">Completed</span>
                        </div>
                        <div>
                            <button class="btn btn-outline-secondary btn-sm me-2" onclick="refreshTasks()">
                                <i class="fas fa-sync-alt me-1"></i>Refresh
                            </button>
                            <a href="/upload" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-1"></i>New Task
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task List -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <span id="task-count">0</span> Tasks Found
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary active" id="view-grid" onclick="setView('grid')">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="view-list" onclick="setView('list')">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <div class="row" id="task-list">
                    <!-- Tasks will be loaded here -->
                </div>

                <!-- Loading State -->
                <div id="loading-state" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading tasks...</p>
                </div>

                <!-- Empty State -->
                <div id="empty-state" class="text-center py-5" style="display: none;">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No tasks found</h4>
                    <p class="text-muted">Start by uploading a document to convert.</p>
                    <a href="/upload" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>
                        Upload Document
                    </a>
                </div>

                <!-- Pagination -->
                <nav aria-label="Task pagination" id="pagination-container" style="display: none;">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Task Detail Modal -->
    <div class="modal fade" id="taskDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Task Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="task-detail-content">
                    <!-- Task details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="download-result-btn" style="display: none;">
                        <i class="fas fa-download me-1"></i>Download Result
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/websocket-client.js"></script>
    <script src="/js/progress-monitor.js"></script>
    <script src="/js/task-management.js"></script>
</body>
</html>
