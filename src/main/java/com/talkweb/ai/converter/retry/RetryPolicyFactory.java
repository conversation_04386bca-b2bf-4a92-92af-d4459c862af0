package com.talkweb.ai.converter.retry;

import com.talkweb.ai.converter.recovery.OcrErrorHandler;
import org.springframework.stereotype.Component;

/**
 * 重试策略工厂
 * 
 * 根据不同的错误类型和场景创建相应的重试策略
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class RetryPolicyFactory {
    
    /**
     * 创建默认重试配置
     */
    public OcrRetryManager.RetryConfig createDefaultConfig() {
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setMaxAttempts(3);
        config.setBaseDelayMs(1000);
        config.setMaxDelayMs(30000);
        config.setBackoffMultiplier(2.0);
        config.setJitterFactor(0.1);
        config.setEnableCircuitBreaker(true);
        return config;
    }
    
    /**
     * 创建快速重试配置（用于轻量级操作）
     */
    public OcrRetryManager.RetryConfig createFastRetryConfig() {
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setMaxAttempts(5);
        config.setBaseDelayMs(500);
        config.setMaxDelayMs(5000);
        config.setBackoffMultiplier(1.5);
        config.setJitterFactor(0.2);
        config.setEnableCircuitBreaker(false);
        return config;
    }
    
    /**
     * 创建保守重试配置（用于重要操作）
     */
    public OcrRetryManager.RetryConfig createConservativeConfig() {
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setMaxAttempts(2);
        config.setBaseDelayMs(2000);
        config.setMaxDelayMs(60000);
        config.setBackoffMultiplier(3.0);
        config.setJitterFactor(0.05);
        config.setEnableCircuitBreaker(true);
        return config;
    }
    
    /**
     * 创建激进重试配置（用于可靠性要求高的操作）
     */
    public OcrRetryManager.RetryConfig createAggressiveConfig() {
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setMaxAttempts(7);
        config.setBaseDelayMs(300);
        config.setMaxDelayMs(15000);
        config.setBackoffMultiplier(1.8);
        config.setJitterFactor(0.3);
        config.setEnableCircuitBreaker(false);
        return config;
    }
    
    /**
     * 根据错误类型创建重试配置
     */
    public OcrRetryManager.RetryConfig createConfigForErrorType(OcrErrorHandler.ErrorType errorType) {
        switch (errorType) {
            case TESSERACT_ERROR:
                // Tesseract错误通常是临时的，可以多次重试
                return createAggressiveConfig();
                
            case TIMEOUT_ERROR:
                // 超时错误需要更长的等待时间
                OcrRetryManager.RetryConfig timeoutConfig = createDefaultConfig();
                timeoutConfig.setBaseDelayMs(3000);
                timeoutConfig.setMaxDelayMs(60000);
                timeoutConfig.setBackoffMultiplier(2.5);
                return timeoutConfig;
                
            case MEMORY_ERROR:
                // 内存错误需要保守的重试策略
                return createConservativeConfig();
                
            case IO_ERROR:
                // IO错误可能是临时的，使用快速重试
                return createFastRetryConfig();
                
            case IMAGE_FORMAT_ERROR:
                // 图像格式错误通常不会通过重试解决
                OcrRetryManager.RetryConfig formatConfig = new OcrRetryManager.RetryConfig();
                formatConfig.setMaxAttempts(1);
                formatConfig.setEnableCircuitBreaker(false);
                return formatConfig;
                
            case CONFIGURATION_ERROR:
                // 配置错误不应该重试
                OcrRetryManager.RetryConfig configConfig = new OcrRetryManager.RetryConfig();
                configConfig.setMaxAttempts(1);
                configConfig.setEnableCircuitBreaker(false);
                return configConfig;
                
            case THREAD_POOL_ERROR:
                // 线程池错误需要等待资源释放
                OcrRetryManager.RetryConfig threadConfig = createDefaultConfig();
                threadConfig.setBaseDelayMs(2000);
                threadConfig.setMaxDelayMs(30000);
                threadConfig.setBackoffMultiplier(2.0);
                return threadConfig;
                
            case CACHE_ERROR:
                // 缓存错误可以快速重试
                return createFastRetryConfig();
                
            case UNKNOWN_ERROR:
            default:
                // 未知错误使用默认策略
                return createDefaultConfig();
        }
    }
    
    /**
     * 根据操作类型创建重试配置
     */
    public OcrRetryManager.RetryConfig createConfigForOperation(String operationType) {
        switch (operationType.toLowerCase()) {
            case "batch":
            case "batch_processing":
                // 批处理操作使用保守策略
                return createConservativeConfig();
                
            case "single":
            case "single_image":
                // 单图像处理使用默认策略
                return createDefaultConfig();
                
            case "preview":
            case "thumbnail":
                // 预览操作使用快速策略
                return createFastRetryConfig();
                
            case "critical":
            case "important":
                // 关键操作使用激进策略
                return createAggressiveConfig();
                
            case "background":
            case "async":
                // 后台操作使用激进策略，因为用户不会等待
                return createAggressiveConfig();
                
            default:
                return createDefaultConfig();
        }
    }
    
    /**
     * 根据图像大小创建重试配置
     */
    public OcrRetryManager.RetryConfig createConfigForImageSize(long imageSizeBytes) {
        if (imageSizeBytes < 100 * 1024) { // 小于100KB
            // 小图像使用快速重试
            return createFastRetryConfig();
        } else if (imageSizeBytes < 1024 * 1024) { // 小于1MB
            // 中等图像使用默认策略
            return createDefaultConfig();
        } else if (imageSizeBytes < 5 * 1024 * 1024) { // 小于5MB
            // 大图像使用保守策略
            return createConservativeConfig();
        } else {
            // 超大图像使用非常保守的策略
            OcrRetryManager.RetryConfig largeImageConfig = new OcrRetryManager.RetryConfig();
            largeImageConfig.setMaxAttempts(2);
            largeImageConfig.setBaseDelayMs(5000);
            largeImageConfig.setMaxDelayMs(120000); // 2分钟
            largeImageConfig.setBackoffMultiplier(4.0);
            largeImageConfig.setJitterFactor(0.1);
            largeImageConfig.setEnableCircuitBreaker(true);
            return largeImageConfig;
        }
    }
    
    /**
     * 根据系统负载创建重试配置
     */
    public OcrRetryManager.RetryConfig createConfigForSystemLoad(double cpuUsage, double memoryUsage) {
        if (cpuUsage > 0.8 || memoryUsage > 0.8) {
            // 高负载时使用保守策略
            OcrRetryManager.RetryConfig highLoadConfig = createConservativeConfig();
            highLoadConfig.setBaseDelayMs(5000);
            highLoadConfig.setMaxDelayMs(180000); // 3分钟
            return highLoadConfig;
        } else if (cpuUsage > 0.6 || memoryUsage > 0.6) {
            // 中等负载时使用默认策略但增加延迟
            OcrRetryManager.RetryConfig mediumLoadConfig = createDefaultConfig();
            mediumLoadConfig.setBaseDelayMs(2000);
            mediumLoadConfig.setMaxDelayMs(60000);
            return mediumLoadConfig;
        } else {
            // 低负载时可以使用激进策略
            return createAggressiveConfig();
        }
    }
    
    /**
     * 创建自定义重试配置
     */
    public OcrRetryManager.RetryConfig createCustomConfig(int maxAttempts, long baseDelayMs, 
                                                         long maxDelayMs, double backoffMultiplier, 
                                                         double jitterFactor, boolean enableCircuitBreaker) {
        OcrRetryManager.RetryConfig config = new OcrRetryManager.RetryConfig();
        config.setMaxAttempts(maxAttempts);
        config.setBaseDelayMs(baseDelayMs);
        config.setMaxDelayMs(maxDelayMs);
        config.setBackoffMultiplier(backoffMultiplier);
        config.setJitterFactor(jitterFactor);
        config.setEnableCircuitBreaker(enableCircuitBreaker);
        return config;
    }
    
    /**
     * 根据历史成功率调整重试配置
     */
    public OcrRetryManager.RetryConfig adjustConfigBySuccessRate(OcrRetryManager.RetryConfig baseConfig, 
                                                                double successRate) {
        OcrRetryManager.RetryConfig adjustedConfig = new OcrRetryManager.RetryConfig();
        
        // 复制基础配置
        adjustedConfig.setMaxAttempts(baseConfig.getMaxAttempts());
        adjustedConfig.setBaseDelayMs(baseConfig.getBaseDelayMs());
        adjustedConfig.setMaxDelayMs(baseConfig.getMaxDelayMs());
        adjustedConfig.setBackoffMultiplier(baseConfig.getBackoffMultiplier());
        adjustedConfig.setJitterFactor(baseConfig.getJitterFactor());
        adjustedConfig.setEnableCircuitBreaker(baseConfig.isEnableCircuitBreaker());
        
        if (successRate < 0.5) {
            // 成功率低，减少重试次数，增加延迟
            adjustedConfig.setMaxAttempts(Math.max(1, baseConfig.getMaxAttempts() - 1));
            adjustedConfig.setBaseDelayMs((long) (baseConfig.getBaseDelayMs() * 1.5));
            adjustedConfig.setBackoffMultiplier(baseConfig.getBackoffMultiplier() * 1.2);
        } else if (successRate > 0.8) {
            // 成功率高，可以增加重试次数，减少延迟
            adjustedConfig.setMaxAttempts(baseConfig.getMaxAttempts() + 1);
            adjustedConfig.setBaseDelayMs((long) (baseConfig.getBaseDelayMs() * 0.8));
            adjustedConfig.setBackoffMultiplier(baseConfig.getBackoffMultiplier() * 0.9);
        }
        
        return adjustedConfig;
    }
}
