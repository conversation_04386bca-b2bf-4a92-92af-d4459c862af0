package com.talkweb.ai.converter.util.markdown.converter;

import com.talkweb.ai.converter.util.markdown.ConversionContext;
import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;

import java.util.Set;

import static com.talkweb.ai.converter.util.markdown.MarkdownConstants.*;

/**
 * Converter for HTML media elements (img, video, audio, etc.)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class MediaConverter extends AbstractElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(TAG_IMG);
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String tagName = element.tagName().toLowerCase();
        
        switch (tagName) {
            case TAG_IMG:
                convertImage(element, builder, context);
                break;
            default:
                throw new ConversionException("Unsupported media tag: " + tagName);
        }
    }
    
    @Override
    public boolean shouldProcessChildren(Element element, ConversionContext context) {
        // Media elements typically don't have meaningful children for markdown
        return false;
    }
    
    @Override
    public int getPriority() {
        return 75; // Medium-high priority for media
    }
    
    /**
     * Converts image elements
     * 
     * @param element the img element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertImage(Element element, MarkdownBuilder builder, ConversionContext context) {
        String src = getAttribute(element, ATTR_SRC);
        String alt = getAttribute(element, ATTR_ALT);
        String title = getAttribute(element, ATTR_TITLE);
        
        // If no src, skip the image
        if (src.isEmpty()) {
            return;
        }
        
        // Use alt text, or fallback to filename or empty string
        if (alt.isEmpty()) {
            alt = extractFilenameFromUrl(src);
        }
        
        // Create the markdown image
        if (title.isEmpty()) {
            builder.image(alt, src);
        } else {
            builder.image(alt, src, title);
        }
    }
    
    /**
     * Extracts filename from URL for alt text fallback
     * 
     * @param url the image URL
     * @return the filename or empty string
     */
    private String extractFilenameFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return EMPTY_STRING;
        }
        
        try {
            // Remove query parameters and fragments
            int queryIndex = url.indexOf('?');
            if (queryIndex != -1) {
                url = url.substring(0, queryIndex);
            }
            
            int fragmentIndex = url.indexOf('#');
            if (fragmentIndex != -1) {
                url = url.substring(0, fragmentIndex);
            }
            
            // Extract filename
            int lastSlash = url.lastIndexOf('/');
            if (lastSlash != -1 && lastSlash < url.length() - 1) {
                return url.substring(lastSlash + 1);
            }
            
            return url;
        } catch (Exception e) {
            return EMPTY_STRING;
        }
    }
    
    @Override
    public void validate(Element element, ConversionContext context) throws ConversionException {
        super.validate(element, context);
        
        // Additional validation for images
        if (context.isStrictMode()) {
            String src = getAttribute(element, ATTR_SRC);
            if (!src.isEmpty() && !isValidImageUrl(src)) {
                throw new ConversionException("Invalid image URL in strict mode: " + src);
            }
        }
    }
    
    /**
     * Validates if an image URL is well-formed (basic validation)
     * 
     * @param url the URL to validate
     * @return true if the URL appears valid
     */
    private boolean isValidImageUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        url = url.trim().toLowerCase();
        
        // Check for valid URL schemes
        boolean hasValidScheme = url.startsWith("http://") || 
                                url.startsWith("https://") || 
                                url.startsWith("data:") ||
                                url.startsWith("/") || 
                                url.startsWith("./") || 
                                url.startsWith("../");
        
        if (!hasValidScheme) {
            return false;
        }
        
        // Check for common image extensions (basic check)
        String[] imageExtensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".webp", ".ico"};
        for (String ext : imageExtensions) {
            if (url.contains(ext)) {
                return true;
            }
        }
        
        // Allow data URLs and URLs without extensions (might be dynamic)
        return url.startsWith("data:image/") || !url.contains(".");
    }
}
