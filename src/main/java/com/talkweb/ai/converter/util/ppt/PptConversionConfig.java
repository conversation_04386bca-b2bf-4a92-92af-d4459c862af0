package com.talkweb.ai.converter.util.ppt;

/**
 * Configuration class for PowerPoint to Markdown conversion
 * 
 * Provides comprehensive configuration options for customizing the conversion process,
 * including content extraction preferences, output formatting, and performance settings.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class PptConversionConfig {
    
    // Content extraction options
    private boolean includeMetadata = true;
    private boolean includeSpeakerNotes = false;
    private boolean extractImages = true;
    private boolean extractTables = true;
    private boolean extractCharts = false; // Charts as images by default
    private boolean includeSlideNumbers = true;
    private boolean includeHiddenSlides = false;
    
    // Output formatting options
    private boolean strictMode = false;
    private boolean preserveFormatting = true;
    private boolean generateTableOfContents = false;
    private String imageOutputDirectory = "images";
    private String imageFormat = "png"; // png, jpg, gif
    private int maxImageWidth = 800;
    private int maxImageHeight = 600;
    
    // Text processing options
    private boolean normalizeWhitespace = true;
    private boolean convertBulletsToMarkdown = true;
    private boolean preserveTextBoxes = true;
    private boolean extractTextFromShapes = true;
    
    // Performance and memory options
    private boolean enableCaching = true;
    private int maxCacheSize = 100; // Maximum number of cached objects
    private boolean streamProcessing = false; // For very large presentations
    private int processingTimeout = 300; // seconds
    
    // Error handling options
    private boolean skipCorruptedSlides = true;
    private boolean logDetailedErrors = true;
    private boolean generateErrorReport = false;
    
    /**
     * Default constructor with sensible defaults
     */
    public PptConversionConfig() {
        // All defaults are set in field declarations
    }
    
    /**
     * Copy constructor
     */
    public PptConversionConfig(PptConversionConfig other) {
        if (other != null) {
            this.includeMetadata = other.includeMetadata;
            this.includeSpeakerNotes = other.includeSpeakerNotes;
            this.extractImages = other.extractImages;
            this.extractTables = other.extractTables;
            this.extractCharts = other.extractCharts;
            this.includeSlideNumbers = other.includeSlideNumbers;
            this.includeHiddenSlides = other.includeHiddenSlides;
            this.strictMode = other.strictMode;
            this.preserveFormatting = other.preserveFormatting;
            this.generateTableOfContents = other.generateTableOfContents;
            this.imageOutputDirectory = other.imageOutputDirectory;
            this.imageFormat = other.imageFormat;
            this.maxImageWidth = other.maxImageWidth;
            this.maxImageHeight = other.maxImageHeight;
            this.normalizeWhitespace = other.normalizeWhitespace;
            this.convertBulletsToMarkdown = other.convertBulletsToMarkdown;
            this.preserveTextBoxes = other.preserveTextBoxes;
            this.extractTextFromShapes = other.extractTextFromShapes;
            this.enableCaching = other.enableCaching;
            this.maxCacheSize = other.maxCacheSize;
            this.streamProcessing = other.streamProcessing;
            this.processingTimeout = other.processingTimeout;
            this.skipCorruptedSlides = other.skipCorruptedSlides;
            this.logDetailedErrors = other.logDetailedErrors;
            this.generateErrorReport = other.generateErrorReport;
        }
    }
    
    // Content extraction getters and setters
    public boolean isIncludeMetadata() {
        return includeMetadata;
    }
    
    public PptConversionConfig setIncludeMetadata(boolean includeMetadata) {
        this.includeMetadata = includeMetadata;
        return this;
    }
    
    public boolean isIncludeSpeakerNotes() {
        return includeSpeakerNotes;
    }
    
    public PptConversionConfig setIncludeSpeakerNotes(boolean includeSpeakerNotes) {
        this.includeSpeakerNotes = includeSpeakerNotes;
        return this;
    }
    
    public boolean isExtractImages() {
        return extractImages;
    }
    
    public PptConversionConfig setExtractImages(boolean extractImages) {
        this.extractImages = extractImages;
        return this;
    }
    
    public boolean isExtractTables() {
        return extractTables;
    }
    
    public PptConversionConfig setExtractTables(boolean extractTables) {
        this.extractTables = extractTables;
        return this;
    }
    
    public boolean isExtractCharts() {
        return extractCharts;
    }
    
    public PptConversionConfig setExtractCharts(boolean extractCharts) {
        this.extractCharts = extractCharts;
        return this;
    }
    
    public boolean isIncludeSlideNumbers() {
        return includeSlideNumbers;
    }
    
    public PptConversionConfig setIncludeSlideNumbers(boolean includeSlideNumbers) {
        this.includeSlideNumbers = includeSlideNumbers;
        return this;
    }
    
    public boolean isIncludeHiddenSlides() {
        return includeHiddenSlides;
    }
    
    public PptConversionConfig setIncludeHiddenSlides(boolean includeHiddenSlides) {
        this.includeHiddenSlides = includeHiddenSlides;
        return this;
    }
    
    // Output formatting getters and setters
    public boolean isStrictMode() {
        return strictMode;
    }
    
    public PptConversionConfig setStrictMode(boolean strictMode) {
        this.strictMode = strictMode;
        return this;
    }
    
    public boolean isPreserveFormatting() {
        return preserveFormatting;
    }
    
    public PptConversionConfig setPreserveFormatting(boolean preserveFormatting) {
        this.preserveFormatting = preserveFormatting;
        return this;
    }
    
    public boolean isGenerateTableOfContents() {
        return generateTableOfContents;
    }
    
    public PptConversionConfig setGenerateTableOfContents(boolean generateTableOfContents) {
        this.generateTableOfContents = generateTableOfContents;
        return this;
    }
    
    public String getImageOutputDirectory() {
        return imageOutputDirectory;
    }
    
    public PptConversionConfig setImageOutputDirectory(String imageOutputDirectory) {
        this.imageOutputDirectory = imageOutputDirectory != null ? imageOutputDirectory : "images";
        return this;
    }
    
    public String getImageFormat() {
        return imageFormat;
    }
    
    public PptConversionConfig setImageFormat(String imageFormat) {
        this.imageFormat = imageFormat != null ? imageFormat.toLowerCase() : "png";
        return this;
    }
    
    public int getMaxImageWidth() {
        return maxImageWidth;
    }
    
    public PptConversionConfig setMaxImageWidth(int maxImageWidth) {
        this.maxImageWidth = Math.max(100, maxImageWidth);
        return this;
    }
    
    public int getMaxImageHeight() {
        return maxImageHeight;
    }
    
    public PptConversionConfig setMaxImageHeight(int maxImageHeight) {
        this.maxImageHeight = Math.max(100, maxImageHeight);
        return this;
    }
    
    // Text processing getters and setters
    public boolean isNormalizeWhitespace() {
        return normalizeWhitespace;
    }
    
    public PptConversionConfig setNormalizeWhitespace(boolean normalizeWhitespace) {
        this.normalizeWhitespace = normalizeWhitespace;
        return this;
    }
    
    public boolean isConvertBulletsToMarkdown() {
        return convertBulletsToMarkdown;
    }
    
    public PptConversionConfig setConvertBulletsToMarkdown(boolean convertBulletsToMarkdown) {
        this.convertBulletsToMarkdown = convertBulletsToMarkdown;
        return this;
    }
    
    public boolean isPreserveTextBoxes() {
        return preserveTextBoxes;
    }
    
    public PptConversionConfig setPreserveTextBoxes(boolean preserveTextBoxes) {
        this.preserveTextBoxes = preserveTextBoxes;
        return this;
    }
    
    public boolean isExtractTextFromShapes() {
        return extractTextFromShapes;
    }
    
    public PptConversionConfig setExtractTextFromShapes(boolean extractTextFromShapes) {
        this.extractTextFromShapes = extractTextFromShapes;
        return this;
    }
    
    // Performance getters and setters
    public boolean isEnableCaching() {
        return enableCaching;
    }
    
    public PptConversionConfig setEnableCaching(boolean enableCaching) {
        this.enableCaching = enableCaching;
        return this;
    }
    
    public int getMaxCacheSize() {
        return maxCacheSize;
    }
    
    public PptConversionConfig setMaxCacheSize(int maxCacheSize) {
        this.maxCacheSize = Math.max(10, maxCacheSize);
        return this;
    }
    
    public boolean isStreamProcessing() {
        return streamProcessing;
    }
    
    public PptConversionConfig setStreamProcessing(boolean streamProcessing) {
        this.streamProcessing = streamProcessing;
        return this;
    }
    
    public int getProcessingTimeout() {
        return processingTimeout;
    }
    
    public PptConversionConfig setProcessingTimeout(int processingTimeout) {
        this.processingTimeout = Math.max(30, processingTimeout);
        return this;
    }
    
    // Error handling getters and setters
    public boolean isSkipCorruptedSlides() {
        return skipCorruptedSlides;
    }
    
    public PptConversionConfig setSkipCorruptedSlides(boolean skipCorruptedSlides) {
        this.skipCorruptedSlides = skipCorruptedSlides;
        return this;
    }
    
    public boolean isLogDetailedErrors() {
        return logDetailedErrors;
    }
    
    public PptConversionConfig setLogDetailedErrors(boolean logDetailedErrors) {
        this.logDetailedErrors = logDetailedErrors;
        return this;
    }
    
    public boolean isGenerateErrorReport() {
        return generateErrorReport;
    }
    
    public PptConversionConfig setGenerateErrorReport(boolean generateErrorReport) {
        this.generateErrorReport = generateErrorReport;
        return this;
    }
    
    /**
     * Creates a configuration optimized for high fidelity conversion
     */
    public static PptConversionConfig createHighFidelityConfig() {
        return new PptConversionConfig()
            .setIncludeMetadata(true)
            .setIncludeSpeakerNotes(true)
            .setExtractImages(true)
            .setExtractTables(true)
            .setExtractCharts(true)
            .setPreserveFormatting(true)
            .setExtractTextFromShapes(true)
            .setStrictMode(false);
    }
    
    /**
     * Creates a configuration optimized for performance
     */
    public static PptConversionConfig createPerformanceConfig() {
        return new PptConversionConfig()
            .setIncludeMetadata(false)
            .setIncludeSpeakerNotes(false)
            .setExtractImages(false)
            .setExtractTables(true)
            .setExtractCharts(false)
            .setPreserveFormatting(false)
            .setEnableCaching(true)
            .setStreamProcessing(true);
    }
    
    /**
     * Creates a strict mode configuration
     */
    public static PptConversionConfig createStrictConfig() {
        return new PptConversionConfig()
            .setStrictMode(true)
            .setSkipCorruptedSlides(false)
            .setLogDetailedErrors(true)
            .setGenerateErrorReport(true);
    }
    
    @Override
    public String toString() {
        return "PptConversionConfig{" +
            "includeMetadata=" + includeMetadata +
            ", includeSpeakerNotes=" + includeSpeakerNotes +
            ", extractImages=" + extractImages +
            ", extractTables=" + extractTables +
            ", strictMode=" + strictMode +
            ", imageFormat='" + imageFormat + '\'' +
            '}';
    }
}
