package com.talkweb.ai.converter.util.word.converter;

import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import com.talkweb.ai.converter.util.word.WordConversionContext;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.hwpf.usermodel.Table;
import org.apache.poi.hwpf.usermodel.TableRow;
import org.apache.poi.hwpf.usermodel.TableCell;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Converter for Word tables to Markdown
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class WordTableConverter implements WordElementConverter<Object> {
    
    private static final Logger logger = LoggerFactory.getLogger(WordTableConverter.class);
    
    @Override
    public boolean canConvert(Object element, WordConversionContext context) {
        return element instanceof XWPFTable || element instanceof Table;
    }
    
    @Override
    public void convert(Object element, MarkdownBuilder builder, WordConversionContext context) 
            throws ConversionException {
        
        validate(element, context);
        
        context.setInTable(true);
        
        try {
            if (element instanceof XWPFTable) {
                convertXWPFTable((XWPFTable) element, builder, context);
            } else if (element instanceof Table) {
                convertHWPFTable((Table) element, builder, context);
            }
        } finally {
            context.setInTable(false);
        }
    }
    
    /**
     * Converts XWPF (docx) table
     */
    private void convertXWPFTable(XWPFTable table, MarkdownBuilder builder, 
                                 WordConversionContext context) throws ConversionException {
        
        List<XWPFTableRow> rows = table.getRows();
        if (rows.isEmpty()) {
            return;
        }
        
        builder.newline();
        
        // Extract table data
        List<List<String>> tableData = new ArrayList<>();
        int maxCols = 0;
        
        for (XWPFTableRow row : rows) {
            List<String> rowData = new ArrayList<>();
            List<XWPFTableCell> cells = row.getTableCells();
            
            for (XWPFTableCell cell : cells) {
                String cellContent = extractCellContent(cell, context);
                rowData.add(cellContent);
            }
            
            tableData.add(rowData);
            maxCols = Math.max(maxCols, rowData.size());
        }
        
        if (tableData.isEmpty() || maxCols == 0) {
            return;
        }
        
        // Normalize table data (ensure all rows have same number of columns)
        for (List<String> row : tableData) {
            while (row.size() < maxCols) {
                row.add("");
            }
        }
        
        // Write table in markdown format
        writeMarkdownTable(tableData, builder, context);

        builder.newline();
    }
    
    /**
     * Converts HWPF (doc) table
     */
    private void convertHWPFTable(Table table, MarkdownBuilder builder, 
                                 WordConversionContext context) throws ConversionException {
        
        int numRows = table.numRows();
        if (numRows == 0) {
            return;
        }
        
        builder.newline();
        
        // Extract table data
        List<List<String>> tableData = new ArrayList<>();
        int maxCols = 0;
        
        for (int i = 0; i < numRows; i++) {
            TableRow row = table.getRow(i);
            List<String> rowData = new ArrayList<>();
            int numCells = row.numCells();
            
            for (int j = 0; j < numCells; j++) {
                TableCell cell = row.getCell(j);
                String cellContent = extractHWPFCellContent(cell, context);
                rowData.add(cellContent);
            }
            
            tableData.add(rowData);
            maxCols = Math.max(maxCols, rowData.size());
        }
        
        if (tableData.isEmpty() || maxCols == 0) {
            return;
        }
        
        // Normalize table data
        for (List<String> row : tableData) {
            while (row.size() < maxCols) {
                row.add("");
            }
        }
        
        // Write table in markdown format
        writeMarkdownTable(tableData, builder, context);

        builder.newline();
    }
    
    /**
     * Extracts content from XWPF table cell
     */
    private String extractCellContent(XWPFTableCell cell, WordConversionContext context) {
        StringBuilder content = new StringBuilder();
        
        for (XWPFParagraph paragraph : cell.getParagraphs()) {
            String text = paragraph.getText();
            if (text != null && !text.trim().isEmpty()) {
                if (content.length() > 0) {
                    content.append(" ");
                }
                content.append(text.trim());
            }
        }
        
        String result = content.toString();
        
        // Escape pipe characters and clean up
        result = result.replace("|", "\\|")
                      .replace("\n", " ")
                      .replace("\r", " ")
                      .replaceAll("\\s+", " ")
                      .trim();
        
        return result;
    }
    
    /**
     * Extracts content from HWPF table cell
     */
    private String extractHWPFCellContent(TableCell cell, WordConversionContext context) {
        String text = cell.text();
        if (text == null) {
            return "";
        }
        
        // Clean up the text
        text = text.replace("|", "\\|")
                  .replace("\n", " ")
                  .replace("\r", " ")
                  .replaceAll("\\s+", " ")
                  .trim();
        
        return text;
    }
    
    /**
     * Writes table data in markdown format
     */
    private void writeMarkdownTable(List<List<String>> tableData, MarkdownBuilder builder, 
                                   WordConversionContext context) {
        
        if (tableData.isEmpty()) {
            return;
        }
        
        int maxCols = tableData.get(0).size();
        
        // Write header row (first row)
        List<String> headerRow = tableData.get(0);
        builder.append("|");
        for (String cell : headerRow) {
            builder.append(" ").append(cell).append(" |");
        }
        builder.newline();

        // Write separator row
        builder.append("|");
        for (int i = 0; i < maxCols; i++) {
            builder.append("---|");
        }
        builder.newline();

        // Write data rows (skip first row as it's the header)
        for (int i = 1; i < tableData.size(); i++) {
            List<String> row = tableData.get(i);
            builder.append("|");
            for (String cell : row) {
                builder.append(" ").append(cell).append(" |");
            }
            builder.newline();
        }
    }
    
    /**
     * Writes enhanced table with formatting preservation
     */
    private void writeEnhancedMarkdownTable(List<List<String>> tableData, MarkdownBuilder builder, 
                                           WordConversionContext context) {
        
        if (!context.getOptions().isPreserveTableFormatting()) {
            writeMarkdownTable(tableData, builder, context);
            return;
        }
        
        // For enhanced formatting, we could add HTML table tags
        // This would preserve more complex table structures
        builder.append("<table>").newline();

        // Write header
        if (!tableData.isEmpty()) {
            builder.append("<thead>").newline();
            builder.append("<tr>");
            for (String cell : tableData.get(0)) {
                builder.append("<th>").append(cell).append("</th>");
            }
            builder.append("</tr>").newline();
            builder.append("</thead>").newline();
        }

        // Write body
        if (tableData.size() > 1) {
            builder.append("<tbody>").newline();
            for (int i = 1; i < tableData.size(); i++) {
                builder.append("<tr>");
                for (String cell : tableData.get(i)) {
                    builder.append("<td>").append(cell).append("</td>");
                }
                builder.append("</tr>").newline();
            }
            builder.append("</tbody>").newline();
        }

        builder.append("</table>").newline();
    }
    
    @Override
    public void beforeConvert(Object element, MarkdownBuilder builder, WordConversionContext context) {
        context.setInTable(true);
    }
    
    @Override
    public void afterConvert(Object element, MarkdownBuilder builder, WordConversionContext context) {
        context.setInTable(false);
    }
    
    @Override
    public int getPriority() {
        return 80; // High priority for tables
    }
    
    @Override
    public boolean shouldProcessChildren(Object element, WordConversionContext context) {
        return false; // We handle children ourselves
    }
}
