package com.talkweb.ai.converter.util.image;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.*;
import java.util.List;

/**
 * 布局分析器
 * 
 * 实现页面布局检测算法，包括文本区域识别、
 * 图像区域检测和布局结构分析功能。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class LayoutAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(LayoutAnalyzer.class);
    
    /**
     * 布局分析配置
     */
    public static class LayoutAnalysisConfig {
        private int minTextRegionWidth = 50;
        private int minTextRegionHeight = 20;
        private int minImageRegionWidth = 30;
        private int minImageRegionHeight = 30;
        private double textDensityThreshold = 0.1;
        private double imageDensityThreshold = 0.3;
        private boolean enableColumnDetection = true;
        private boolean enableRegionMerging = true;
        private int mergingDistance = 10;
        private double confidenceThreshold = 0.6;
        
        // Getters and setters
        public int getMinTextRegionWidth() { return minTextRegionWidth; }
        public void setMinTextRegionWidth(int minTextRegionWidth) { this.minTextRegionWidth = minTextRegionWidth; }
        
        public int getMinTextRegionHeight() { return minTextRegionHeight; }
        public void setMinTextRegionHeight(int minTextRegionHeight) { this.minTextRegionHeight = minTextRegionHeight; }
        
        public int getMinImageRegionWidth() { return minImageRegionWidth; }
        public void setMinImageRegionWidth(int minImageRegionWidth) { this.minImageRegionWidth = minImageRegionWidth; }
        
        public int getMinImageRegionHeight() { return minImageRegionHeight; }
        public void setMinImageRegionHeight(int minImageRegionHeight) { this.minImageRegionHeight = minImageRegionHeight; }
        
        public double getTextDensityThreshold() { return textDensityThreshold; }
        public void setTextDensityThreshold(double textDensityThreshold) { this.textDensityThreshold = textDensityThreshold; }
        
        public double getImageDensityThreshold() { return imageDensityThreshold; }
        public void setImageDensityThreshold(double imageDensityThreshold) { this.imageDensityThreshold = imageDensityThreshold; }
        
        public boolean isEnableColumnDetection() { return enableColumnDetection; }
        public void setEnableColumnDetection(boolean enableColumnDetection) { this.enableColumnDetection = enableColumnDetection; }
        
        public boolean isEnableRegionMerging() { return enableRegionMerging; }
        public void setEnableRegionMerging(boolean enableRegionMerging) { this.enableRegionMerging = enableRegionMerging; }
        
        public int getMergingDistance() { return mergingDistance; }
        public void setMergingDistance(int mergingDistance) { this.mergingDistance = mergingDistance; }
        
        public double getConfidenceThreshold() { return confidenceThreshold; }
        public void setConfidenceThreshold(double confidenceThreshold) { this.confidenceThreshold = confidenceThreshold; }
    }
    
    /**
     * 布局分析结果
     */
    public static class LayoutAnalysisResult {
        private final List<DocumentRegion> regions;
        private final LayoutStructure structure;
        private final boolean success;
        private final String errorMessage;
        private final Map<String, Object> metadata;
        
        public LayoutAnalysisResult(List<DocumentRegion> regions, LayoutStructure structure) {
            this.regions = regions != null ? new ArrayList<>(regions) : new ArrayList<>();
            this.structure = structure;
            this.success = true;
            this.errorMessage = null;
            this.metadata = new HashMap<>();
        }
        
        public LayoutAnalysisResult(String errorMessage) {
            this.regions = new ArrayList<>();
            this.structure = null;
            this.success = false;
            this.errorMessage = errorMessage;
            this.metadata = new HashMap<>();
        }
        
        // Getters
        public List<DocumentRegion> getRegions() { return new ArrayList<>(regions); }
        public LayoutStructure getStructure() { return structure; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
        
        public void addMetadata(String key, Object value) {
            this.metadata.put(key, value);
        }
    }
    
    /**
     * 文档区域
     */
    public static class DocumentRegion {
        private final Rectangle bounds;
        private final RegionType type;
        private final double confidence;
        private final Map<String, Object> properties;
        
        public DocumentRegion(Rectangle bounds, RegionType type, double confidence) {
            this.bounds = new Rectangle(bounds);
            this.type = type;
            this.confidence = confidence;
            this.properties = new HashMap<>();
        }
        
        // Getters
        public Rectangle getBounds() { return new Rectangle(bounds); }
        public RegionType getType() { return type; }
        public double getConfidence() { return confidence; }
        public Map<String, Object> getProperties() { return new HashMap<>(properties); }
        
        public void setProperty(String key, Object value) {
            this.properties.put(key, value);
        }
        
        public Object getProperty(String key) {
            return properties.get(key);
        }
    }
    
    /**
     * 区域类型
     */
    public enum RegionType {
        TEXT,           // 文本区域
        IMAGE,          // 图像区域
        TABLE,          // 表格区域
        HEADER,         // 页眉区域
        FOOTER,         // 页脚区域
        COLUMN,         // 列区域
        PARAGRAPH,      // 段落区域
        UNKNOWN         // 未知区域
    }
    
    /**
     * 布局结构
     */
    public static class LayoutStructure {
        private final int columns;
        private final List<Rectangle> columnBounds;
        private final boolean isMultiColumn;
        private final double confidence;
        
        public LayoutStructure(int columns, List<Rectangle> columnBounds, double confidence) {
            this.columns = columns;
            this.columnBounds = columnBounds != null ? new ArrayList<>(columnBounds) : new ArrayList<>();
            this.isMultiColumn = columns > 1;
            this.confidence = confidence;
        }
        
        // Getters
        public int getColumns() { return columns; }
        public List<Rectangle> getColumnBounds() { return new ArrayList<>(columnBounds); }
        public boolean isMultiColumn() { return isMultiColumn; }
        public double getConfidence() { return confidence; }
    }
    
    /**
     * 分析文档布局
     * 
     * @param image 输入图像
     * @return 布局分析结果
     */
    public LayoutAnalysisResult analyzeLayout(BufferedImage image) {
        return analyzeLayout(image, new LayoutAnalysisConfig());
    }
    
    /**
     * 分析文档布局（带配置）
     * 
     * @param image 输入图像
     * @param config 分析配置
     * @return 布局分析结果
     */
    public LayoutAnalysisResult analyzeLayout(BufferedImage image, LayoutAnalysisConfig config) {
        if (image == null) {
            return new LayoutAnalysisResult("Input image is null");
        }
        
        if (config == null) {
            config = new LayoutAnalysisConfig();
        }
        
        try {
            logger.debug("Starting layout analysis for image {}x{}", image.getWidth(), image.getHeight());
            
            // 1. 图像预处理
            BufferedImage processedImage = preprocessForLayoutAnalysis(image);
            
            // 2. 区域检测
            List<DocumentRegion> regions = detectRegions(processedImage, config);
            
            // 3. 区域分类
            regions = classifyRegions(regions, processedImage, config);
            
            // 4. 区域合并（如果启用）
            if (config.isEnableRegionMerging()) {
                regions = mergeRegions(regions, config);
            }
            
            // 5. 布局结构分析
            LayoutStructure structure = analyzeLayoutStructure(regions, image, config);
            
            // 6. 过滤和验证
            regions = filterAndValidateRegions(regions, config);
            
            LayoutAnalysisResult result = new LayoutAnalysisResult(regions, structure);
            result.addMetadata("imageSize", image.getWidth() + "x" + image.getHeight());
            result.addMetadata("regionsDetected", regions.size());
            result.addMetadata("layoutColumns", structure.getColumns());
            result.addMetadata("isMultiColumn", structure.isMultiColumn());
            
            logger.debug("Layout analysis completed. Found {} regions with {} columns", 
                        regions.size(), structure.getColumns());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Layout analysis failed", e);
            return new LayoutAnalysisResult("Layout analysis failed: " + e.getMessage());
        }
    }
    
    /**
     * 图像预处理用于布局分析
     */
    private BufferedImage preprocessForLayoutAnalysis(BufferedImage image) {
        // 转换为灰度图像
        BufferedImage grayImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g2d = grayImage.createGraphics();
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        
        return grayImage;
    }
    
    /**
     * 检测文档区域
     */
    private List<DocumentRegion> detectRegions(BufferedImage image, LayoutAnalysisConfig config) {
        List<DocumentRegion> regions = new ArrayList<>();
        
        // 使用连通组件分析检测区域
        List<Rectangle> components = findConnectedComponents(image, config);
        
        // 为每个组件创建区域
        for (Rectangle component : components) {
            if (isValidRegion(component, config)) {
                double confidence = calculateRegionConfidence(component, image);
                DocumentRegion region = new DocumentRegion(component, RegionType.UNKNOWN, confidence);
                regions.add(region);
            }
        }
        
        return regions;
    }
    
    /**
     * 查找连通组件
     */
    private List<Rectangle> findConnectedComponents(BufferedImage image, LayoutAnalysisConfig config) {
        List<Rectangle> components = new ArrayList<>();
        int width = image.getWidth();
        int height = image.getHeight();
        boolean[][] visited = new boolean[height][width];
        
        // 简化的连通组件分析
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                if (!visited[y][x] && isTextPixel(image, x, y)) {
                    Rectangle component = floodFill(image, x, y, visited);
                    if (component != null) {
                        components.add(component);
                    }
                }
            }
        }
        
        return components;
    }
    
    /**
     * 判断是否为文本像素
     */
    private boolean isTextPixel(BufferedImage image, int x, int y) {
        int rgb = image.getRGB(x, y);
        int gray = (rgb >> 16) & 0xFF; // 获取灰度值
        return gray < 128; // 暗色像素认为是文本
    }
    
    /**
     * 洪水填充算法
     */
    private Rectangle floodFill(BufferedImage image, int startX, int startY, boolean[][] visited) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        if (startX < 0 || startX >= width || startY < 0 || startY >= height || visited[startY][startX]) {
            return null;
        }
        
        Stack<Point> stack = new Stack<>();
        stack.push(new Point(startX, startY));
        
        int minX = startX, maxX = startX;
        int minY = startY, maxY = startY;
        int pixelCount = 0;
        
        while (!stack.isEmpty()) {
            Point p = stack.pop();
            int x = p.x, y = p.y;
            
            if (x < 0 || x >= width || y < 0 || y >= height || visited[y][x] || !isTextPixel(image, x, y)) {
                continue;
            }
            
            visited[y][x] = true;
            pixelCount++;
            
            minX = Math.min(minX, x);
            maxX = Math.max(maxX, x);
            minY = Math.min(minY, y);
            maxY = Math.max(maxY, y);
            
            // 添加相邻像素
            stack.push(new Point(x + 1, y));
            stack.push(new Point(x - 1, y));
            stack.push(new Point(x, y + 1));
            stack.push(new Point(x, y - 1));
        }
        
        // 如果组件太小，忽略
        if (pixelCount < 10) {
            return null;
        }
        
        return new Rectangle(minX, minY, maxX - minX + 1, maxY - minY + 1);
    }

    /**
     * 验证区域是否有效
     */
    private boolean isValidRegion(Rectangle region, LayoutAnalysisConfig config) {
        return region.width >= config.getMinTextRegionWidth() &&
               region.height >= config.getMinTextRegionHeight();
    }

    /**
     * 计算区域置信度
     */
    private double calculateRegionConfidence(Rectangle region, BufferedImage image) {
        // 基于区域大小和像素密度计算置信度
        int area = region.width * region.height;
        int textPixels = 0;

        for (int y = region.y; y < region.y + region.height; y++) {
            for (int x = region.x; x < region.x + region.width; x++) {
                if (x < image.getWidth() && y < image.getHeight() && isTextPixel(image, x, y)) {
                    textPixels++;
                }
            }
        }

        double density = (double) textPixels / area;
        return Math.min(1.0, density * 5.0); // 归一化到0-1范围
    }

    /**
     * 分类区域
     */
    private List<DocumentRegion> classifyRegions(List<DocumentRegion> regions,
                                               BufferedImage image, LayoutAnalysisConfig config) {
        List<DocumentRegion> classifiedRegions = new ArrayList<>();

        for (DocumentRegion region : regions) {
            RegionType type = classifyRegion(region, image, config);
            double confidence = region.getConfidence();

            // 根据分类结果调整置信度
            if (type != RegionType.UNKNOWN) {
                confidence = Math.min(1.0, confidence * 1.2);
            }

            DocumentRegion classifiedRegion = new DocumentRegion(region.getBounds(), type, confidence);

            // 复制属性
            for (Map.Entry<String, Object> entry : region.getProperties().entrySet()) {
                classifiedRegion.setProperty(entry.getKey(), entry.getValue());
            }

            classifiedRegions.add(classifiedRegion);
        }

        return classifiedRegions;
    }

    /**
     * 分类单个区域
     */
    private RegionType classifyRegion(DocumentRegion region, BufferedImage image, LayoutAnalysisConfig config) {
        Rectangle bounds = region.getBounds();

        // 基于位置判断页眉页脚
        if (bounds.y < image.getHeight() * 0.1) {
            return RegionType.HEADER;
        }
        if (bounds.y > image.getHeight() * 0.9) {
            return RegionType.FOOTER;
        }

        // 基于宽高比判断类型
        double aspectRatio = (double) bounds.width / bounds.height;

        if (aspectRatio > 3.0) {
            // 很宽的区域可能是标题或分隔线
            return RegionType.TEXT;
        } else if (aspectRatio < 0.5) {
            // 很高的区域可能是列
            return RegionType.COLUMN;
        } else if (aspectRatio > 0.8 && aspectRatio < 1.2) {
            // 接近正方形的区域可能是图像
            return RegionType.IMAGE;
        }

        // 基于内容密度判断
        double textDensity = calculateTextDensity(bounds, image);
        if (textDensity > config.getTextDensityThreshold()) {
            return RegionType.TEXT;
        }

        return RegionType.UNKNOWN;
    }

    /**
     * 计算文本密度
     */
    private double calculateTextDensity(Rectangle bounds, BufferedImage image) {
        int textPixels = 0;
        int totalPixels = bounds.width * bounds.height;

        for (int y = bounds.y; y < bounds.y + bounds.height; y++) {
            for (int x = bounds.x; x < bounds.x + bounds.width; x++) {
                if (x < image.getWidth() && y < image.getHeight() && isTextPixel(image, x, y)) {
                    textPixels++;
                }
            }
        }

        return (double) textPixels / totalPixels;
    }

    /**
     * 合并区域
     */
    private List<DocumentRegion> mergeRegions(List<DocumentRegion> regions, LayoutAnalysisConfig config) {
        List<DocumentRegion> mergedRegions = new ArrayList<>(regions);
        boolean merged = true;

        while (merged) {
            merged = false;

            for (int i = 0; i < mergedRegions.size() - 1; i++) {
                for (int j = i + 1; j < mergedRegions.size(); j++) {
                    DocumentRegion region1 = mergedRegions.get(i);
                    DocumentRegion region2 = mergedRegions.get(j);

                    if (shouldMergeRegions(region1, region2, config)) {
                        DocumentRegion mergedRegion = mergeRegionPair(region1, region2);
                        mergedRegions.remove(j);
                        mergedRegions.set(i, mergedRegion);
                        merged = true;
                        break;
                    }
                }
                if (merged) break;
            }
        }

        return mergedRegions;
    }

    /**
     * 判断是否应该合并两个区域
     */
    private boolean shouldMergeRegions(DocumentRegion region1, DocumentRegion region2, LayoutAnalysisConfig config) {
        // 只合并相同类型的区域
        if (region1.getType() != region2.getType()) {
            return false;
        }

        Rectangle bounds1 = region1.getBounds();
        Rectangle bounds2 = region2.getBounds();

        // 计算区域间距离
        double distance = calculateRegionDistance(bounds1, bounds2);

        return distance <= config.getMergingDistance();
    }

    /**
     * 计算区域间距离
     */
    private double calculateRegionDistance(Rectangle rect1, Rectangle rect2) {
        // 计算两个矩形的最短距离
        int dx = Math.max(0, Math.max(rect1.x - (rect2.x + rect2.width), rect2.x - (rect1.x + rect1.width)));
        int dy = Math.max(0, Math.max(rect1.y - (rect2.y + rect2.height), rect2.y - (rect1.y + rect1.height)));

        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 合并两个区域
     */
    private DocumentRegion mergeRegionPair(DocumentRegion region1, DocumentRegion region2) {
        Rectangle bounds1 = region1.getBounds();
        Rectangle bounds2 = region2.getBounds();
        Rectangle mergedBounds = bounds1.union(bounds2);

        double mergedConfidence = Math.max(region1.getConfidence(), region2.getConfidence());
        RegionType mergedType = region1.getType();

        DocumentRegion mergedRegion = new DocumentRegion(mergedBounds, mergedType, mergedConfidence);

        // 合并属性
        for (Map.Entry<String, Object> entry : region1.getProperties().entrySet()) {
            mergedRegion.setProperty(entry.getKey(), entry.getValue());
        }
        for (Map.Entry<String, Object> entry : region2.getProperties().entrySet()) {
            mergedRegion.setProperty(entry.getKey(), entry.getValue());
        }

        return mergedRegion;
    }

    /**
     * 分析布局结构
     */
    private LayoutStructure analyzeLayoutStructure(List<DocumentRegion> regions,
                                                 BufferedImage image, LayoutAnalysisConfig config) {
        if (!config.isEnableColumnDetection()) {
            return new LayoutStructure(1, Arrays.asList(new Rectangle(0, 0, image.getWidth(), image.getHeight())), 1.0);
        }

        // 检测列结构
        List<Rectangle> columns = detectColumns(regions, image);
        double confidence = calculateLayoutConfidence(columns, regions);

        return new LayoutStructure(columns.size(), columns, confidence);
    }

    /**
     * 检测列结构
     */
    private List<Rectangle> detectColumns(List<DocumentRegion> regions, BufferedImage image) {
        // 简化的列检测算法
        List<Rectangle> columns = new ArrayList<>();

        // 按X坐标排序区域
        List<DocumentRegion> sortedRegions = new ArrayList<>(regions);
        sortedRegions.sort(Comparator.comparingInt(r -> r.getBounds().x));

        if (sortedRegions.isEmpty()) {
            columns.add(new Rectangle(0, 0, image.getWidth(), image.getHeight()));
            return columns;
        }

        // 查找列边界
        List<Integer> columnBoundaries = new ArrayList<>();
        columnBoundaries.add(0);

        int lastX = 0;
        for (DocumentRegion region : sortedRegions) {
            int currentX = region.getBounds().x;
            if (currentX - lastX > image.getWidth() * 0.1) { // 10%的宽度作为列间距阈值
                columnBoundaries.add(currentX);
            }
            lastX = currentX + region.getBounds().width;
        }

        columnBoundaries.add(image.getWidth());

        // 创建列矩形
        for (int i = 0; i < columnBoundaries.size() - 1; i++) {
            int x = columnBoundaries.get(i);
            int width = columnBoundaries.get(i + 1) - x;
            columns.add(new Rectangle(x, 0, width, image.getHeight()));
        }

        return columns;
    }

    /**
     * 计算布局置信度
     */
    private double calculateLayoutConfidence(List<Rectangle> columns, List<DocumentRegion> regions) {
        if (columns.isEmpty() || regions.isEmpty()) {
            return 0.0;
        }

        // 基于区域在列中的分布计算置信度
        double totalConfidence = 0.0;

        for (Rectangle column : columns) {
            int regionsInColumn = 0;
            double columnConfidence = 0.0;

            for (DocumentRegion region : regions) {
                if (column.intersects(region.getBounds())) {
                    regionsInColumn++;
                    columnConfidence += region.getConfidence();
                }
            }

            if (regionsInColumn > 0) {
                totalConfidence += columnConfidence / regionsInColumn;
            }
        }

        return totalConfidence / columns.size();
    }

    /**
     * 过滤和验证区域
     */
    private List<DocumentRegion> filterAndValidateRegions(List<DocumentRegion> regions, LayoutAnalysisConfig config) {
        return regions.stream()
            .filter(region -> region.getConfidence() >= config.getConfidenceThreshold())
            .filter(region -> isValidRegion(region.getBounds(), config))
            .sorted(Comparator.comparingDouble(DocumentRegion::getConfidence).reversed())
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取布局分析调试信息
     */
    public String getDebugInfo(BufferedImage image, LayoutAnalysisConfig config) {
        StringBuilder debug = new StringBuilder();
        debug.append("=== Layout Analysis Debug Info ===\n");
        debug.append("Image Size: ").append(image.getWidth()).append("x").append(image.getHeight()).append("\n");
        debug.append("Config: ").append(configToString(config)).append("\n");

        try {
            LayoutAnalysisResult result = analyzeLayout(image, config);
            debug.append("Analysis Result: ").append(result.isSuccess() ? "SUCCESS" : "FAILED").append("\n");
            debug.append("Regions Found: ").append(result.getRegions().size()).append("\n");

            if (result.getStructure() != null) {
                debug.append("Layout Structure: ").append(result.getStructure().getColumns())
                     .append(" columns, confidence: ").append(String.format("%.3f", result.getStructure().getConfidence()))
                     .append("\n");
            }

            // 显示区域详情
            for (int i = 0; i < Math.min(10, result.getRegions().size()); i++) {
                DocumentRegion region = result.getRegions().get(i);
                debug.append("Region ").append(i + 1).append(": ")
                     .append(region.getType()).append(" at ")
                     .append(region.getBounds().toString())
                     .append(", confidence: ").append(String.format("%.3f", region.getConfidence()))
                     .append("\n");
            }

            debug.append("Metadata: ").append(result.getMetadata()).append("\n");

        } catch (Exception e) {
            debug.append("Error during analysis: ").append(e.getMessage()).append("\n");
        }

        debug.append("=== End Debug Info ===\n");
        return debug.toString();
    }

    /**
     * 配置转字符串
     */
    private String configToString(LayoutAnalysisConfig config) {
        return String.format("minTextRegion=%dx%d, columnDetection=%s, regionMerging=%s, confidence=%.2f",
            config.getMinTextRegionWidth(), config.getMinTextRegionHeight(),
            config.isEnableColumnDetection(), config.isEnableRegionMerging(),
            config.getConfidenceThreshold());
    }

    /**
     * 获取区域统计信息
     */
    public Map<String, Object> getRegionStatistics(List<DocumentRegion> regions) {
        Map<String, Object> stats = new HashMap<>();

        if (regions == null || regions.isEmpty()) {
            stats.put("totalRegions", 0);
            return stats;
        }

        int totalRegions = regions.size();
        Map<RegionType, Integer> typeCounts = new HashMap<>();
        double avgConfidence = 0.0;
        int totalArea = 0;

        for (DocumentRegion region : regions) {
            RegionType type = region.getType();
            typeCounts.put(type, typeCounts.getOrDefault(type, 0) + 1);
            avgConfidence += region.getConfidence();
            totalArea += region.getBounds().width * region.getBounds().height;
        }

        stats.put("totalRegions", totalRegions);
        stats.put("typeCounts", typeCounts);
        stats.put("avgConfidence", avgConfidence / totalRegions);
        stats.put("totalArea", totalArea);
        stats.put("avgAreaPerRegion", totalArea / totalRegions);

        return stats;
    }

    /**
     * 验证布局分析结果
     */
    public boolean validateAnalysisResult(LayoutAnalysisResult result) {
        if (result == null || !result.isSuccess()) {
            return false;
        }

        List<DocumentRegion> regions = result.getRegions();
        if (regions == null) {
            return false;
        }

        // 检查区域重叠
        for (int i = 0; i < regions.size() - 1; i++) {
            for (int j = i + 1; j < regions.size(); j++) {
                Rectangle bounds1 = regions.get(i).getBounds();
                Rectangle bounds2 = regions.get(j).getBounds();

                // 如果重叠面积超过较小区域的50%，认为不合理
                Rectangle intersection = bounds1.intersection(bounds2);
                if (!intersection.isEmpty()) {
                    int intersectionArea = intersection.width * intersection.height;
                    int minArea = Math.min(bounds1.width * bounds1.height, bounds2.width * bounds2.height);

                    if (intersectionArea > minArea * 0.5) {
                        return false;
                    }
                }
            }
        }

        return true;
    }
}
