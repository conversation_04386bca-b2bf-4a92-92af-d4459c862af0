package com.talkweb.ai.converter.util;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginMetadata;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Enumeration;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.jar.Manifest;

/**
 * 插件工具类
 */
public final class PluginUtils {
    
    private static final String PLUGIN_METADATA_FILE = "META-INF/plugin.xml";
    
    private PluginUtils() {
        // 工具类，防止实例化
    }
    
    /**
     * 从类路径加载插件元数据
     * @param pluginClass 插件类
     * @return 插件元数据
     * @throws PluginException 如果加载失败
     */
    public static PluginMetadata loadPluginMetadata(Class<?> pluginClass) throws PluginException {
        try (InputStream is = pluginClass.getClassLoader().getResourceAsStream(PLUGIN_METADATA_FILE)) {
            if (is == null) {
                throw new PluginException("Plugin metadata file not found: " + PLUGIN_METADATA_FILE);
            }
            // 这里简化处理，实际应该解析XML文件
            return new PluginMetadata(
                pluginClass.getName(),
                pluginClass.getSimpleName(),
                "1.0.0",
                "",
                "",
                pluginClass.getName()
            );
        } catch (IOException e) {
            throw new PluginException("Failed to load plugin metadata", e);
        }
    }
    
    /**
     * 从JAR文件加载插件元数据
     * @param jarFile JAR文件
     * @return 插件元数据
     * @throws PluginException 如果加载失败
     */
    public static PluginMetadata loadPluginMetadata(File jarFile) throws PluginException {
        try (JarFile jar = new JarFile(jarFile)) {
            // 检查清单文件
            Manifest manifest = jar.getManifest();
            if (manifest == null) {
                throw new PluginException("JAR file has no manifest: " + jarFile);
            }
            
            // 检查插件元数据文件
            JarEntry entry = jar.getJarEntry(PLUGIN_METADATA_FILE);
            if (entry == null) {
                throw new PluginException("Plugin metadata file not found in JAR: " + PLUGIN_METADATA_FILE);
            }
            
            // 这里简化处理，实际应该解析XML文件
            return new PluginMetadata(
                jarFile.getName(),
                jarFile.getName().replaceAll("\\.jar$", ""),
                "1.0.0",
                "",
                "",
                ""
            );
            
        } catch (IOException e) {
            throw new PluginException("Failed to load plugin metadata from JAR: " + jarFile, e);
        }
    }
    
    /**
     * 提取JAR文件到目标目录
     * @param jarFile JAR文件
     * @param targetDir 目标目录
     * @throws IOException 如果提取失败
     */
    public static void extractJar(File jarFile, Path targetDir) throws IOException {
        try (JarFile jar = new JarFile(jarFile)) {
            Enumeration<JarEntry> entries = jar.entries();
            
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                Path entryPath = targetDir.resolve(entry.getName());
                
                if (entry.isDirectory()) {
                    Files.createDirectories(entryPath);
                } else {
                    Files.createDirectories(entryPath.getParent());
                    try (InputStream is = jar.getInputStream(entry)) {
                        Files.copy(is, entryPath, StandardCopyOption.REPLACE_EXISTING);
                    }
                }
            }
        }
    }
    
    /**
     * 获取插件的类加载器
     * @param pluginClass 插件类
     * @param parent 父类加载器
     * @return 插件类加载器
     */
    public static ClassLoader createPluginClassLoader(Class<?> pluginClass, ClassLoader parent) {
        return new PluginClassLoader(new URL[0], parent) {
            @Override
            public Class<?> loadClass(String name) throws ClassNotFoundException {
                try {
                    // 优先从父加载器加载
                    return super.findSystemClass(name);
                } catch (ClassNotFoundException e) {
                    // 如果父加载器找不到，尝试从插件类加载
                    if (name.startsWith(pluginClass.getPackage().getName())) {
                        return findClass(name);
                    }
                    throw e;
                }
            }
        };
    }
    
    /**
     * 插件类加载器
     */
    private static class PluginClassLoader extends java.net.URLClassLoader {
        public PluginClassLoader(URL[] urls, ClassLoader parent) {
            super(urls, parent);
        }
        
        @Override
        protected Class<?> findClass(String name) throws ClassNotFoundException {
            try {
                return super.findClass(name);
            } catch (ClassNotFoundException e) {
                // 尝试从当前类路径加载
                try (InputStream is = getResourceAsStream(name.replace('.', '/') + ".class")) {
                    if (is != null) {
                        byte[] bytes = is.readAllBytes();
                        return defineClass(name, bytes, 0, bytes.length);
                    }
                } catch (IOException ex) {
                    throw new ClassNotFoundException("Failed to load class: " + name, ex);
                }
                throw e;
            }
        }
    }
}
