package com.talkweb.ai.converter.util.markdown.converter;

import com.talkweb.ai.converter.util.markdown.ConversionContext;
import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.talkweb.ai.converter.util.markdown.MarkdownConstants.*;

/**
 * Enhanced converter for HTML table elements with framework compatibility
 *
 * Supports:
 * - Bootstrap tables (table, table-striped, table-bordered, etc.)
 * - Tailwind CSS tables (table-auto, table-fixed, etc.)
 * - jQuery DataTables
 * - Material Design tables
 * - Foundation tables
 * - Semantic UI tables
 * - Custom styled tables
 *
 * <AUTHOR> Assistant
 * @version 2.0
 */
public class TableConverter extends AbstractElementConverter {

    private static final Set<String> SUPPORTED_TAGS = Set.of(
        TAG_TABLE, TAG_TR, TAG_TD, TAG_TH, TAG_THEAD, TAG_TBODY, TAG_TFOOT
    );

    // Performance optimization: Framework detection cache
    private static final Map<String, TableFramework> FRAMEWORK_CACHE = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 1000;

    // Performance optimization: Pre-compiled selectors
    private static final String STANDARD_ROW_SELECTOR = "tr";
    private static final String STANDARD_CELL_SELECTOR = "td, th";
    private static final String TABLE_SELECTOR = "table";

    // Performance optimization: Component processing cache
    private static final Map<String, String> COMPONENT_TEXT_CACHE = new ConcurrentHashMap<>();
    private static final int MAX_COMPONENT_CACHE_SIZE = 500;

    // Framework-specific CSS classes for table identification
    private static final Set<String> BOOTSTRAP_TABLE_CLASSES = Set.of(
        "table", "table-striped", "table-bordered", "table-hover", "table-condensed",
        "table-responsive", "table-dark", "table-light", "table-sm", "table-lg"
    );

    private static final Set<String> TAILWIND_TABLE_CLASSES = Set.of(
        "table-auto", "table-fixed", "border-collapse", "border-separate",
        "table-caption", "table-cell", "table-column", "table-column-group",
        "table-footer-group", "table-header-group", "table-row", "table-row-group"
    );

    private static final Set<String> DATATABLE_CLASSES = Set.of(
        "dataTable", "dataTables_wrapper", "dataTables_length", "dataTables_filter",
        "dataTables_info", "dataTables_paginate", "display", "cell-border", "stripe"
    );

    private static final Set<String> MATERIAL_TABLE_CLASSES = Set.of(
        "mdl-data-table", "mdc-data-table", "mat-table", "material-table"
    );

    private static final Set<String> FOUNDATION_TABLE_CLASSES = Set.of(
        "table", "stack", "scroll", "hover", "unstriped"
    );

    private static final Set<String> SEMANTIC_TABLE_CLASSES = Set.of(
        "ui", "table", "celled", "striped", "selectable", "inverted", "definition"
    );

    private static final Set<String> ANT_DESIGN_TABLE_CLASSES = Set.of(
        "ant-table", "ant-table-wrapper", "ant-table-container", "ant-table-content",
        "ant-table-thead", "ant-table-tbody", "ant-table-tfoot", "ant-table-cell",
        "ant-table-row", "ant-table-header", "ant-table-body", "ant-table-footer",
        "ant-table-bordered", "ant-table-striped", "ant-table-hover", "ant-table-small",
        "ant-table-middle", "ant-table-large", "ant-table-fixed-header", "ant-table-fixed-column",
        "ant-table-scroll-horizontal", "ant-table-scroll-vertical", "ant-table-ping-left",
        "ant-table-ping-right", "ant-table-has-fix-left", "ant-table-has-fix-right",
        "ant-table-selection-column", "ant-table-expand-icon-col", "ant-table-row-expand-icon-cell"
    );

    private static final Set<String> ELEMENT_UI_TABLE_CLASSES = Set.of(
        "el-table", "el-table__header-wrapper", "el-table__body-wrapper", "el-table__footer-wrapper",
        "el-table__header", "el-table__body", "el-table__footer", "el-table__row",
        "el-table__cell", "el-table-column", "el-table-column--selection", "el-table-column--expand",
        "el-table--border", "el-table--striped", "el-table--enable-row-hover", "el-table--enable-row-transition",
        "el-table--medium", "el-table--small", "el-table--mini", "el-table--scrollable-x", "el-table--scrollable-y",
        "el-table--fit", "el-table--hidden", "el-table--group", "el-table--has-footer",
        "el-table-fixed", "el-table-fixed-right", "el-table-fixed-left", "el-table__fixed",
        "el-table__fixed-right", "el-table__fixed-left", "el-table__fixed-header-wrapper",
        "el-table__fixed-body-wrapper", "el-table__fixed-footer-wrapper", "el-table__append-wrapper",
        "el-table__empty-block", "el-table__empty-text", "el-table__expand-column",
        "el-table__expand-icon", "el-table__indent", "el-table__placeholder"
    );

    private static final Set<String> VUETIFY_TABLE_CLASSES = Set.of(
        "v-table", "v-data-table", "v-data-table__wrapper", "v-data-table__table",
        "v-data-table-header", "v-data-table-rows", "v-data-table-footer",
        "v-data-table__thead", "v-data-table__tbody", "v-data-table__tfoot",
        "v-data-table-row", "v-data-table-column", "v-data-table-header__content",
        "v-data-table-rows__content", "v-data-table-footer__content",
        "v-table--density-default", "v-table--density-comfortable", "v-table--density-compact",
        "v-table--fixed-header", "v-table--fixed-footer", "v-table--has-top", "v-table--has-bottom",
        "v-data-table--loading", "v-data-table--show-select", "v-data-table--show-expand",
        "v-data-table-group-header-row", "v-data-table-virtual", "v-data-table-server",
        "v-theme-light", "v-theme-dark", "v-locale--is-ltr", "v-locale--is-rtl"
    );

    // Common table wrapper classes that should be ignored
    private static final Set<String> WRAPPER_CLASSES = Set.of(
        "table-responsive", "table-wrapper", "dataTables_wrapper", "table-container",
        "overflow-x-auto", "overflow-auto", "scroll-container", "ant-table-wrapper",
        "ant-table-container", "ant-spin-nested-loading", "ant-spin-container",
        "el-table__header-wrapper", "el-table__body-wrapper", "el-table__footer-wrapper",
        "v-data-table__wrapper", "v-data-table-header", "v-data-table-rows", "v-data-table-footer"
    );

    /**
     * Enum representing different table frameworks
     */
    private enum TableFramework {
        BOOTSTRAP,
        TAILWIND,
        DATATABLE,
        MATERIAL,
        FOUNDATION,
        SEMANTIC,
        ANT_DESIGN,
        ELEMENT_UI,
        VUETIFY,
        STANDARD
    }
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context)
            throws ConversionException {
        validate(element, context);

        String tagName = element.tagName().toLowerCase();

        switch (tagName) {
            case TAG_TABLE:
                convertTable(element, builder, context);
                break;
            case TAG_THEAD:
            case TAG_TBODY:
            case TAG_TFOOT:
                // These are handled by the table converter
                processChildren(element, builder, context);
                break;
            case TAG_TR:
            case TAG_TD:
            case TAG_TH:
                // These are handled by the table converter
                break;
            default:
                throw new ConversionException("Unsupported table tag: " + tagName);
        }
    }

    /**
     * Batch processing method for multiple tables with shared optimizations
     *
     * @param tables list of table elements to process
     * @param builder the markdown builder
     * @param context the conversion context
     */
    public void convertTablesBatch(List<Element> tables, MarkdownBuilder builder, ConversionContext context) {
        if (tables == null || tables.isEmpty()) {
            return;
        }

        // Pre-warm caches and shared resources
        Map<String, TableFramework> batchFrameworkCache = new HashMap<>();

        for (Element table : tables) {
            try {
                convertTableWithBatchOptimization(table, builder, context, batchFrameworkCache);
            } catch (Exception e) {
                // Log error but continue with other tables
                System.err.println("Error processing table in batch: " + e.getMessage());
            }
        }
    }

    /**
     * Converts a table with batch optimization
     */
    private void convertTableWithBatchOptimization(Element table, MarkdownBuilder builder,
                                                 ConversionContext context, Map<String, TableFramework> batchCache) {
        if (!builder.isEmpty()) {
            builder.newline();
        }

        // Use batch cache for framework detection
        String cacheKey = generateFrameworkCacheKey(table);
        TableFramework framework = batchCache.computeIfAbsent(cacheKey, k -> detectTableFrameworkInternal(table));

        // Find the actual table element if wrapped
        Element actualTable = findActualTableElement(table);
        if (actualTable == null) {
            return;
        }

        // Extract rows with framework-aware logic
        Elements rows = extractTableRows(actualTable, framework);
        if (rows.isEmpty()) {
            return;
        }

        // Extract table data with enhanced cell processing
        List<List<String>> tableData = extractEnhancedTableData(rows, context, framework);
        if (tableData.isEmpty()) {
            return;
        }

        int maxCols = tableData.stream().mapToInt(List::size).max().orElse(0);
        if (maxCols == 0) {
            return;
        }

        // Normalize table data (ensure all rows have same number of columns)
        normalizeTableData(tableData, maxCols);

        // Determine header rows based on framework
        int headerRowCount = determineHeaderRowCount(actualTable, framework);

        // Write table with proper header handling
        writeEnhancedTable(tableData, headerRowCount, maxCols, builder);

        builder.newline();
    }

    /**
     * Clears all performance caches to free memory
     */
    public static void clearCaches() {
        FRAMEWORK_CACHE.clear();
        COMPONENT_TEXT_CACHE.clear();
    }

    /**
     * Gets cache statistics for monitoring
     *
     * @return cache statistics
     */
    public static Map<String, Integer> getCacheStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("frameworkCacheSize", FRAMEWORK_CACHE.size());
        stats.put("componentCacheSize", COMPONENT_TEXT_CACHE.size());
        stats.put("frameworkCacheLimit", MAX_CACHE_SIZE);
        stats.put("componentCacheLimit", MAX_COMPONENT_CACHE_SIZE);
        return stats;
    }

    @Override
    public boolean shouldProcessChildren(Element element, ConversionContext context) {
        String tagName = element.tagName().toLowerCase();
        // Only table and section elements should process children normally
        return TAG_TABLE.equals(tagName) || TAG_THEAD.equals(tagName) || 
               TAG_TBODY.equals(tagName) || TAG_TFOOT.equals(tagName);
    }
    
    @Override
    public int getPriority() {
        return 60; // Medium priority for tables
    }
    
    @Override
    public void beforeConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (TAG_TABLE.equals(element.tagName().toLowerCase())) {
            context.setInTable(true);
        }
    }
    
    @Override
    public void afterConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (TAG_TABLE.equals(element.tagName().toLowerCase())) {
            context.setInTable(false);
        }
    }
    
    /**
     * Converts a complete table to Markdown with enhanced framework compatibility
     *
     * @param table the table element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertTable(Element table, MarkdownBuilder builder, ConversionContext context) {
        if (!builder.isEmpty()) {
            builder.newline();
        }

        // Detect table framework and apply appropriate processing
        TableFramework framework = detectTableFramework(table);

        // Find the actual table element if wrapped
        Element actualTable = findActualTableElement(table);
        if (actualTable == null) {
            return;
        }

        // Extract rows with framework-aware logic
        Elements rows = extractTableRows(actualTable, framework);
        if (rows.isEmpty()) {
            return;
        }

        // Extract table data with enhanced cell processing
        List<List<String>> tableData = extractEnhancedTableData(rows, context, framework);
        if (tableData.isEmpty()) {
            return;
        }

        int maxCols = tableData.stream().mapToInt(List::size).max().orElse(0);
        if (maxCols == 0) {
            return;
        }

        // Normalize table data (ensure all rows have same number of columns)
        normalizeTableData(tableData, maxCols);

        // Determine header rows based on framework
        int headerRowCount = determineHeaderRowCount(actualTable, framework);

        // Write table with proper header handling
        writeEnhancedTable(tableData, headerRowCount, maxCols, builder);

        builder.newline();
    }
    

    
    /**
     * Normalizes table data to ensure all rows have the same number of columns
     * 
     * @param tableData the table data
     * @param maxCols the maximum number of columns
     */
    private void normalizeTableData(List<List<String>> tableData, int maxCols) {
        for (List<String> row : tableData) {
            while (row.size() < maxCols) {
                row.add(EMPTY_STRING);
            }
        }
    }
    
    /**
     * Writes a table header row
     * 
     * @param headerRow the header row data
     * @param builder the markdown builder
     */
    private void writeTableHeader(List<String> headerRow, MarkdownBuilder builder) {
        builder.append(TABLE_SEPARATOR);
        for (String cell : headerRow) {
            builder.append(SPACE).append(cell).append(SPACE).append(TABLE_SEPARATOR);
        }
        builder.newline();
    }
    
    /**
     * Writes the table separator row
     * 
     * @param numCols the number of columns
     * @param builder the markdown builder
     */
    private void writeTableSeparator(int numCols, MarkdownBuilder builder) {
        builder.append(TABLE_SEPARATOR);
        for (int i = 0; i < numCols; i++) {
            builder.append(TABLE_HEADER_SEPARATOR);
        }
        builder.newline();
    }
    
    /**
     * Writes a table data row
     *
     * @param row the row data
     * @param builder the markdown builder
     */
    private void writeTableRow(List<String> row, MarkdownBuilder builder) {
        builder.append(TABLE_SEPARATOR);
        for (String cell : row) {
            builder.append(SPACE).append(cell).append(SPACE).append(TABLE_SEPARATOR);
        }
        builder.newline();
    }

    // ========== Enhanced Framework-Aware Methods ==========

    /**
     * Detects the table framework based on CSS classes and structure with caching
     *
     * @param element the table or wrapper element
     * @return the detected framework
     */
    private TableFramework detectTableFramework(Element element) {
        // Generate cache key from element and ancestor classes
        String cacheKey = generateFrameworkCacheKey(element);

        // Check cache first
        TableFramework cachedFramework = FRAMEWORK_CACHE.get(cacheKey);
        if (cachedFramework != null) {
            return cachedFramework;
        }

        // Perform detection
        TableFramework framework = detectTableFrameworkInternal(element);

        // Cache the result (with size limit)
        if (FRAMEWORK_CACHE.size() < MAX_CACHE_SIZE) {
            FRAMEWORK_CACHE.put(cacheKey, framework);
        }

        return framework;
    }

    /**
     * Generates a cache key for framework detection based on element hierarchy
     *
     * @param element the table or wrapper element
     * @return the cache key
     */
    private String generateFrameworkCacheKey(Element element) {
        StringBuilder keyBuilder = new StringBuilder();
        Element current = element;
        int depth = 0;

        // Include classes from element and up to 3 ancestors
        while (current != null && depth < 4) {
            String className = current.className().toLowerCase().trim();
            if (!className.isEmpty()) {
                if (keyBuilder.length() > 0) {
                    keyBuilder.append("|");
                }
                keyBuilder.append(className);
            }
            current = current.parent();
            depth++;
        }

        return keyBuilder.toString();
    }

    /**
     * Internal framework detection logic
     *
     * @param element the table or wrapper element
     * @return the detected framework
     */
    private TableFramework detectTableFrameworkInternal(Element element) {
        // Check element and its ancestors for framework classes
        Element current = element;
        while (current != null) {
            String className = current.className().toLowerCase();
            Set<String> classes = Set.of(className.split("\\s+"));

            // Check for Bootstrap classes
            if (classes.stream().anyMatch(BOOTSTRAP_TABLE_CLASSES::contains)) {
                return TableFramework.BOOTSTRAP;
            }

            // Check for Tailwind classes
            if (classes.stream().anyMatch(TAILWIND_TABLE_CLASSES::contains)) {
                return TableFramework.TAILWIND;
            }

            // Check for DataTable classes
            if (classes.stream().anyMatch(DATATABLE_CLASSES::contains)) {
                return TableFramework.DATATABLE;
            }

            // Check for Material Design classes
            if (classes.stream().anyMatch(MATERIAL_TABLE_CLASSES::contains)) {
                return TableFramework.MATERIAL;
            }

            // Check for Foundation classes
            if (classes.stream().anyMatch(FOUNDATION_TABLE_CLASSES::contains)) {
                return TableFramework.FOUNDATION;
            }

            // Check for Semantic UI classes
            if (classes.stream().anyMatch(SEMANTIC_TABLE_CLASSES::contains)) {
                return TableFramework.SEMANTIC;
            }

            // Check for Ant Design classes
            if (classes.stream().anyMatch(ANT_DESIGN_TABLE_CLASSES::contains)) {
                return TableFramework.ANT_DESIGN;
            }

            // Check for Element UI/Element Plus classes
            if (classes.stream().anyMatch(ELEMENT_UI_TABLE_CLASSES::contains)) {
                return TableFramework.ELEMENT_UI;
            }

            // Check for Vuetify classes
            if (classes.stream().anyMatch(VUETIFY_TABLE_CLASSES::contains)) {
                return TableFramework.VUETIFY;
            }

            current = current.parent();
        }

        return TableFramework.STANDARD;
    }

    /**
     * Finds the actual table element, handling framework wrappers with optimized selectors
     *
     * @param element the starting element
     * @return the actual table element or null if not found
     */
    private Element findActualTableElement(Element element) {
        // If it's already a table, return it
        if (TAG_TABLE.equals(element.tagName().toLowerCase())) {
            return element;
        }

        // Look for table in children (handle wrappers) using optimized selector
        Elements tables = element.select(TABLE_SELECTOR);
        if (!tables.isEmpty()) {
            return tables.first();
        }

        return null;
    }

    /**
     * Extracts table rows with framework-aware logic and optimized selectors
     *
     * @param table the table element
     * @param framework the detected framework
     * @return the table rows
     */
    private Elements extractTableRows(Element table, TableFramework framework) {
        switch (framework) {
            case DATATABLE:
                // DataTables might have additional wrapper rows
                return table.select("tbody tr, thead tr, tfoot tr");
            case MATERIAL:
                // Material tables might use different selectors
                Elements materialRows = table.select(STANDARD_ROW_SELECTOR);
                if (materialRows.isEmpty()) {
                    materialRows = table.select(".mdc-data-table__row, .mat-row");
                }
                return materialRows;
            case ANT_DESIGN:
                // Ant Design tables might have specific row classes
                Elements antRows = table.select(STANDARD_ROW_SELECTOR);
                if (antRows.isEmpty()) {
                    antRows = table.select(".ant-table-row, .ant-table-header-row");
                }
                return antRows;
            case ELEMENT_UI:
                // Element UI tables might have specific row classes
                Elements elementRows = table.select(STANDARD_ROW_SELECTOR);
                if (elementRows.isEmpty()) {
                    elementRows = table.select(".el-table__row");
                }
                return elementRows;
            case VUETIFY:
                // Vuetify tables might have specific row classes
                Elements vuetifyRows = table.select(STANDARD_ROW_SELECTOR);
                if (vuetifyRows.isEmpty()) {
                    vuetifyRows = table.select(".v-data-table-row");
                }
                return vuetifyRows;
            default:
                return table.select(STANDARD_ROW_SELECTOR);
        }
    }

    /**
     * Extracts table data with enhanced cell processing for different frameworks
     *
     * @param rows the table rows
     * @param context the conversion context
     * @param framework the detected framework
     * @return list of table data rows
     */
    private List<List<String>> extractEnhancedTableData(Elements rows, ConversionContext context,
                                                        TableFramework framework) {
        List<List<String>> tableData = new ArrayList<>();

        for (Element row : rows) {
            List<String> rowData = extractRowData(row, context, framework);
            if (!rowData.isEmpty()) {
                tableData.add(rowData);
            }
        }

        return tableData;
    }

    /**
     * Extracts data from a single row with framework-specific logic
     *
     * @param row the table row
     * @param context the conversion context
     * @param framework the detected framework
     * @return the row data
     */
    private List<String> extractRowData(Element row, ConversionContext context, TableFramework framework) {
        List<String> rowData = new ArrayList<>();

        // Get cells based on framework
        Elements cells = getCellsForFramework(row, framework);

        for (Element cell : cells) {
            String cellContent = extractEnhancedCellContent(cell, context, framework);
            rowData.add(cellContent);
        }

        return rowData;
    }

    /**
     * Gets cells from a row based on the framework with optimized selectors
     *
     * @param row the table row
     * @param framework the detected framework
     * @return the cells
     */
    private Elements getCellsForFramework(Element row, TableFramework framework) {
        switch (framework) {
            case MATERIAL:
                // Material Design might use different cell selectors
                Elements materialCells = row.select(STANDARD_CELL_SELECTOR);
                if (materialCells.isEmpty()) {
                    materialCells = row.select(".mdc-data-table__cell, .mat-cell");
                }
                return materialCells;
            case DATATABLE:
                // DataTables might have additional cell classes
                return row.select(STANDARD_CELL_SELECTOR);
            case ANT_DESIGN:
                // Ant Design might use specific cell classes
                Elements antCells = row.select(STANDARD_CELL_SELECTOR);
                if (antCells.isEmpty()) {
                    antCells = row.select(".ant-table-cell");
                }
                return antCells;
            case ELEMENT_UI:
                // Element UI might use specific cell classes
                Elements elementCells = row.select(STANDARD_CELL_SELECTOR);
                if (elementCells.isEmpty()) {
                    elementCells = row.select(".el-table__cell");
                }
                return elementCells;
            case VUETIFY:
                // Vuetify might use specific cell classes
                Elements vuetifyCells = row.select(STANDARD_CELL_SELECTOR);
                if (vuetifyCells.isEmpty()) {
                    vuetifyCells = row.select(".v-data-table-column");
                }
                return vuetifyCells;
            default:
                return row.select(STANDARD_CELL_SELECTOR);
        }
    }

    /**
     * Extracts content from a table cell with framework-specific enhancements and memory optimization
     *
     * @param cell the table cell
     * @param context the conversion context
     * @param framework the detected framework
     * @return the cell content
     */
    private String extractEnhancedCellContent(Element cell, ConversionContext context, TableFramework framework) {
        String content;

        // Handle framework-specific cell content extraction with memory optimization
        switch (framework) {
            case DATATABLE:
                // DataTables might have sorting controls or other elements to ignore
                content = extractDataTableCellContentOptimized(cell);
                break;
            case MATERIAL:
                // Material Design cells might have specific structure
                content = extractMaterialCellContent(cell);
                break;
            case BOOTSTRAP:
                // Bootstrap tables might have additional styling elements
                content = extractBootstrapCellContentOptimized(cell);
                break;
            case ANT_DESIGN:
                // Ant Design tables might have specific components
                content = extractAntDesignCellContentOptimized(cell);
                break;
            case ELEMENT_UI:
                // Element UI tables might have specific components
                content = extractElementUICellContentOptimized(cell);
                break;
            case VUETIFY:
                // Vuetify tables might have specific components
                content = extractVuetifyCellContentOptimized(cell);
                break;
            default:
                content = extractTextContent(cell);
                break;
        }

        // Common post-processing
        content = postProcessCellContent(content);

        return content;
    }

    /**
     * Memory-optimized component text extraction with caching
     *
     * @param element the component element
     * @param componentType the type of component for cache key
     * @return the extracted text
     */
    private String extractComponentTextOptimized(Element element, String componentType) {
        String elementKey = componentType + ":" + element.className() + ":" + element.text();

        // Check cache first
        String cachedText = COMPONENT_TEXT_CACHE.get(elementKey);
        if (cachedText != null) {
            return cachedText;
        }

        String text = element.text();

        // Cache the result (with size limit)
        if (COMPONENT_TEXT_CACHE.size() < MAX_COMPONENT_CACHE_SIZE) {
            COMPONENT_TEXT_CACHE.put(elementKey, text);
        }

        return text;
    }

    /**
     * Memory-optimized generic component processing
     *
     * @param cell the table cell
     * @param removeSelectors CSS selectors for elements to remove
     * @param componentSelectors map of component selectors to their processing logic
     * @return the processed cell content
     */
    private String processComponentsOptimized(Element cell, String[] removeSelectors,
                                            Map<String, ComponentProcessor> componentSelectors) {
        // Use StringBuilder for efficient string building
        StringBuilder result = new StringBuilder();

        // Process text nodes and components without cloning
        processElementRecursively(cell, result, removeSelectors, componentSelectors);

        return result.toString().trim();
    }

    /**
     * Recursively processes element content without cloning
     */
    private void processElementRecursively(Element element, StringBuilder result,
                                         String[] removeSelectors, Map<String, ComponentProcessor> componentSelectors) {
        // Skip elements that should be removed
        for (String selector : removeSelectors) {
            if (element.is(selector)) {
                return;
            }
        }

        // Check if this element matches any component selectors
        for (Map.Entry<String, ComponentProcessor> entry : componentSelectors.entrySet()) {
            if (element.is(entry.getKey())) {
                String processedText = entry.getValue().process(element);
                if (!processedText.isEmpty()) {
                    result.append(processedText).append(" ");
                }
                return;
            }
        }

        // If it's a text node, add the text
        if (element.children().isEmpty()) {
            String text = element.text();
            if (!text.isEmpty()) {
                result.append(text).append(" ");
            }
        } else {
            // Process children
            for (Element child : element.children()) {
                processElementRecursively(child, result, removeSelectors, componentSelectors);
            }
        }
    }

    /**
     * Interface for component processing logic
     */
    private interface ComponentProcessor {
        String process(Element element);
    }

    /**
     * Memory-optimized DataTable cell content extraction
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractDataTableCellContentOptimized(Element cell) {
        // For DataTables, we should only remove sizing elements, not sorting classes
        // The sorting classes contain the actual header text
        String[] removeSelectors = {".dataTables_sizing"};
        Map<String, ComponentProcessor> componentSelectors = new HashMap<>();

        return processComponentsOptimized(cell, removeSelectors, componentSelectors);
    }

    /**
     * Memory-optimized Bootstrap cell content extraction
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractBootstrapCellContentOptimized(Element cell) {
        String[] removeSelectors = {};
        Map<String, ComponentProcessor> componentSelectors = new HashMap<>();

        // Handle badges
        componentSelectors.put(".badge", element -> extractComponentTextOptimized(element, "badge"));

        return processComponentsOptimized(cell, removeSelectors, componentSelectors);
    }

    /**
     * Memory-optimized Ant Design cell content extraction
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractAntDesignCellContentOptimized(Element cell) {
        String[] removeSelectors = {
            ".ant-table-selection-column", ".ant-table-expand-icon-col",
            ".ant-table-row-expand-icon", ".ant-table-row-indent",
            ".ant-badge-status-dot"
        };

        Map<String, ComponentProcessor> componentSelectors = new HashMap<>();

        // Handle various Ant Design components
        componentSelectors.put(".ant-tag", element -> extractComponentTextOptimized(element, "tag"));
        componentSelectors.put(".ant-btn", element -> extractComponentTextOptimized(element, "button"));
        componentSelectors.put(".ant-badge", element -> {
            Element badgeCount = element.selectFirst(".ant-badge-count");
            return badgeCount != null ? badgeCount.text() : element.text();
        });
        componentSelectors.put(".ant-avatar", element -> element.attr("alt"));
        componentSelectors.put(".ant-tooltip", element -> {
            Element tooltipInner = element.selectFirst(".ant-tooltip-inner");
            return tooltipInner != null ? tooltipInner.text() : element.text();
        });
        componentSelectors.put(".ant-badge-status-text", element -> extractComponentTextOptimized(element, "status"));

        return processComponentsOptimized(cell, removeSelectors, componentSelectors);
    }

    /**
     * Memory-optimized Element UI cell content extraction
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractElementUICellContentOptimized(Element cell) {
        String[] removeSelectors = {
            ".el-table-column--selection", ".el-table-column--expand",
            ".el-table__expand-icon", ".el-table__indent", ".el-table__placeholder"
        };

        Map<String, ComponentProcessor> componentSelectors = new HashMap<>();

        // Handle various Element UI components
        componentSelectors.put(".el-tag", element -> extractComponentTextOptimized(element, "tag"));
        componentSelectors.put(".el-button", element -> extractComponentTextOptimized(element, "button"));
        componentSelectors.put(".el-badge", element -> {
            Element badgeContent = element.selectFirst(".el-badge__content");
            return badgeContent != null ? badgeContent.text() : element.text();
        });
        componentSelectors.put(".el-avatar", element -> element.attr("alt"));
        componentSelectors.put(".el-tooltip", element -> {
            Element tooltipContent = element.selectFirst(".el-tooltip__content");
            return tooltipContent != null ? tooltipContent.text() : element.text();
        });
        componentSelectors.put(".el-progress", element -> {
            Element progressText = element.selectFirst(".el-progress__text");
            return progressText != null ? progressText.text() : "";
        });
        componentSelectors.put(".el-switch", element -> element.hasClass("is-checked") ? "ON" : "OFF");

        return processComponentsOptimized(cell, removeSelectors, componentSelectors);
    }

    /**
     * Memory-optimized Vuetify cell content extraction
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractVuetifyCellContentOptimized(Element cell) {
        String[] removeSelectors = {
            ".v-data-table__select", ".v-data-table__expand-icon",
            ".v-data-table__mobile-row", ".v-data-table__mobile-header", ".v-icon"
        };

        Map<String, ComponentProcessor> componentSelectors = new HashMap<>();

        // Handle various Vuetify components
        componentSelectors.put(".v-chip", element -> extractComponentTextOptimized(element, "chip"));
        componentSelectors.put(".v-btn", element -> extractComponentTextOptimized(element, "button"));
        componentSelectors.put(".v-badge", element -> {
            Element badgeContent = element.selectFirst(".v-badge__badge");
            return badgeContent != null ? badgeContent.text() : element.text();
        });
        componentSelectors.put(".v-avatar", element -> element.attr("alt"));
        componentSelectors.put(".v-tooltip", element -> {
            Element tooltipContent = element.selectFirst(".v-tooltip__content");
            return tooltipContent != null ? tooltipContent.text() : element.text();
        });
        componentSelectors.put(".v-progress-linear", element -> {
            String value = element.attr("value");
            return !value.isEmpty() ? value + "%" : "";
        });
        componentSelectors.put(".v-progress-circular", element -> {
            String value = element.attr("value");
            return !value.isEmpty() ? value + "%" : "";
        });
        componentSelectors.put(".v-switch", element -> element.hasClass("v-input--is-label-active") ? "ON" : "OFF");

        return processComponentsOptimized(cell, removeSelectors, componentSelectors);
    }

    /**
     * Extracts content from DataTable cells, ignoring sorting controls
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractDataTableCellContent(Element cell) {
        // Clone the cell to avoid modifying the original
        Element cellCopy = cell.clone();

        // Remove DataTable-specific elements
        cellCopy.select(".sorting, .sorting_asc, .sorting_desc").remove();
        cellCopy.select(".dataTables_sizing").remove();

        return extractTextContent(cellCopy);
    }

    /**
     * Extracts content from Material Design cells
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractMaterialCellContent(Element cell) {
        // Material cells might have specific content structure
        Element contentElement = cell.selectFirst(".mdc-data-table__cell-content, .mat-cell-content");
        if (contentElement != null) {
            return extractTextContent(contentElement);
        }

        return extractTextContent(cell);
    }

    /**
     * Extracts content from Bootstrap table cells
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractBootstrapCellContent(Element cell) {
        // Bootstrap might have badges, buttons, or other components in cells
        Element cellCopy = cell.clone();

        // Preserve important content but clean up styling
        Elements badges = cellCopy.select(".badge");
        for (Element badge : badges) {
            badge.replaceWith(new org.jsoup.nodes.TextNode(badge.text()));
        }

        return extractTextContent(cellCopy);
    }

    /**
     * Extracts content from Ant Design table cells
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractAntDesignCellContent(Element cell) {
        // Clone the cell to avoid modifying the original
        Element cellCopy = cell.clone();

        // Remove Ant Design specific elements that shouldn't be in content
        cellCopy.select(".ant-table-selection-column").remove();
        cellCopy.select(".ant-table-expand-icon-col").remove();
        cellCopy.select(".ant-table-row-expand-icon").remove();
        cellCopy.select(".ant-table-row-indent").remove();

        // Handle Ant Design components with proper spacing
        Elements tags = cellCopy.select(".ant-tag");
        for (Element tag : tags) {
            String tagText = tag.text();
            if (!tagText.isEmpty()) {
                tag.replaceWith(new org.jsoup.nodes.TextNode(tagText + " "));
            } else {
                tag.remove();
            }
        }

        Elements buttons = cellCopy.select(".ant-btn");
        for (Element button : buttons) {
            String buttonText = button.text();
            if (!buttonText.isEmpty()) {
                button.replaceWith(new org.jsoup.nodes.TextNode(buttonText + " "));
            } else {
                button.remove();
            }
        }

        Elements badges = cellCopy.select(".ant-badge");
        for (Element badge : badges) {
            // Extract badge text, might be in .ant-badge-count or direct text
            Element badgeCount = badge.selectFirst(".ant-badge-count");
            String badgeText = badgeCount != null ? badgeCount.text() : badge.text();
            badge.replaceWith(new org.jsoup.nodes.TextNode(badgeText));
        }

        Elements avatars = cellCopy.select(".ant-avatar");
        for (Element avatar : avatars) {
            // For avatars, we might want to extract alt text or just remove them
            String altText = avatar.attr("alt");
            if (!altText.isEmpty()) {
                avatar.replaceWith(new org.jsoup.nodes.TextNode(altText));
            } else {
                avatar.remove();
            }
        }

        Elements tooltips = cellCopy.select(".ant-tooltip");
        for (Element tooltip : tooltips) {
            // Extract tooltip content
            Element tooltipInner = tooltip.selectFirst(".ant-tooltip-inner");
            if (tooltipInner != null) {
                tooltip.replaceWith(new org.jsoup.nodes.TextNode(tooltipInner.text()));
            } else {
                tooltip.replaceWith(new org.jsoup.nodes.TextNode(tooltip.text()));
            }
        }

        // Handle status indicators
        Elements statusDots = cellCopy.select(".ant-badge-status-dot");
        statusDots.remove(); // Remove visual status dots

        Elements statusText = cellCopy.select(".ant-badge-status-text");
        for (Element status : statusText) {
            status.replaceWith(new org.jsoup.nodes.TextNode(status.text()));
        }

        return extractTextContent(cellCopy);
    }

    /**
     * Extracts content from Element UI/Element Plus table cells
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractElementUICellContent(Element cell) {
        // Clone the cell to avoid modifying the original
        Element cellCopy = cell.clone();

        // Remove Element UI specific elements that shouldn't be in content
        cellCopy.select(".el-table-column--selection").remove();
        cellCopy.select(".el-table-column--expand").remove();
        cellCopy.select(".el-table__expand-icon").remove();
        cellCopy.select(".el-table__indent").remove();
        cellCopy.select(".el-table__placeholder").remove();

        // Handle Element UI components
        Elements tags = cellCopy.select(".el-tag");
        for (Element tag : tags) {
            String tagText = tag.text();
            if (!tagText.isEmpty()) {
                tag.replaceWith(new org.jsoup.nodes.TextNode(tagText + " "));
            } else {
                tag.remove();
            }
        }

        Elements buttons = cellCopy.select(".el-button");
        for (Element button : buttons) {
            String buttonText = button.text();
            if (!buttonText.isEmpty()) {
                button.replaceWith(new org.jsoup.nodes.TextNode(buttonText + " "));
            } else {
                button.remove();
            }
        }

        Elements badges = cellCopy.select(".el-badge");
        for (Element badge : badges) {
            // Extract badge text, might be in .el-badge__content or direct text
            Element badgeContent = badge.selectFirst(".el-badge__content");
            String badgeText = badgeContent != null ? badgeContent.text() : badge.text();
            if (!badgeText.isEmpty()) {
                badge.replaceWith(new org.jsoup.nodes.TextNode(badgeText + " "));
            } else {
                badge.remove();
            }
        }

        Elements avatars = cellCopy.select(".el-avatar");
        for (Element avatar : avatars) {
            // For avatars, we might want to extract alt text or just remove them
            String altText = avatar.attr("alt");
            if (!altText.isEmpty()) {
                avatar.replaceWith(new org.jsoup.nodes.TextNode(altText + " "));
            } else {
                avatar.remove();
            }
        }

        Elements tooltips = cellCopy.select(".el-tooltip");
        for (Element tooltip : tooltips) {
            // Extract tooltip content
            Element tooltipContent = tooltip.selectFirst(".el-tooltip__content");
            if (tooltipContent != null) {
                tooltip.replaceWith(new org.jsoup.nodes.TextNode(tooltipContent.text() + " "));
            } else {
                tooltip.replaceWith(new org.jsoup.nodes.TextNode(tooltip.text() + " "));
            }
        }

        // Handle progress bars
        Elements progress = cellCopy.select(".el-progress");
        for (Element prog : progress) {
            Element progressText = prog.selectFirst(".el-progress__text");
            if (progressText != null) {
                prog.replaceWith(new org.jsoup.nodes.TextNode(progressText.text() + " "));
            } else {
                prog.remove();
            }
        }

        // Handle switches
        Elements switches = cellCopy.select(".el-switch");
        for (Element switchEl : switches) {
            // For switches, we might want to extract the state or just remove them
            boolean isChecked = switchEl.hasClass("is-checked");
            switchEl.replaceWith(new org.jsoup.nodes.TextNode(isChecked ? "ON " : "OFF "));
        }

        return extractTextContent(cellCopy);
    }

    /**
     * Extracts content from Vuetify table cells
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractVuetifyCellContent(Element cell) {
        // Clone the cell to avoid modifying the original
        Element cellCopy = cell.clone();

        // Remove Vuetify specific elements that shouldn't be in content
        cellCopy.select(".v-data-table__select").remove();
        cellCopy.select(".v-data-table__expand-icon").remove();
        cellCopy.select(".v-data-table__mobile-row").remove();
        cellCopy.select(".v-data-table__mobile-header").remove();

        // Handle Vuetify components
        Elements chips = cellCopy.select(".v-chip");
        for (Element chip : chips) {
            String chipText = chip.text();
            if (!chipText.isEmpty()) {
                chip.replaceWith(new org.jsoup.nodes.TextNode(chipText + " "));
            } else {
                chip.remove();
            }
        }

        Elements buttons = cellCopy.select(".v-btn");
        for (Element button : buttons) {
            String buttonText = button.text();
            if (!buttonText.isEmpty()) {
                button.replaceWith(new org.jsoup.nodes.TextNode(buttonText + " "));
            } else {
                button.remove();
            }
        }

        Elements badges = cellCopy.select(".v-badge");
        for (Element badge : badges) {
            // Extract badge text, might be in .v-badge__badge or direct text
            Element badgeContent = badge.selectFirst(".v-badge__badge");
            String badgeText = badgeContent != null ? badgeContent.text() : badge.text();
            if (!badgeText.isEmpty()) {
                badge.replaceWith(new org.jsoup.nodes.TextNode(badgeText + " "));
            } else {
                badge.remove();
            }
        }

        Elements avatars = cellCopy.select(".v-avatar");
        for (Element avatar : avatars) {
            // For avatars, we might want to extract alt text or just remove them
            String altText = avatar.attr("alt");
            if (!altText.isEmpty()) {
                avatar.replaceWith(new org.jsoup.nodes.TextNode(altText + " "));
            } else {
                avatar.remove();
            }
        }

        Elements tooltips = cellCopy.select(".v-tooltip");
        for (Element tooltip : tooltips) {
            // Extract tooltip content
            Element tooltipContent = tooltip.selectFirst(".v-tooltip__content");
            if (tooltipContent != null) {
                tooltip.replaceWith(new org.jsoup.nodes.TextNode(tooltipContent.text() + " "));
            } else {
                tooltip.replaceWith(new org.jsoup.nodes.TextNode(tooltip.text() + " "));
            }
        }

        // Handle progress bars
        Elements progress = cellCopy.select(".v-progress-linear, .v-progress-circular");
        for (Element prog : progress) {
            // For progress bars, we might want to extract the value or just remove them
            String value = prog.attr("value");
            if (!value.isEmpty()) {
                prog.replaceWith(new org.jsoup.nodes.TextNode(value + "% "));
            } else {
                prog.remove();
            }
        }

        // Handle switches
        Elements switches = cellCopy.select(".v-switch");
        for (Element switchEl : switches) {
            // For switches, we might want to extract the state or just remove them
            boolean isChecked = switchEl.hasClass("v-input--is-label-active");
            switchEl.replaceWith(new org.jsoup.nodes.TextNode(isChecked ? "ON " : "OFF "));
        }

        // Handle icons (remove them as they're decorative)
        Elements icons = cellCopy.select(".v-icon");
        icons.remove();

        return extractTextContent(cellCopy);
    }

    /**
     * Post-processes cell content for markdown compatibility
     *
     * @param content the raw cell content
     * @return the processed content
     */
    private String postProcessCellContent(String content) {
        if (content == null) {
            return EMPTY_STRING;
        }

        // Escape pipe characters in cell content
        content = content.replace(TABLE_SEPARATOR, "\\" + TABLE_SEPARATOR);

        // Remove newlines from cell content
        content = content.replace(NEWLINE, SPACE);

        // Normalize whitespace
        content = content.replaceAll("\\s+", SPACE).trim();

        return content;
    }

    /**
     * Determines the number of header rows based on table structure and framework
     *
     * @param table the table element
     * @param framework the detected framework
     * @return the number of header rows
     */
    private int determineHeaderRowCount(Element table, TableFramework framework) {
        // Check for explicit thead section
        Elements theadRows = table.select("thead tr");
        if (!theadRows.isEmpty()) {
            return theadRows.size();
        }

        // Check for th elements in first rows
        Elements allRows = table.select("tr");
        if (allRows.isEmpty()) {
            return 0;
        }

        int headerRowCount = 0;
        for (Element row : allRows) {
            Elements thCells = row.select("th");
            if (!thCells.isEmpty()) {
                headerRowCount++;
            } else {
                break; // Stop at first row without th elements
            }
        }

        // Framework-specific adjustments
        switch (framework) {
            case DATATABLE:
                // DataTables typically have one header row
                return Math.max(1, headerRowCount);
            case MATERIAL:
                // Material tables usually have one header row
                return Math.max(1, headerRowCount);
            case ANT_DESIGN:
                // Ant Design tables typically have one header row
                return Math.max(1, headerRowCount);
            case ELEMENT_UI:
                // Element UI tables typically have one header row
                return Math.max(1, headerRowCount);
            case VUETIFY:
                // Vuetify tables typically have one header row
                return Math.max(1, headerRowCount);
            default:
                return Math.max(1, headerRowCount); // At least one header row
        }
    }

    /**
     * Writes an enhanced table with proper header handling
     *
     * @param tableData the table data
     * @param headerRowCount the number of header rows
     * @param maxCols the maximum number of columns
     * @param builder the markdown builder
     */
    private void writeEnhancedTable(List<List<String>> tableData, int headerRowCount,
                                   int maxCols, MarkdownBuilder builder) {
        if (tableData.isEmpty()) {
            return;
        }

        // Write header rows
        for (int i = 0; i < Math.min(headerRowCount, tableData.size()); i++) {
            writeTableHeader(tableData.get(i), builder);
            if (i == 0) {
                // Write separator after first header row
                writeTableSeparator(maxCols, builder);
            }
        }

        // If no header was written, write the first row as header
        if (headerRowCount == 0 && !tableData.isEmpty()) {
            writeTableHeader(tableData.get(0), builder);
            writeTableSeparator(maxCols, builder);
            headerRowCount = 1;
        }

        // Write data rows
        for (int i = headerRowCount; i < tableData.size(); i++) {
            writeTableRow(tableData.get(i), builder);
        }
    }
}
