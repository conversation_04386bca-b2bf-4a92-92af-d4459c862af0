package com.talkweb.ai.converter.util.word;

// WordConversionMode and WordConversionOptions are in the same package
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.hwpf.HWPFDocument;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Context object that maintains state during Word to Markdown conversion
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class WordConversionContext {
    
    private final WordConversionMode mode;
    private final WordConversionOptions options;
    private final Map<String, Object> properties;
    private final Map<String, Object> cache;
    private final Set<String> processedElements;
    private final List<String> footnotes;
    private final Map<String, String> imageMap;
    private final AtomicInteger imageCounter;
    private final File sourceFile;
    
    // Document state
    private Object document; // XWPFDocument or HWPFDocument
    private int currentDepth;
    private int listLevel;
    private boolean inTable;
    private boolean inList;
    private boolean inFootnote;
    private String currentListType; // "ordered" or "unordered"
    private int tableRowIndex;
    private int tableCellIndex;
    
    // Style tracking
    private final Map<String, String> styleMap;
    private final Set<String> headingStyles;
    
    /**
     * Creates a new Word conversion context
     * 
     * @param mode the conversion mode
     * @param options the conversion options
     * @param sourceFile the source Word file
     */
    public WordConversionContext(WordConversionMode mode, WordConversionOptions options, File sourceFile) {
        this.mode = mode != null ? mode : WordConversionMode.LOOSE;
        this.options = options != null ? options : new WordConversionOptions();
        this.sourceFile = sourceFile;
        this.properties = new ConcurrentHashMap<>();
        this.cache = new ConcurrentHashMap<>();
        this.processedElements = new HashSet<>();
        this.footnotes = new ArrayList<>();
        this.imageMap = new HashMap<>();
        this.imageCounter = new AtomicInteger(1);
        this.styleMap = new HashMap<>();
        this.headingStyles = new HashSet<>();
        
        // Initialize state
        this.currentDepth = 0;
        this.listLevel = 0;
        this.inTable = false;
        this.inList = false;
        this.inFootnote = false;
        this.currentListType = null;
        this.tableRowIndex = 0;
        this.tableCellIndex = 0;
        
        // Initialize common heading styles
        initializeHeadingStyles();
    }
    
    /**
     * Initialize common heading style names
     */
    private void initializeHeadingStyles() {
        headingStyles.add("heading 1");
        headingStyles.add("heading 2");
        headingStyles.add("heading 3");
        headingStyles.add("heading 4");
        headingStyles.add("heading 5");
        headingStyles.add("heading 6");
        headingStyles.add("title");
        headingStyles.add("subtitle");
        headingStyles.add("标题 1");
        headingStyles.add("标题 2");
        headingStyles.add("标题 3");
        headingStyles.add("标题 4");
        headingStyles.add("标题 5");
        headingStyles.add("标题 6");
    }
    
    // Getters and setters
    
    public WordConversionMode getMode() {
        return mode;
    }
    
    public WordConversionOptions getOptions() {
        return options;
    }
    
    public File getSourceFile() {
        return sourceFile;
    }
    
    public Object getDocument() {
        return document;
    }
    
    public void setDocument(Object document) {
        this.document = document;
    }
    
    public boolean isDocx() {
        return document instanceof XWPFDocument;
    }
    
    public boolean isDoc() {
        return document instanceof HWPFDocument;
    }
    
    public XWPFDocument getXWPFDocument() {
        return isDocx() ? (XWPFDocument) document : null;
    }
    
    public HWPFDocument getHWPFDocument() {
        return isDoc() ? (HWPFDocument) document : null;
    }
    
    public boolean isStrictMode() {
        return mode == WordConversionMode.STRICT;
    }
    
    // Property management
    
    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }
    
    public Object getProperty(String key) {
        return properties.get(key);
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key, T defaultValue) {
        Object value = properties.get(key);
        return value != null ? (T) value : defaultValue;
    }
    
    public boolean hasProperty(String key) {
        return properties.containsKey(key);
    }
    
    // Cache management
    
    public void cache(String key, Object value) {
        cache.put(key, value);
    }
    
    public Object getCached(String key) {
        return cache.get(key);
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getCached(String key, T defaultValue) {
        Object value = cache.get(key);
        return value != null ? (T) value : defaultValue;
    }
    
    // State management
    
    public int getCurrentDepth() {
        return currentDepth;
    }
    
    public void incrementDepth() {
        currentDepth++;
    }
    
    public void decrementDepth() {
        if (currentDepth > 0) {
            currentDepth--;
        }
    }
    
    public int getListLevel() {
        return listLevel;
    }
    
    public void enterList(String listType) {
        inList = true;
        listLevel++;
        currentListType = listType;
    }
    
    public void exitList() {
        if (listLevel > 0) {
            listLevel--;
        }
        if (listLevel == 0) {
            inList = false;
            currentListType = null;
        }
    }
    
    public boolean isInTable() {
        return inTable;
    }
    
    public void setInTable(boolean inTable) {
        this.inTable = inTable;
        if (!inTable) {
            tableRowIndex = 0;
            tableCellIndex = 0;
        }
    }
    
    public boolean isInList() {
        return inList;
    }
    
    public String getCurrentListType() {
        return currentListType;
    }
    
    public boolean isInFootnote() {
        return inFootnote;
    }
    
    public void setInFootnote(boolean inFootnote) {
        this.inFootnote = inFootnote;
    }
    
    public int getTableRowIndex() {
        return tableRowIndex;
    }
    
    public void incrementTableRowIndex() {
        tableRowIndex++;
        tableCellIndex = 0;
    }
    
    public int getTableCellIndex() {
        return tableCellIndex;
    }
    
    public void incrementTableCellIndex() {
        tableCellIndex++;
    }
    
    // Footnote management
    
    public void addFootnote(String footnote) {
        footnotes.add(footnote);
    }
    
    public List<String> getFootnotes() {
        return new ArrayList<>(footnotes);
    }
    
    public int getFootnoteCount() {
        return footnotes.size();
    }
    
    // Image management
    
    public String generateImageName(String extension) {
        return "image_" + imageCounter.getAndIncrement() + "." + extension;
    }
    
    public void mapImage(String originalId, String fileName) {
        imageMap.put(originalId, fileName);
    }
    
    public String getImageFileName(String originalId) {
        return imageMap.get(originalId);
    }
    
    // Style management
    
    public void mapStyle(String styleName, String markdownEquivalent) {
        styleMap.put(styleName.toLowerCase(), markdownEquivalent);
    }
    
    public String getStyleMapping(String styleName) {
        return styleMap.get(styleName.toLowerCase());
    }
    
    public boolean isHeadingStyle(String styleName) {
        return styleName != null && headingStyles.contains(styleName.toLowerCase());
    }
    
    public void addHeadingStyle(String styleName) {
        if (styleName != null) {
            headingStyles.add(styleName.toLowerCase());
        }
    }
    
    // Processing tracking
    
    public void markProcessed(String elementId) {
        processedElements.add(elementId);
    }
    
    public boolean isProcessed(String elementId) {
        return processedElements.contains(elementId);
    }
    
    /**
     * Creates a copy of this context for nested processing
     */
    public WordConversionContext copy() {
        WordConversionContext copy = new WordConversionContext(this.mode, this.options, this.sourceFile);
        copy.document = this.document;
        copy.properties.putAll(this.properties);
        copy.cache.putAll(this.cache);
        copy.currentDepth = this.currentDepth;
        copy.listLevel = this.listLevel;
        copy.inTable = this.inTable;
        copy.inList = this.inList;
        copy.inFootnote = this.inFootnote;
        copy.currentListType = this.currentListType;
        copy.tableRowIndex = this.tableRowIndex;
        copy.tableCellIndex = this.tableCellIndex;
        copy.styleMap.putAll(this.styleMap);
        copy.headingStyles.addAll(this.headingStyles);
        copy.footnotes.addAll(this.footnotes);
        copy.imageMap.putAll(this.imageMap);
        return copy;
    }
    
    /**
     * Clears the context state
     */
    public void clear() {
        properties.clear();
        cache.clear();
        processedElements.clear();
        footnotes.clear();
        imageMap.clear();
        styleMap.clear();
        currentDepth = 0;
        listLevel = 0;
        inTable = false;
        inList = false;
        inFootnote = false;
        currentListType = null;
        tableRowIndex = 0;
        tableCellIndex = 0;
        imageCounter.set(1);
    }
}
