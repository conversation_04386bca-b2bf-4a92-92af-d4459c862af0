package com.talkweb.ai.converter.util.image;

import com.talkweb.ai.converter.service.OcrService;
import com.talkweb.ai.converter.model.OcrResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.*;
import java.util.List;

/**
 * 表格提取器
 * 
 * 实现表格内容提取功能，包括单元格文本识别、
 * 表格数据结构化和表格内容验证。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class TableExtractor {
    
    private static final Logger logger = LoggerFactory.getLogger(TableExtractor.class);
    
    @Autowired
    private OcrService ocrService;
    
    /**
     * 表格提取配置
     */
    public static class TableExtractionConfig {
        private double minCellConfidence = 0.5;
        private boolean enableCellValidation = true;
        private boolean enableContentCleaning = true;
        private int maxCellTextLength = 1000;
        private boolean preserveWhitespace = false;
        private boolean enableSmartMerging = true;
        private double mergingThreshold = 0.8;
        
        // Getters and setters
        public double getMinCellConfidence() { return minCellConfidence; }
        public void setMinCellConfidence(double minCellConfidence) { this.minCellConfidence = minCellConfidence; }
        
        public boolean isEnableCellValidation() { return enableCellValidation; }
        public void setEnableCellValidation(boolean enableCellValidation) { this.enableCellValidation = enableCellValidation; }
        
        public boolean isEnableContentCleaning() { return enableContentCleaning; }
        public void setEnableContentCleaning(boolean enableContentCleaning) { this.enableContentCleaning = enableContentCleaning; }
        
        public int getMaxCellTextLength() { return maxCellTextLength; }
        public void setMaxCellTextLength(int maxCellTextLength) { this.maxCellTextLength = maxCellTextLength; }
        
        public boolean isPreserveWhitespace() { return preserveWhitespace; }
        public void setPreserveWhitespace(boolean preserveWhitespace) { this.preserveWhitespace = preserveWhitespace; }
        
        public boolean isEnableSmartMerging() { return enableSmartMerging; }
        public void setEnableSmartMerging(boolean enableSmartMerging) { this.enableSmartMerging = enableSmartMerging; }
        
        public double getMergingThreshold() { return mergingThreshold; }
        public void setMergingThreshold(double mergingThreshold) { this.mergingThreshold = mergingThreshold; }
    }
    
    /**
     * 表格提取结果
     */
    public static class TableExtractionResult {
        private final List<TableData> tables;
        private final boolean success;
        private final String errorMessage;
        private final Map<String, Object> metadata;
        
        public TableExtractionResult(List<TableData> tables) {
            this.tables = tables != null ? new ArrayList<>(tables) : new ArrayList<>();
            this.success = true;
            this.errorMessage = null;
            this.metadata = new HashMap<>();
        }
        
        public TableExtractionResult(String errorMessage) {
            this.tables = new ArrayList<>();
            this.success = false;
            this.errorMessage = errorMessage;
            this.metadata = new HashMap<>();
        }
        
        // Getters
        public List<TableData> getTables() { return new ArrayList<>(tables); }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
        
        public void addMetadata(String key, Object value) {
            this.metadata.put(key, value);
        }
    }
    
    /**
     * 表格数据
     */
    public static class TableData {
        private final List<List<CellData>> cells;
        private final int rows;
        private final int columns;
        private final Rectangle bounds;
        private final double confidence;
        
        public TableData(List<List<CellData>> cells, Rectangle bounds, double confidence) {
            this.cells = cells != null ? deepCopyCells(cells) : new ArrayList<>();
            this.bounds = new Rectangle(bounds);
            this.confidence = confidence;
            this.rows = this.cells.size();
            this.columns = this.cells.isEmpty() ? 0 : this.cells.get(0).size();
        }
        
        private List<List<CellData>> deepCopyCells(List<List<CellData>> original) {
            List<List<CellData>> copy = new ArrayList<>();
            for (List<CellData> row : original) {
                List<CellData> rowCopy = new ArrayList<>();
                for (CellData cell : row) {
                    rowCopy.add(new CellData(cell.getText(), cell.getBounds(), cell.getConfidence()));
                }
                copy.add(rowCopy);
            }
            return copy;
        }
        
        // Getters
        public List<List<CellData>> getCells() { return deepCopyCells(cells); }
        public int getRows() { return rows; }
        public int getColumns() { return columns; }
        public Rectangle getBounds() { return new Rectangle(bounds); }
        public double getConfidence() { return confidence; }
        
        /**
         * 获取指定位置的单元格
         */
        public CellData getCell(int row, int col) {
            if (row >= 0 && row < rows && col >= 0 && col < columns) {
                return cells.get(row).get(col);
            }
            return null;
        }
        
        /**
         * 转换为字符串数组
         */
        public String[][] toStringArray() {
            String[][] result = new String[rows][columns];
            for (int i = 0; i < rows; i++) {
                for (int j = 0; j < columns; j++) {
                    result[i][j] = cells.get(i).get(j).getText();
                }
            }
            return result;
        }
    }
    
    /**
     * 单元格数据
     */
    public static class CellData {
        private final String text;
        private final Rectangle bounds;
        private final double confidence;
        
        public CellData(String text, Rectangle bounds, double confidence) {
            this.text = text != null ? text : "";
            this.bounds = new Rectangle(bounds);
            this.confidence = confidence;
        }
        
        // Getters
        public String getText() { return text; }
        public Rectangle getBounds() { return new Rectangle(bounds); }
        public double getConfidence() { return confidence; }
        
        public boolean isEmpty() {
            return text.trim().isEmpty();
        }
    }
    
    /**
     * 提取表格内容
     * 
     * @param image 输入图像
     * @param tableRegions 表格区域列表
     * @return 表格提取结果
     */
    public TableExtractionResult extractTables(BufferedImage image, 
                                             List<TableDetector.TableRegion> tableRegions) {
        return extractTables(image, tableRegions, new TableExtractionConfig());
    }
    
    /**
     * 提取表格内容（带配置）
     * 
     * @param image 输入图像
     * @param tableRegions 表格区域列表
     * @param config 提取配置
     * @return 表格提取结果
     */
    public TableExtractionResult extractTables(BufferedImage image, 
                                             List<TableDetector.TableRegion> tableRegions,
                                             TableExtractionConfig config) {
        if (image == null || tableRegions == null) {
            return new TableExtractionResult("Input image or table regions is null");
        }
        
        if (config == null) {
            config = new TableExtractionConfig();
        }
        
        try {
            logger.debug("Starting table extraction for {} tables", tableRegions.size());
            
            List<TableData> extractedTables = new ArrayList<>();
            
            for (int i = 0; i < tableRegions.size(); i++) {
                TableDetector.TableRegion region = tableRegions.get(i);
                logger.debug("Extracting table {} of {}", i + 1, tableRegions.size());
                
                TableData tableData = extractSingleTable(image, region, config);
                if (tableData != null) {
                    extractedTables.add(tableData);
                }
            }
            
            TableExtractionResult result = new TableExtractionResult(extractedTables);
            result.addMetadata("totalTables", tableRegions.size());
            result.addMetadata("extractedTables", extractedTables.size());
            result.addMetadata("extractionRate", 
                extractedTables.size() / (double) Math.max(1, tableRegions.size()));
            
            logger.debug("Table extraction completed. Extracted {} of {} tables", 
                        extractedTables.size(), tableRegions.size());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Table extraction failed", e);
            return new TableExtractionResult("Table extraction failed: " + e.getMessage());
        }
    }
    
    /**
     * 提取单个表格
     */
    private TableData extractSingleTable(BufferedImage image, 
                                       TableDetector.TableRegion region,
                                       TableExtractionConfig config) {
        try {
            List<List<Rectangle>> cellBounds = region.getCells();
            List<List<CellData>> cellData = new ArrayList<>();
            
            double totalConfidence = 0.0;
            int cellCount = 0;
            
            // 提取每个单元格的内容
            for (List<Rectangle> row : cellBounds) {
                List<CellData> rowData = new ArrayList<>();
                
                for (Rectangle cellBound : row) {
                    CellData cell = extractCellContent(image, cellBound, config);
                    rowData.add(cell);
                    
                    totalConfidence += cell.getConfidence();
                    cellCount++;
                }
                
                cellData.add(rowData);
            }
            
            double avgConfidence = cellCount > 0 ? totalConfidence / cellCount : 0.0;
            
            // 应用智能合并（如果启用）
            if (config.isEnableSmartMerging()) {
                cellData = applySmartMerging(cellData, config);
            }
            
            return new TableData(cellData, region.getBounds(), avgConfidence);
            
        } catch (Exception e) {
            logger.error("Failed to extract single table", e);
            return null;
        }
    }
    
    /**
     * 提取单元格内容
     */
    private CellData extractCellContent(BufferedImage image, Rectangle cellBounds, 
                                      TableExtractionConfig config) {
        try {
            // 裁剪单元格图像
            BufferedImage cellImage = image.getSubimage(
                cellBounds.x, cellBounds.y, cellBounds.width, cellBounds.height);
            
            // 使用OCR识别文本
            OcrResult ocrResult = ocrService.recognizeText(cellImage);
            
            String text = ocrResult.getText();
            double confidence = ocrResult.getConfidence() / 100.0; // 转换为0-1范围
            
            // 内容清理
            if (config.isEnableContentCleaning()) {
                text = cleanCellContent(text, config);
            }
            
            // 内容验证
            if (config.isEnableCellValidation()) {
                if (!validateCellContent(text, confidence, config)) {
                    text = ""; // 无效内容设为空
                    confidence = 0.0;
                }
            }
            
            return new CellData(text, cellBounds, confidence);
            
        } catch (Exception e) {
            logger.warn("Failed to extract cell content at {}", cellBounds, e);
            return new CellData("", cellBounds, 0.0);
        }
    }

    /**
     * 清理单元格内容
     */
    private String cleanCellContent(String text, TableExtractionConfig config) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        String cleaned = text;

        // 移除多余的空白字符
        if (!config.isPreserveWhitespace()) {
            cleaned = cleaned.replaceAll("\\s+", " ").trim();
        }

        // 移除特殊字符
        cleaned = cleaned.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

        // 限制长度
        if (cleaned.length() > config.getMaxCellTextLength()) {
            cleaned = cleaned.substring(0, config.getMaxCellTextLength()) + "...";
        }

        return cleaned;
    }

    /**
     * 验证单元格内容
     */
    private boolean validateCellContent(String text, double confidence, TableExtractionConfig config) {
        // 检查置信度
        if (confidence < config.getMinCellConfidence()) {
            return false;
        }

        // 检查文本长度
        if (text.length() > config.getMaxCellTextLength()) {
            return false;
        }

        // 检查是否包含有效字符
        if (text.trim().isEmpty()) {
            return true; // 空单元格是有效的
        }

        // 检查是否主要由有效字符组成
        long validChars = text.chars()
            .filter(c -> Character.isLetterOrDigit(c) || Character.isWhitespace(c) ||
                        ".,;:!?-_()[]{}\"'".indexOf(c) >= 0)
            .count();

        double validRatio = (double) validChars / text.length();
        return validRatio >= 0.7; // 至少70%的字符是有效的
    }

    /**
     * 应用智能合并
     */
    private List<List<CellData>> applySmartMerging(List<List<CellData>> cellData,
                                                 TableExtractionConfig config) {
        // 简化的智能合并实现
        // 在实际应用中，可以实现更复杂的合并逻辑

        List<List<CellData>> mergedData = new ArrayList<>();

        for (List<CellData> row : cellData) {
            List<CellData> mergedRow = new ArrayList<>();

            for (int i = 0; i < row.size(); i++) {
                CellData currentCell = row.get(i);

                // 检查是否需要与下一个单元格合并
                if (i < row.size() - 1) {
                    CellData nextCell = row.get(i + 1);

                    if (shouldMergeCells(currentCell, nextCell, config)) {
                        // 合并单元格
                        CellData mergedCell = mergeCells(currentCell, nextCell);
                        mergedRow.add(mergedCell);
                        i++; // 跳过下一个单元格
                        continue;
                    }
                }

                mergedRow.add(currentCell);
            }

            mergedData.add(mergedRow);
        }

        return mergedData;
    }

    /**
     * 判断是否应该合并单元格
     */
    private boolean shouldMergeCells(CellData cell1, CellData cell2, TableExtractionConfig config) {
        // 如果其中一个单元格为空，考虑合并
        if (cell1.isEmpty() && !cell2.isEmpty()) {
            return true;
        }

        if (!cell1.isEmpty() && cell2.isEmpty()) {
            return true;
        }

        // 如果两个单元格的置信度都很低，考虑合并
        if (cell1.getConfidence() < config.getMergingThreshold() &&
            cell2.getConfidence() < config.getMergingThreshold()) {
            return true;
        }

        return false;
    }

    /**
     * 合并两个单元格
     */
    private CellData mergeCells(CellData cell1, CellData cell2) {
        String mergedText;
        if (cell1.isEmpty()) {
            mergedText = cell2.getText();
        } else if (cell2.isEmpty()) {
            mergedText = cell1.getText();
        } else {
            mergedText = cell1.getText() + " " + cell2.getText();
        }

        // 合并边界
        Rectangle bounds1 = cell1.getBounds();
        Rectangle bounds2 = cell2.getBounds();
        Rectangle mergedBounds = bounds1.union(bounds2);

        // 使用较高的置信度
        double mergedConfidence = Math.max(cell1.getConfidence(), cell2.getConfidence());

        return new CellData(mergedText, mergedBounds, mergedConfidence);
    }

    /**
     * 获取表格提取的调试信息
     */
    public String getDebugInfo(BufferedImage image, List<TableDetector.TableRegion> tableRegions,
                              TableExtractionConfig config) {
        StringBuilder debug = new StringBuilder();
        debug.append("=== Table Extraction Debug Info ===\n");
        debug.append("Image Size: ").append(image.getWidth()).append("x").append(image.getHeight()).append("\n");
        debug.append("Table Regions: ").append(tableRegions.size()).append("\n");
        debug.append("Config: ").append(configToString(config)).append("\n");

        try {
            TableExtractionResult result = extractTables(image, tableRegions, config);
            debug.append("Extraction Result: ").append(result.isSuccess() ? "SUCCESS" : "FAILED").append("\n");
            debug.append("Extracted Tables: ").append(result.getTables().size()).append("\n");

            for (int i = 0; i < result.getTables().size(); i++) {
                TableData table = result.getTables().get(i);
                debug.append("Table ").append(i + 1).append(": ")
                     .append(table.getRows()).append("x").append(table.getColumns())
                     .append(" cells, confidence: ").append(String.format("%.3f", table.getConfidence()))
                     .append("\n");

                // 显示前几行内容
                for (int row = 0; row < Math.min(3, table.getRows()); row++) {
                    debug.append("  Row ").append(row + 1).append(": ");
                    for (int col = 0; col < Math.min(5, table.getColumns()); col++) {
                        CellData cell = table.getCell(row, col);
                        String cellText = cell.getText();
                        if (cellText.length() > 20) {
                            cellText = cellText.substring(0, 17) + "...";
                        }
                        debug.append("[").append(cellText).append("] ");
                    }
                    debug.append("\n");
                }
            }

            debug.append("Metadata: ").append(result.getMetadata()).append("\n");

        } catch (Exception e) {
            debug.append("Error during extraction: ").append(e.getMessage()).append("\n");
        }

        debug.append("=== End Debug Info ===\n");
        return debug.toString();
    }

    /**
     * 配置转字符串
     */
    private String configToString(TableExtractionConfig config) {
        return String.format("minConfidence=%.2f, validation=%s, cleaning=%s, smartMerging=%s",
            config.getMinCellConfidence(),
            config.isEnableCellValidation(),
            config.isEnableContentCleaning(),
            config.isEnableSmartMerging());
    }
}
