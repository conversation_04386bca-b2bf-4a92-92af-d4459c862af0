package com.talkweb.ai.converter.util.word;

/**
 * Word文档转换选项
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class WordConversionOptions {

    /**
     * 图片格式枚举
     */
    public enum ImageFormat {
        PNG, JPEG, GIF, BMP, ORIGINAL
    }
    
    private WordConversionMode mode = WordConversionMode.STANDARD;
    private boolean preserveFormatting = true;
    private boolean includeImages = true;
    private boolean includeTables = true;
    private boolean includeHeaders = true;
    private boolean includeFooters = false;
    private boolean normalizeWhitespace = true;
    private boolean convertTables = true;
    private boolean convertImages = true;
    private boolean convertFootnotes = false;
    private boolean extractImages = false;
    private String imageDirectory = "images";
    private String imageOutputDir = "images";
    private int maxImageSize = 1024 * 1024; // 1MB
    private int maxImageWidth = 800;
    private String imageFormat = "png";
    private boolean preserveTextFormatting = true;
    private boolean skipEmptyParagraphs = false;
    private boolean preserveTableFormatting = true;
    private boolean generateImageAltText = true;
    private boolean includePageBreaks = false;
    private boolean preserveListFormatting = true;
    private boolean convertHeadersFooters = false;
    private boolean includeComments = false;
    private boolean preserveHyperlinks = true;
    private ImageFormat preferredImageFormat = ImageFormat.PNG;
    private int maxImageHeight = 600;
    
    public WordConversionOptions() {
        // Default values are set in field declarations
    }

    /**
     * Copy constructor
     */
    public WordConversionOptions(WordConversionOptions other) {
        this.includeTables = other.includeTables;
        this.includeHeaders = other.includeHeaders;
        this.includeFooters = other.includeFooters;
        this.normalizeWhitespace = other.normalizeWhitespace;
        this.convertTables = other.convertTables;
        this.convertImages = other.convertImages;
        this.convertFootnotes = other.convertFootnotes;
        this.extractImages = other.extractImages;
        this.imageDirectory = other.imageDirectory;
        this.imageOutputDir = other.imageOutputDir;
        this.maxImageSize = other.maxImageSize;
        this.maxImageWidth = other.maxImageWidth;
        this.imageFormat = other.imageFormat;
        this.preserveTextFormatting = other.preserveTextFormatting;
        this.skipEmptyParagraphs = other.skipEmptyParagraphs;
        this.preserveTableFormatting = other.preserveTableFormatting;
        this.generateImageAltText = other.generateImageAltText;
        this.includePageBreaks = other.includePageBreaks;
        this.preserveListFormatting = other.preserveListFormatting;
        this.convertHeadersFooters = other.convertHeadersFooters;
        this.includeComments = other.includeComments;
        this.preserveHyperlinks = other.preserveHyperlinks;
        this.preferredImageFormat = other.preferredImageFormat;
        this.maxImageHeight = other.maxImageHeight;
    }
    
    public static WordConversionOptions createDefault() {
        return new WordConversionOptions();
    }
    
    public static WordConversionOptions createMinimal() {
        WordConversionOptions options = new WordConversionOptions();
        options.setPreserveFormatting(false);
        options.setIncludeImages(false);
        options.setIncludeHeaders(false);
        options.setIncludeFooters(false);
        return options;
    }
    
    public static WordConversionOptions createComplete() {
        WordConversionOptions options = new WordConversionOptions();
        options.setIncludeHeaders(true);
        options.setIncludeFooters(true);
        options.setIncludeImages(true);
        options.setIncludeTables(true);
        return options;
    }

    public static WordConversionOptions basic() {
        return createMinimal();
    }

    public static WordConversionOptions highFidelity() {
        return createComplete();
    }
    
    // Getters and setters
    public WordConversionMode getMode() {
        return mode;
    }
    
    public void setMode(WordConversionMode mode) {
        this.mode = mode;
    }
    
    public boolean isPreserveFormatting() {
        return preserveFormatting;
    }
    
    public void setPreserveFormatting(boolean preserveFormatting) {
        this.preserveFormatting = preserveFormatting;
    }
    
    public boolean isIncludeImages() {
        return includeImages;
    }
    
    public void setIncludeImages(boolean includeImages) {
        this.includeImages = includeImages;
    }
    
    public boolean isIncludeTables() {
        return includeTables;
    }
    
    public void setIncludeTables(boolean includeTables) {
        this.includeTables = includeTables;
    }
    
    public boolean isIncludeHeaders() {
        return includeHeaders;
    }
    
    public void setIncludeHeaders(boolean includeHeaders) {
        this.includeHeaders = includeHeaders;
    }
    
    public boolean isIncludeFooters() {
        return includeFooters;
    }
    
    public void setIncludeFooters(boolean includeFooters) {
        this.includeFooters = includeFooters;
    }
    
    public boolean isNormalizeWhitespace() {
        return normalizeWhitespace;
    }
    
    public void setNormalizeWhitespace(boolean normalizeWhitespace) {
        this.normalizeWhitespace = normalizeWhitespace;
    }
    
    public int getMaxImageWidth() {
        return maxImageWidth;
    }
    
    public WordConversionOptions setMaxImageWidth(int maxImageWidth) {
        this.maxImageWidth = maxImageWidth;
        return this;
    }
    
    public String getImageFormat() {
        return imageFormat;
    }
    
    public void setImageFormat(String imageFormat) {
        this.imageFormat = imageFormat;
    }

    public boolean isConvertTables() {
        return convertTables;
    }

    public WordConversionOptions setConvertTables(boolean convertTables) {
        this.convertTables = convertTables;
        return this;
    }

    public boolean isConvertImages() {
        return convertImages;
    }

    public WordConversionOptions setConvertImages(boolean convertImages) {
        this.convertImages = convertImages;
        return this;
    }

    public boolean isConvertFootnotes() {
        return convertFootnotes;
    }

    public WordConversionOptions setConvertFootnotes(boolean convertFootnotes) {
        this.convertFootnotes = convertFootnotes;
        return this;
    }

    public boolean isExtractImages() {
        return extractImages;
    }

    public WordConversionOptions setExtractImages(boolean extractImages) {
        this.extractImages = extractImages;
        return this;
    }

    public String getImageDirectory() {
        return imageDirectory;
    }

    public WordConversionOptions setImageDirectory(String imageDirectory) {
        this.imageDirectory = imageDirectory;
        return this;
    }

    public int getMaxImageSize() {
        return maxImageSize;
    }

    public WordConversionOptions setMaxImageSize(int maxImageSize) {
        this.maxImageSize = maxImageSize;
        return this;
    }

    // Fluent API methods for chaining
    public WordConversionOptions setPreserveTableFormatting(boolean preserve) {
        this.includeTables = preserve;
        return this;
    }

    // Removed duplicate method - using the one below

    public WordConversionOptions setPreserveTextFormatting(boolean preserve) {
        this.preserveFormatting = preserve;
        return this;
    }

    public WordConversionOptions setSkipEmptyParagraphs(boolean skip) {
        this.skipEmptyParagraphs = skip;
        return this;
    }

    public boolean isPreserveTextFormatting() {
        return preserveTextFormatting;
    }

    public boolean isSkipEmptyParagraphs() {
        return skipEmptyParagraphs;
    }

    public boolean isPreserveTableFormatting() {
        return preserveTableFormatting;
    }

    public boolean isGenerateImageAltText() {
        return generateImageAltText;
    }

    public WordConversionOptions setGenerateImageAltText(boolean generateImageAltText) {
        this.generateImageAltText = generateImageAltText;
        return this;
    }

    public String getImageOutputDir() {
        return imageOutputDir;
    }

    public WordConversionOptions setImageOutputDir(String imageOutputDir) {
        this.imageOutputDir = imageOutputDir;
        return this;
    }

    public boolean isIncludePageBreaks() {
        return includePageBreaks;
    }

    public WordConversionOptions setIncludePageBreaks(boolean includePageBreaks) {
        this.includePageBreaks = includePageBreaks;
        return this;
    }

    public boolean isPreserveListFormatting() {
        return preserveListFormatting;
    }

    public WordConversionOptions setPreserveListFormatting(boolean preserveListFormatting) {
        this.preserveListFormatting = preserveListFormatting;
        return this;
    }

    public boolean isConvertHeadersFooters() {
        return convertHeadersFooters;
    }

    public WordConversionOptions setConvertHeadersFooters(boolean convertHeadersFooters) {
        this.convertHeadersFooters = convertHeadersFooters;
        return this;
    }

    public boolean isIncludeComments() {
        return includeComments;
    }

    public WordConversionOptions setIncludeComments(boolean includeComments) {
        this.includeComments = includeComments;
        return this;
    }

    public boolean isPreserveHyperlinks() {
        return preserveHyperlinks;
    }

    public WordConversionOptions setPreserveHyperlinks(boolean preserveHyperlinks) {
        this.preserveHyperlinks = preserveHyperlinks;
        return this;
    }

    public ImageFormat getPreferredImageFormat() {
        return preferredImageFormat;
    }

    public WordConversionOptions setPreferredImageFormat(ImageFormat preferredImageFormat) {
        this.preferredImageFormat = preferredImageFormat;
        return this;
    }

    public int getMaxImageHeight() {
        return maxImageHeight;
    }

    public WordConversionOptions setMaxImageHeight(int maxImageHeight) {
        this.maxImageHeight = maxImageHeight;
        return this;
    }
}
