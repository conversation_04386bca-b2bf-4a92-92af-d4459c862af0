package com.talkweb.ai.converter.util.ppt;

import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Context class for PowerPoint to Markdown conversion
 * 
 * Maintains state and provides utilities during the conversion process,
 * including caching, error tracking, and resource management.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class PptConversionContext {
    
    private static final Logger logger = LoggerFactory.getLogger(PptConversionContext.class);
    
    private final PptConversionConfig config;
    private final File sourceFile;
    private final long startTime;
    
    // Slide show references
    private XMLSlideShow xmlSlideShow;
    private HSLFSlideShow hslSlideShow;
    
    // Conversion state
    private int currentSlideIndex = 0;
    private int totalSlides = 0;
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    private final List<String> errors = new ArrayList<>();
    private final List<String> warnings = new ArrayList<>();
    private final Map<String, String> extractedImages = new HashMap<>();
    private final Set<String> processedShapes = new HashSet<>();
    
    // Statistics
    private int processedSlides = 0;
    private int skippedSlides = 0;
    private int extractedImagesCount = 0;
    private int extractedTablesCount = 0;
    private int extractedTextBoxesCount = 0;
    
    /**
     * Constructor
     */
    public PptConversionContext(PptConversionConfig config, File sourceFile) {
        this.config = config != null ? config : new PptConversionConfig();
        this.sourceFile = sourceFile;
        this.startTime = System.currentTimeMillis();
        
        logger.debug("Created conversion context for file: {}", 
            sourceFile != null ? sourceFile.getName() : "unknown");
    }
    
    // Configuration access
    public PptConversionConfig getConfig() {
        return config;
    }
    
    public File getSourceFile() {
        return sourceFile;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public long getElapsedTime() {
        return System.currentTimeMillis() - startTime;
    }
    
    // Slide show management
    public XMLSlideShow getXmlSlideShow() {
        return xmlSlideShow;
    }
    
    public void setSlideShow(XMLSlideShow slideShow) {
        this.xmlSlideShow = slideShow;
        if (slideShow != null) {
            this.totalSlides = slideShow.getSlides().size();
        }
    }
    
    public HSLFSlideShow getHslSlideShow() {
        return hslSlideShow;
    }
    
    public void setSlideShow(HSLFSlideShow slideShow) {
        this.hslSlideShow = slideShow;
        if (slideShow != null) {
            this.totalSlides = slideShow.getSlides().size();
        }
    }
    
    public boolean isXmlSlideShow() {
        return xmlSlideShow != null;
    }
    
    public boolean isHslSlideShow() {
        return hslSlideShow != null;
    }
    
    // Slide tracking
    public int getCurrentSlideIndex() {
        return currentSlideIndex;
    }
    
    public void setCurrentSlideIndex(int currentSlideIndex) {
        this.currentSlideIndex = currentSlideIndex;
    }
    
    public int getTotalSlides() {
        return totalSlides;
    }
    
    public void incrementProcessedSlides() {
        this.processedSlides++;
    }
    
    public void incrementSkippedSlides() {
        this.skippedSlides++;
    }
    
    // Caching
    public void putInCache(String key, Object value) {
        if (config.isEnableCaching() && key != null && value != null) {
            // Implement LRU-like behavior
            if (cache.size() >= config.getMaxCacheSize()) {
                // Remove oldest entry (simple implementation)
                String oldestKey = cache.keySet().iterator().next();
                cache.remove(oldestKey);
            }
            cache.put(key, value);
        }
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getFromCache(String key, Class<T> type) {
        if (config.isEnableCaching() && key != null) {
            Object value = cache.get(key);
            if (value != null && type.isInstance(value)) {
                return (T) value;
            }
        }
        return null;
    }
    
    public boolean isInCache(String key) {
        return config.isEnableCaching() && cache.containsKey(key);
    }
    
    public void clearCache() {
        cache.clear();
    }
    
    // Error and warning tracking
    public void addError(String error) {
        if (error != null) {
            errors.add(error);
            if (config.isLogDetailedErrors()) {
                logger.error("Conversion error: {}", error);
            }
        }
    }
    
    public void addWarning(String warning) {
        if (warning != null) {
            warnings.add(warning);
            if (config.isLogDetailedErrors()) {
                logger.warn("Conversion warning: {}", warning);
            }
        }
    }
    
    public List<String> getErrors() {
        return new ArrayList<>(errors);
    }
    
    public List<String> getWarnings() {
        return new ArrayList<>(warnings);
    }
    
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    // Image tracking
    public void addExtractedImage(String imageName, String imagePath) {
        if (imageName != null && imagePath != null) {
            extractedImages.put(imageName, imagePath);
            extractedImagesCount++;
        }
    }
    
    public String getExtractedImagePath(String imageName) {
        return extractedImages.get(imageName);
    }
    
    public Map<String, String> getExtractedImages() {
        return new HashMap<>(extractedImages);
    }
    
    // Shape processing tracking
    public void markShapeAsProcessed(String shapeId) {
        if (shapeId != null) {
            processedShapes.add(shapeId);
        }
    }
    
    public boolean isShapeProcessed(String shapeId) {
        return shapeId != null && processedShapes.contains(shapeId);
    }
    
    // Statistics
    public int getProcessedSlides() {
        return processedSlides;
    }
    
    public int getSkippedSlides() {
        return skippedSlides;
    }
    
    public int getExtractedImagesCount() {
        return extractedImagesCount;
    }
    
    public void incrementExtractedTablesCount() {
        this.extractedTablesCount++;
    }
    
    public int getExtractedTablesCount() {
        return extractedTablesCount;
    }
    
    public void incrementExtractedTextBoxesCount() {
        this.extractedTextBoxesCount++;
    }
    
    public int getExtractedTextBoxesCount() {
        return extractedTextBoxesCount;
    }
    
    // Utility methods
    public String generateImageFileName(int slideIndex, int imageIndex, String format) {
        String baseName = sourceFile != null ? 
            sourceFile.getName().replaceAll("\\.(ppt|pptx|pptm)$", "") : "presentation";
        return String.format("%s_slide_%d_image_%d.%s", baseName, slideIndex + 1, imageIndex, format);
    }
    
    public String generateShapeId(int slideIndex, int shapeIndex) {
        return String.format("slide_%d_shape_%d", slideIndex, shapeIndex);
    }
    
    /**
     * Creates a conversion summary
     */
    public String getConversionSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("PowerPoint Conversion Summary:\n");
        summary.append("  Source File: ").append(sourceFile != null ? sourceFile.getName() : "unknown").append("\n");
        summary.append("  Total Slides: ").append(totalSlides).append("\n");
        summary.append("  Processed Slides: ").append(processedSlides).append("\n");
        summary.append("  Skipped Slides: ").append(skippedSlides).append("\n");
        summary.append("  Extracted Images: ").append(extractedImagesCount).append("\n");
        summary.append("  Extracted Tables: ").append(extractedTablesCount).append("\n");
        summary.append("  Extracted Text Boxes: ").append(extractedTextBoxesCount).append("\n");
        summary.append("  Errors: ").append(errors.size()).append("\n");
        summary.append("  Warnings: ").append(warnings.size()).append("\n");
        summary.append("  Processing Time: ").append(getElapsedTime()).append(" ms\n");
        
        return summary.toString();
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        try {
            clearCache();
            processedShapes.clear();
            
            if (xmlSlideShow != null) {
                xmlSlideShow.close();
                xmlSlideShow = null;
            }
            
            if (hslSlideShow != null) {
                hslSlideShow.close();
                hslSlideShow = null;
            }
            
            logger.debug("Conversion context cleaned up");
            
        } catch (Exception e) {
            logger.warn("Error during context cleanup", e);
        }
    }
    
    @Override
    public String toString() {
        return "PptConversionContext{" +
            "sourceFile=" + (sourceFile != null ? sourceFile.getName() : "null") +
            ", currentSlide=" + currentSlideIndex +
            ", totalSlides=" + totalSlides +
            ", processedSlides=" + processedSlides +
            ", errors=" + errors.size() +
            ", warnings=" + warnings.size() +
            '}';
    }
}
