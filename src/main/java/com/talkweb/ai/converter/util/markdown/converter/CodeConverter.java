package com.talkweb.ai.converter.util.markdown.converter;

import com.talkweb.ai.converter.util.markdown.ConversionContext;
import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;

import java.util.Set;

import static com.talkweb.ai.converter.util.markdown.MarkdownConstants.*;

/**
 * Converter for HTML code elements (code, pre)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class CodeConverter extends AbstractElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(TAG_CODE, TAG_PRE);
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String tagName = element.tagName().toLowerCase();
        
        switch (tagName) {
            case TAG_CODE:
                convertInlineCode(element, builder, context);
                break;
            case TAG_PRE:
                convertCodeBlock(element, builder, context);
                break;
            default:
                throw new ConversionException("Unsupported code tag: " + tagName);
        }
    }
    
    @Override
    public boolean shouldProcessChildren(Element element, ConversionContext context) {
        // We extract text content directly, so don't process children separately
        return false;
    }
    
    @Override
    public int getPriority() {
        return 85; // High priority for code elements
    }
    
    @Override
    public void beforeConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (TAG_PRE.equals(element.tagName().toLowerCase())) {
            context.setInCodeBlock(true);
        }
    }
    
    @Override
    public void afterConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (TAG_PRE.equals(element.tagName().toLowerCase())) {
            context.setInCodeBlock(false);
        }
    }
    
    /**
     * Converts inline code elements
     * 
     * @param element the code element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertInlineCode(Element element, MarkdownBuilder builder, ConversionContext context) {
        // Check if this code element is inside a pre element
        if (context.hasAncestor(TAG_PRE)) {
            // Inside pre, just extract text without code markers
            String codeText = extractCodeContent(element);
            builder.append(codeText);
        } else {
            // Standalone inline code
            String codeText = extractCodeContent(element);
            if (!codeText.isEmpty()) {
                builder.code(codeText);
            }
        }
    }
    
    /**
     * Converts code block elements (pre)
     * 
     * @param element the pre element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertCodeBlock(Element element, MarkdownBuilder builder, ConversionContext context) {
        String language = detectLanguage(element);
        String codeContent = extractCodeContent(element);
        
        if (!codeContent.isEmpty()) {
            builder.codeBlock(codeContent, language);
        }
    }
    
    /**
     * Extracts code content from an element, preserving formatting
     * 
     * @param element the code element
     * @return the code content
     */
    private String extractCodeContent(Element element) {
        // For code elements, we want to preserve the exact text content
        // including whitespace and line breaks
        return element.text();
    }
    
    /**
     * Attempts to detect the programming language from the element
     * 
     * @param element the code element
     * @return the detected language, or null if not detected
     */
    private String detectLanguage(Element element) {
        // Check class attribute for language hints
        String className = getAttribute(element, "class");
        if (!className.isEmpty()) {
            // Common patterns: "language-java", "lang-python", "highlight-javascript", etc.
            String language = extractLanguageFromClass(className);
            if (language != null) {
                return language;
            }
        }
        
        // Check data-language attribute
        String dataLang = getAttribute(element, "data-language");
        if (!dataLang.isEmpty()) {
            return dataLang;
        }
        
        // Check lang attribute
        String lang = getAttribute(element, "lang");
        if (!lang.isEmpty()) {
            return lang;
        }
        
        // Check if there's a code element inside pre with class
        if (TAG_PRE.equals(element.tagName().toLowerCase())) {
            Element codeChild = element.selectFirst("code");
            if (codeChild != null) {
                String codeClass = getAttribute(codeChild, "class");
                if (!codeClass.isEmpty()) {
                    return extractLanguageFromClass(codeClass);
                }
            }
        }
        
        return null; // No language detected
    }
    
    /**
     * Extracts language from CSS class names
     * 
     * @param className the CSS class string
     * @return the extracted language, or null if not found
     */
    private String extractLanguageFromClass(String className) {
        if (className == null || className.trim().isEmpty()) {
            return null;
        }
        
        String[] classes = className.toLowerCase().split("\\s+");
        
        for (String cls : classes) {
            // Pattern: language-xxx
            if (cls.startsWith("language-")) {
                return cls.substring(9);
            }
            
            // Pattern: lang-xxx
            if (cls.startsWith("lang-")) {
                return cls.substring(5);
            }
            
            // Pattern: highlight-xxx
            if (cls.startsWith("highlight-")) {
                return cls.substring(10);
            }
            
            // Pattern: brush-xxx (SyntaxHighlighter)
            if (cls.startsWith("brush-")) {
                return cls.substring(6);
            }
            
            // Direct language names (common ones)
            if (isKnownLanguage(cls)) {
                return cls;
            }
        }
        
        return null;
    }
    
    /**
     * Checks if a string is a known programming language
     * 
     * @param lang the potential language name
     * @return true if it's a known language
     */
    private boolean isKnownLanguage(String lang) {
        // Common programming languages
        Set<String> knownLanguages = Set.of(
            "java", "javascript", "js", "python", "py", "c", "cpp", "csharp", "cs",
            "php", "ruby", "go", "rust", "swift", "kotlin", "scala", "html", "css",
            "xml", "json", "yaml", "yml", "sql", "bash", "shell", "sh", "powershell",
            "typescript", "ts", "dart", "r", "matlab", "perl", "lua", "haskell",
            "clojure", "erlang", "elixir", "fsharp", "vb", "vbnet", "objective-c",
            "assembly", "asm", "dockerfile", "makefile", "gradle", "maven", "ant"
        );
        
        return knownLanguages.contains(lang.toLowerCase());
    }
}
