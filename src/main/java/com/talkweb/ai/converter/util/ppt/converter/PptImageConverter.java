package com.talkweb.ai.converter.util.ppt.converter;

import com.talkweb.ai.converter.util.ppt.PptConversionContext;
import org.apache.poi.sl.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Converter for PowerPoint images and picture shapes
 * 
 * Handles the extraction and conversion of images from PowerPoint presentations,
 * including embedded images, charts rendered as images, and other graphical content.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class PptImageConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(PptImageConverter.class);
    
    // Supported image formats
    private static final String[] SUPPORTED_FORMATS = {"png", "jpg", "jpeg", "gif", "bmp"};
    
    /**
     * Converts a picture shape to Markdown image reference
     */
    public void convertImage(PictureShape<?,?> pictureShape, StringBuilder markdown, 
                           PptConversionContext context, int shapeIndex) {
        
        if (pictureShape == null) {
            context.addWarning("Encountered null picture shape");
            return;
        }
        
        if (!context.getConfig().isExtractImages()) {
            // Add placeholder without extracting the actual image
            markdown.append("![Image]()\n\n");
            return;
        }
        
        try {
            logger.debug("Converting image shape {}", shapeIndex);
            
            // Extract image data
            PictureData pictureData = pictureShape.getPictureData();
            if (pictureData == null) {
                context.addWarning("Picture shape contains no image data");
                markdown.append("![Image - No Data]()\n\n");
                return;
            }
            
            // Generate image filename
            String imageFileName = generateImageFileName(context, shapeIndex, pictureData);
            
            // Extract and save image
            String imagePath = extractAndSaveImage(pictureData, imageFileName, context);
            
            if (imagePath != null) {
                // Create Markdown image reference
                String altText = getImageAltText(pictureShape, context);
                String title = getImageTitle(pictureShape, context);
                
                markdown.append("![").append(altText).append("](").append(imagePath);
                
                if (title != null && !title.trim().isEmpty()) {
                    markdown.append(" \"").append(title.trim()).append("\"");
                }
                
                markdown.append(")\n\n");
                
                // Track extracted image
                context.addExtractedImage(imageFileName, imagePath);
                
            } else {
                // Fallback if image extraction failed
                markdown.append("![Image - Extraction Failed]()\n\n");
                context.addWarning("Failed to extract image from shape " + shapeIndex);
            }
            
        } catch (Exception e) {
            String error = "Failed to convert image shape " + shapeIndex + ": " + e.getMessage();
            context.addError(error);
            
            if (context.getConfig().isStrictMode()) {
                throw new RuntimeException(error, e);
            }
            
            // Add error placeholder in loose mode
            markdown.append("![Image - Conversion Error]()\n\n");
            logger.debug("Image conversion error", e);
        }
    }
    
    /**
     * Extracts and saves image data to file
     */
    private String extractAndSaveImage(PictureData pictureData, String fileName, 
                                     PptConversionContext context) {
        
        try {
            // Get image data
            byte[] imageData = pictureData.getData();
            if (imageData == null || imageData.length == 0) {
                context.addWarning("Image data is empty");
                return null;
            }
            
            // Create output directory
            Path outputDir = createImageOutputDirectory(context);
            if (outputDir == null) {
                return null;
            }
            
            // Create full image path
            Path imagePath = outputDir.resolve(fileName);
            
            // Process and save image
            if (shouldResizeImage(imageData, context)) {
                return saveResizedImage(imageData, imagePath, context);
            } else {
                return saveOriginalImage(imageData, imagePath, context);
            }
            
        } catch (Exception e) {
            context.addError("Failed to extract image: " + e.getMessage());
            logger.debug("Image extraction error", e);
            return null;
        }
    }
    
    /**
     * Creates the image output directory
     */
    private Path createImageOutputDirectory(PptConversionContext context) {
        try {
            Path baseDir;
            
            if (context.getSourceFile() != null) {
                baseDir = context.getSourceFile().getParentFile().toPath();
            } else {
                baseDir = Paths.get(".");
            }
            
            Path imageDir = baseDir.resolve(context.getConfig().getImageOutputDirectory());
            
            if (!Files.exists(imageDir)) {
                Files.createDirectories(imageDir);
                logger.debug("Created image output directory: {}", imageDir);
            }
            
            return imageDir;
            
        } catch (Exception e) {
            context.addError("Failed to create image output directory: " + e.getMessage());
            logger.debug("Directory creation error", e);
            return null;
        }
    }
    
    /**
     * Determines if image should be resized based on configuration
     */
    private boolean shouldResizeImage(byte[] imageData, PptConversionContext context) {
        try {
            // Check if resizing is needed based on configuration
            int maxWidth = context.getConfig().getMaxImageWidth();
            int maxHeight = context.getConfig().getMaxImageHeight();
            
            if (maxWidth <= 0 && maxHeight <= 0) {
                return false; // No size limits
            }
            
            // Read image to check dimensions
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageData));
            if (image == null) {
                return false;
            }
            
            int width = image.getWidth();
            int height = image.getHeight();
            
            return (maxWidth > 0 && width > maxWidth) || (maxHeight > 0 && height > maxHeight);
            
        } catch (Exception e) {
            logger.debug("Error checking image size", e);
            return false;
        }
    }
    
    /**
     * Saves original image without modification
     */
    private String saveOriginalImage(byte[] imageData, Path imagePath, 
                                   PptConversionContext context) {
        
        try {
            Files.write(imagePath, imageData);
            logger.debug("Saved original image: {}", imagePath.getFileName());
            return getRelativeImagePath(imagePath, context);
            
        } catch (Exception e) {
            context.addError("Failed to save original image: " + e.getMessage());
            logger.debug("Image save error", e);
            return null;
        }
    }
    
    /**
     * Saves resized image
     */
    private String saveResizedImage(byte[] imageData, Path imagePath, 
                                  PptConversionContext context) {
        
        try {
            // Read original image
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageData));
            if (originalImage == null) {
                context.addWarning("Could not read image for resizing, saving original");
                return saveOriginalImage(imageData, imagePath, context);
            }
            
            // Calculate new dimensions
            Dimension newSize = calculateNewSize(originalImage, context);
            
            // Resize image
            BufferedImage resizedImage = resizeImage(originalImage, newSize);
            
            // Determine output format
            String format = getImageFormat(imagePath, context);
            
            // Save resized image
            try (OutputStream os = Files.newOutputStream(imagePath)) {
                ImageIO.write(resizedImage, format, os);
            }
            
            logger.debug("Saved resized image: {} ({}x{} -> {}x{})", 
                        imagePath.getFileName(),
                        originalImage.getWidth(), originalImage.getHeight(),
                        newSize.width, newSize.height);
            
            return getRelativeImagePath(imagePath, context);
            
        } catch (Exception e) {
            context.addError("Failed to save resized image: " + e.getMessage());
            logger.debug("Image resize error", e);
            
            // Fallback to original image
            return saveOriginalImage(imageData, imagePath, context);
        }
    }
    
    /**
     * Calculates new image size based on configuration limits
     */
    private Dimension calculateNewSize(BufferedImage image, PptConversionContext context) {
        int originalWidth = image.getWidth();
        int originalHeight = image.getHeight();
        
        int maxWidth = context.getConfig().getMaxImageWidth();
        int maxHeight = context.getConfig().getMaxImageHeight();
        
        // If no limits, return original size
        if (maxWidth <= 0 && maxHeight <= 0) {
            return new Dimension(originalWidth, originalHeight);
        }
        
        double scaleX = maxWidth > 0 ? (double) maxWidth / originalWidth : 1.0;
        double scaleY = maxHeight > 0 ? (double) maxHeight / originalHeight : 1.0;
        
        // Use the smaller scale to maintain aspect ratio
        double scale = Math.min(scaleX, scaleY);
        
        // Don't upscale images
        if (scale > 1.0) {
            scale = 1.0;
        }
        
        int newWidth = (int) (originalWidth * scale);
        int newHeight = (int) (originalHeight * scale);
        
        return new Dimension(newWidth, newHeight);
    }
    
    /**
     * Resizes an image using high-quality scaling
     */
    private BufferedImage resizeImage(BufferedImage originalImage, Dimension newSize) {
        BufferedImage resizedImage = new BufferedImage(
            newSize.width, newSize.height, BufferedImage.TYPE_INT_RGB);
        
        Graphics2D g2d = resizedImage.createGraphics();
        try {
            // Use high-quality rendering hints
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, 
                               RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, 
                               RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, 
                               RenderingHints.VALUE_ANTIALIAS_ON);
            
            g2d.drawImage(originalImage, 0, 0, newSize.width, newSize.height, null);
            
        } finally {
            g2d.dispose();
        }
        
        return resizedImage;
    }
    
    /**
     * Gets the image format for saving
     */
    private String getImageFormat(Path imagePath, PptConversionContext context) {
        String configFormat = context.getConfig().getImageFormat().toLowerCase();
        
        // Validate format
        for (String format : SUPPORTED_FORMATS) {
            if (format.equals(configFormat)) {
                return format.equals("jpg") ? "jpeg" : format; // ImageIO uses "jpeg" not "jpg"
            }
        }
        
        // Default to PNG
        return "png";
    }
    
    /**
     * Gets relative path for the image reference in Markdown
     */
    private String getRelativeImagePath(Path imagePath, PptConversionContext context) {
        try {
            if (context.getSourceFile() != null) {
                Path basePath = context.getSourceFile().getParentFile().toPath();
                Path relativePath = basePath.relativize(imagePath);
                return relativePath.toString().replace('\\', '/'); // Use forward slashes for Markdown
            } else {
                return imagePath.getFileName().toString();
            }
        } catch (Exception e) {
            logger.debug("Error calculating relative path", e);
            return imagePath.getFileName().toString();
        }
    }
    
    /**
     * Generates a unique filename for the image
     */
    private String generateImageFileName(PptConversionContext context, int shapeIndex, 
                                       PictureData pictureData) {
        
        String format = context.getConfig().getImageFormat().toLowerCase();
        
        // Ensure format is valid
        boolean validFormat = false;
        for (String supportedFormat : SUPPORTED_FORMATS) {
            if (supportedFormat.equals(format)) {
                validFormat = true;
                break;
            }
        }
        
        if (!validFormat) {
            format = "png"; // Default fallback
        }
        
        return context.generateImageFileName(context.getCurrentSlideIndex(), shapeIndex, format);
    }
    
    /**
     * Gets alt text for the image
     */
    private String getImageAltText(PictureShape<?,?> pictureShape, PptConversionContext context) {
        try {
            // Try to get alternative text from the shape
            // Note: getAlternativeText() method may not be available in all POI versions
            // For now, we'll use a generic approach
            // Future enhancement: implement reflection-based access to getAlternativeText()

            // Fallback to generic description
            return "Image from slide " + (context.getCurrentSlideIndex() + 1);

        } catch (Exception e) {
            logger.debug("Error getting image alt text", e);
            return "Image";
        }
    }
    
    /**
     * Gets title text for the image
     */
    private String getImageTitle(PictureShape<?,?> pictureShape, PptConversionContext context) {
        try {
            // Try to get title or description from the shape
            // This is a placeholder - actual implementation would depend on POI API
            return null; // No title by default
            
        } catch (Exception e) {
            logger.debug("Error getting image title", e);
            return null;
        }
    }
    
    /**
     * Checks if a picture shape has valid image data
     */
    public boolean hasValidImageData(PictureShape<?,?> pictureShape) {
        if (pictureShape == null) {
            return false;
        }
        
        try {
            PictureData pictureData = pictureShape.getPictureData();
            if (pictureData == null) {
                return false;
            }
            
            byte[] data = pictureData.getData();
            return data != null && data.length > 0;
            
        } catch (Exception e) {
            logger.debug("Error checking image data", e);
            return false;
        }
    }
    
    /**
     * Gets image information for debugging
     */
    public String getImageInfo(PictureShape<?,?> pictureShape) {
        if (pictureShape == null) {
            return "null";
        }
        
        try {
            PictureData pictureData = pictureShape.getPictureData();
            if (pictureData == null) {
                return "no data";
            }
            
            byte[] data = pictureData.getData();
            if (data == null) {
                return "null data";
            }
            
            return String.format("image (%d bytes)", data.length);
            
        } catch (Exception e) {
            return "error: " + e.getMessage();
        }
    }
}
