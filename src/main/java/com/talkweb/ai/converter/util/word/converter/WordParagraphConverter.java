package com.talkweb.ai.converter.util.word.converter;

import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import com.talkweb.ai.converter.util.word.WordConversionContext;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.hwpf.usermodel.Paragraph;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * Converter for Word paragraphs to Markdown
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class WordParagraphConverter implements WordElementConverter<Object> {
    
    private static final Logger logger = LoggerFactory.getLogger(WordParagraphConverter.class);
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s+");
    
    @Override
    public boolean canConvert(Object element, WordConversionContext context) {
        return element instanceof XWPFParagraph || element instanceof Paragraph;
    }
    
    @Override
    public void convert(Object element, MarkdownBuilder builder, WordConversionContext context) 
            throws ConversionException {
        
        validate(element, context);
        
        if (element instanceof XWPFParagraph) {
            convertXWPFParagraph((XWPFParagraph) element, builder, context);
        } else if (element instanceof Paragraph) {
            convertHWPFParagraph((Paragraph) element, builder, context);
        }
    }
    
    /**
     * Converts XWPF (docx) paragraph
     */
    private void convertXWPFParagraph(XWPFParagraph paragraph, MarkdownBuilder builder, 
                                     WordConversionContext context) throws ConversionException {
        
        String text = paragraph.getText();
        if (text == null || (text.trim().isEmpty() && context.getOptions().isSkipEmptyParagraphs())) {
            return;
        }
        
        // Check if this is a heading
        String styleName = paragraph.getStyle();
        if (isHeadingParagraph(paragraph, context)) {
            convertHeading(paragraph, builder, context);
            return;
        }
        
        // Handle list items
        if (isListItem(paragraph)) {
            convertListItem(paragraph, builder, context);
            return;
        }
        
        // Regular paragraph
        builder.newline();

        // Process runs for text formatting
        for (XWPFRun run : paragraph.getRuns()) {
            convertRun(run, builder, context);
        }

        builder.newline().newline();
    }
    
    /**
     * Converts HWPF (doc) paragraph
     */
    private void convertHWPFParagraph(Paragraph paragraph, MarkdownBuilder builder, 
                                     WordConversionContext context) throws ConversionException {
        
        String text = paragraph.text();
        if (text == null || (text.trim().isEmpty() && context.getOptions().isSkipEmptyParagraphs())) {
            return;
        }
        
        // For .doc files, we have limited formatting information
        // Just convert as plain text for now
        builder.newline();

        // Clean and normalize text
        text = normalizeText(text);
        builder.append(text);

        builder.newline().newline();
    }
    
    /**
     * Converts a text run with formatting
     */
    private void convertRun(XWPFRun run, MarkdownBuilder builder, WordConversionContext context) {
        String text = run.getText(0);
        if (text == null || text.isEmpty()) {
            return;
        }
        
        text = normalizeText(text);
        
        if (!context.getOptions().isPreserveTextFormatting()) {
            builder.append(text);
            return;
        }
        
        // Apply formatting
        boolean isBold = run.isBold();
        boolean isItalic = run.isItalic();
        boolean isStrikethrough = run.isStrikeThrough();
        
        // Handle code formatting (monospace font)
        String fontFamily = run.getFontFamily();
        boolean isCode = isMonospaceFont(fontFamily);
        
        if (isCode) {
            builder.append("`").append(text).append("`");
        } else {
            // Apply markdown formatting
            if (isBold && isItalic) {
                builder.append("***").append(text).append("***");
            } else if (isBold) {
                builder.append("**").append(text).append("**");
            } else if (isItalic) {
                builder.append("*").append(text).append("*");
            } else if (isStrikethrough) {
                builder.append("~~").append(text).append("~~");
            } else {
                builder.append(text);
            }
        }
    }
    
    /**
     * Converts a heading paragraph
     */
    private void convertHeading(XWPFParagraph paragraph, MarkdownBuilder builder, 
                               WordConversionContext context) {
        
        int level = getHeadingLevel(paragraph, context);
        String text = paragraph.getText();
        
        if (text == null || text.trim().isEmpty()) {
            return;
        }
        
        text = normalizeText(text);

        builder.newline();
        for (int i = 0; i < level; i++) {
            builder.append("#");
        }
        builder.append(" ").append(text).newline().newline();
    }
    
    /**
     * Converts a list item paragraph
     */
    private void convertListItem(XWPFParagraph paragraph, MarkdownBuilder builder, 
                                WordConversionContext context) {
        
        String text = paragraph.getText();
        if (text == null || text.trim().isEmpty()) {
            return;
        }
        
        text = normalizeText(text);
        
        // Get list information
        XWPFNumbering numbering = paragraph.getDocument().getNumbering();
        String numId = paragraph.getNumID() != null ? paragraph.getNumID().toString() : null;
        
        // Determine list type and level
        int level = getListLevel(paragraph);
        boolean isOrdered = isOrderedList(paragraph, numbering);
        
        // Add indentation
        for (int i = 0; i < level; i++) {
            builder.append("  ");
        }
        
        // Add list marker
        if (isOrdered) {
            builder.append("1. ");
        } else {
            builder.append("- ");
        }
        
        // Process runs for formatting
        for (XWPFRun run : paragraph.getRuns()) {
            convertRun(run, builder, context);
        }

        builder.newline();
    }
    
    /**
     * Checks if paragraph is a heading
     */
    private boolean isHeadingParagraph(XWPFParagraph paragraph, WordConversionContext context) {
        String styleName = paragraph.getStyle();
        if (styleName != null && context.isHeadingStyle(styleName)) {
            return true;
        }
        
        // Check for heading-like formatting
        if (paragraph.getRuns().isEmpty()) {
            return false;
        }
        
        XWPFRun firstRun = paragraph.getRuns().get(0);
        if (firstRun.isBold() && firstRun.getFontSize() > 12) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Gets the heading level (1-6)
     */
    private int getHeadingLevel(XWPFParagraph paragraph, WordConversionContext context) {
        String styleName = paragraph.getStyle();
        if (styleName != null) {
            String lowerStyle = styleName.toLowerCase();
            if (lowerStyle.contains("heading") || lowerStyle.contains("标题")) {
                // Extract number from style name
                for (int i = 1; i <= 6; i++) {
                    if (lowerStyle.contains(String.valueOf(i))) {
                        return i;
                    }
                }
            }
            if (lowerStyle.contains("title")) {
                return 1;
            }
            if (lowerStyle.contains("subtitle")) {
                return 2;
            }
        }
        
        // Fallback: determine by font size
        if (!paragraph.getRuns().isEmpty()) {
            int fontSize = paragraph.getRuns().get(0).getFontSize();
            if (fontSize >= 20) return 1;
            if (fontSize >= 18) return 2;
            if (fontSize >= 16) return 3;
            if (fontSize >= 14) return 4;
            if (fontSize >= 12) return 5;
            return 6;
        }
        
        return 1; // Default to h1
    }
    
    /**
     * Checks if paragraph is a list item
     */
    private boolean isListItem(XWPFParagraph paragraph) {
        return paragraph.getNumID() != null;
    }
    
    /**
     * Gets the list level (0-based)
     */
    private int getListLevel(XWPFParagraph paragraph) {
        // This is a simplified implementation
        // In reality, you'd need to parse the numbering definition
        return 0;
    }
    
    /**
     * Checks if the list is ordered
     */
    private boolean isOrderedList(XWPFParagraph paragraph, XWPFNumbering numbering) {
        // This is a simplified implementation
        // In reality, you'd need to check the numbering format
        return false;
    }
    
    /**
     * Checks if font is monospace (code font)
     */
    private boolean isMonospaceFont(String fontFamily) {
        if (fontFamily == null) {
            return false;
        }
        
        String lowerFont = fontFamily.toLowerCase();
        return lowerFont.contains("courier") || 
               lowerFont.contains("consolas") || 
               lowerFont.contains("monaco") || 
               lowerFont.contains("menlo") ||
               lowerFont.contains("monospace");
    }
    
    /**
     * Normalizes text by cleaning whitespace
     */
    private String normalizeText(String text) {
        if (text == null) {
            return "";
        }
        
        // Replace multiple whitespace with single space
        text = WHITESPACE_PATTERN.matcher(text).replaceAll(" ");
        
        // Escape markdown special characters (but not in strict mode where we want to preserve them)
        if (!text.isEmpty()) {
            text = text.replace("*", "\\*")
                      .replace("_", "\\_")
                      .replace("`", "\\`")
                      .replace("#", "\\#")
                      .replace("[", "\\[")
                      .replace("]", "\\]")
                      .replace("(", "\\(")
                      .replace(")", "\\)")
                      .replace("|", "\\|");
        }
        
        return text.trim();
    }
    
    @Override
    public int getPriority() {
        return 100; // High priority for paragraphs
    }
}
