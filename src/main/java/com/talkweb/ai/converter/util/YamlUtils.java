package com.talkweb.ai.converter.util;

import com.talkweb.ai.converter.config.MapConfiguration;
import org.yaml.snakeyaml.Yaml;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * YAML工具类
 */
public class YamlUtils {
    /**
     * 将YAML List转换为Map的工具方法
     * @param list 要转换的列表
     * @return 包含列表元素和索引的Map
     */
    public static Map<String, Object> convertYamlListToMap(List<?> list) {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("list", list);
        for (int i = 0; i < list.size(); i++) {
            Object item = list.get(i);
            if (item instanceof Map) {
                map.put(String.valueOf(i), new MapConfiguration((Map<String, Object>) item));
            } else if (item instanceof List) {
                map.put(String.valueOf(i), convertYamlListToMap((List<?>) item));
            } else {
                map.put(String.valueOf(i), item);
            }
        }
        map.put("size", list.size());
        return map;
    }
    
    /**
     * 加载YAML文件
     * @param file YAML文件
     * @return YAML内容
     * @throws IOException 如果加载失败
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> loadYaml(File file) throws IOException {
        try (InputStream is = new FileInputStream(file)) {
            Yaml yaml = new Yaml();
            Object loaded = yaml.load(is);
            if (loaded instanceof Map) {
                return (Map<String, Object>) loaded;
            }
            return Collections.emptyMap();
        }
    }
    
    /**
     * 将嵌套的YAML映射转换为扁平的属性映射
     * @param yamlMap YAML映射
     * @return 扁平的属性映射
     */
    public static Map<String, String> flattenYamlMap(Map<String, Object> yamlMap) {
        Map<String, String> result = new HashMap<>();
        flattenYamlMapRecursive(yamlMap, "", result);
        return result;
    }
    
    private static void flattenYamlMapRecursive(Map<String, Object> yamlMap, String prefix, Map<String, String> result) {
        for (Map.Entry<String, Object> entry : yamlMap.entrySet()) {
            String key = prefix.isEmpty() ? entry.getKey() : prefix + "." + entry.getKey();
            Object value = entry.getValue();
            
            if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                flattenYamlMapRecursive(nestedMap, key, result);
            } else {
                result.put(key, value != null ? value.toString() : "");
            }
        }
    }
}