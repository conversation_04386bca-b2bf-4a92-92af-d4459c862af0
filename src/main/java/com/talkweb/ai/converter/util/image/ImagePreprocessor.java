package com.talkweb.ai.converter.util.image;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 图像预处理器
 * 
 * 提供图像预处理功能以提高OCR识别准确率，包括格式转换、
 * 去噪、二值化、倾斜校正、分辨率优化等功能。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class ImagePreprocessor {
    
    private static final Logger logger = LoggerFactory.getLogger(ImagePreprocessor.class);
    
    // 支持的图像格式
    private static final String[] SUPPORTED_FORMATS = {"png", "jpg", "jpeg", "tiff", "bmp", "gif"};
    
    // 默认处理参数
    private static final int DEFAULT_DPI = 300;
    private static final int MIN_WIDTH = 100;
    private static final int MIN_HEIGHT = 100;
    private static final int MAX_WIDTH = 4000;
    private static final int MAX_HEIGHT = 4000;

    /**
     * 去噪方法枚举
     */
    public enum DenoisingMethod {
        GAUSSIAN,           // 高斯滤波
        MEDIAN,            // 中值滤波
        BILATERAL,         // 双边滤波
        NON_LOCAL_MEANS,   // 非局部均值去噪
        ADAPTIVE           // 自适应去噪
    }

    /**
     * 二值化方法枚举
     */
    public enum BinarizationMethod {
        SIMPLE_THRESHOLD,   // 简单阈值
        ADAPTIVE_MEAN,      // 自适应均值
        ADAPTIVE_GAUSSIAN,  // 自适应高斯
        OTSU,              // Otsu算法
        ADAPTIVE_OTSU,     // 自适应Otsu
        SAUVOLA            // Sauvola算法
    }

    /**
     * 图像预处理选项 - 增强版
     */
    public static class PreprocessingOptions {
        // 基础选项
        private boolean enableDenoising = true;
        private boolean enableBinarization = true;
        private boolean enableSkewCorrection = true;
        private boolean enableResolutionOptimization = true;
        private boolean enableContrastEnhancement = true;
        private int targetDpi = DEFAULT_DPI;
        private double contrastFactor = 1.2;
        private double brightnessFactor = 1.1;
        private int binarizationThreshold = 128; // 0-255

        // 新增高级选项
        private boolean enableAdaptiveThresholding = true;  // 自适应阈值
        private boolean enableMorphologicalOperations = true; // 形态学操作
        private boolean enableEdgeEnhancement = false;      // 边缘增强
        private boolean enableNoiseReduction = true;        // 高级降噪
        private boolean enableImageSharpening = false;      // 图像锐化
        private boolean enableAutoContrast = true;          // 自动对比度调整

        // 高级参数
        private DenoisingMethod denoisingMethod = DenoisingMethod.GAUSSIAN;
        private BinarizationMethod binarizationMethod = BinarizationMethod.ADAPTIVE_OTSU;
        private int morphologyKernelSize = 3;               // 形态学核大小
        private double sharpeningStrength = 1.0;            // 锐化强度
        private int adaptiveBlockSize = 11;                 // 自适应阈值块大小
        private double adaptiveC = 2.0;                     // 自适应阈值常数
        
        // Getters and Setters
        public boolean isEnableDenoising() { return enableDenoising; }
        public void setEnableDenoising(boolean enableDenoising) { this.enableDenoising = enableDenoising; }
        
        public boolean isEnableBinarization() { return enableBinarization; }
        public void setEnableBinarization(boolean enableBinarization) { this.enableBinarization = enableBinarization; }
        
        public boolean isEnableSkewCorrection() { return enableSkewCorrection; }
        public void setEnableSkewCorrection(boolean enableSkewCorrection) { this.enableSkewCorrection = enableSkewCorrection; }
        
        public boolean isEnableResolutionOptimization() { return enableResolutionOptimization; }
        public void setEnableResolutionOptimization(boolean enableResolutionOptimization) { this.enableResolutionOptimization = enableResolutionOptimization; }
        
        public boolean isEnableContrastEnhancement() { return enableContrastEnhancement; }
        public void setEnableContrastEnhancement(boolean enableContrastEnhancement) { this.enableContrastEnhancement = enableContrastEnhancement; }
        
        public int getTargetDpi() { return targetDpi; }
        public void setTargetDpi(int targetDpi) { this.targetDpi = Math.max(72, Math.min(600, targetDpi)); }
        
        public double getContrastFactor() { return contrastFactor; }
        public void setContrastFactor(double contrastFactor) { this.contrastFactor = Math.max(0.5, Math.min(3.0, contrastFactor)); }
        
        public double getBrightnessFactor() { return brightnessFactor; }
        public void setBrightnessFactor(double brightnessFactor) { this.brightnessFactor = Math.max(0.5, Math.min(3.0, brightnessFactor)); }
        
        public int getBinarizationThreshold() { return binarizationThreshold; }
        public void setBinarizationThreshold(int binarizationThreshold) { this.binarizationThreshold = Math.max(0, Math.min(255, binarizationThreshold)); }

        // 新增字段的getter和setter方法
        public boolean isEnableAdaptiveThresholding() { return enableAdaptiveThresholding; }
        public void setEnableAdaptiveThresholding(boolean enableAdaptiveThresholding) { this.enableAdaptiveThresholding = enableAdaptiveThresholding; }

        public boolean isEnableMorphologicalOperations() { return enableMorphologicalOperations; }
        public void setEnableMorphologicalOperations(boolean enableMorphologicalOperations) { this.enableMorphologicalOperations = enableMorphologicalOperations; }

        public boolean isEnableEdgeEnhancement() { return enableEdgeEnhancement; }
        public void setEnableEdgeEnhancement(boolean enableEdgeEnhancement) { this.enableEdgeEnhancement = enableEdgeEnhancement; }

        public boolean isEnableNoiseReduction() { return enableNoiseReduction; }
        public void setEnableNoiseReduction(boolean enableNoiseReduction) { this.enableNoiseReduction = enableNoiseReduction; }

        public boolean isEnableImageSharpening() { return enableImageSharpening; }
        public void setEnableImageSharpening(boolean enableImageSharpening) { this.enableImageSharpening = enableImageSharpening; }

        public boolean isEnableAutoContrast() { return enableAutoContrast; }
        public void setEnableAutoContrast(boolean enableAutoContrast) { this.enableAutoContrast = enableAutoContrast; }

        public DenoisingMethod getDenoisingMethod() { return denoisingMethod; }
        public void setDenoisingMethod(DenoisingMethod denoisingMethod) { this.denoisingMethod = denoisingMethod != null ? denoisingMethod : DenoisingMethod.GAUSSIAN; }

        public BinarizationMethod getBinarizationMethod() { return binarizationMethod; }
        public void setBinarizationMethod(BinarizationMethod binarizationMethod) { this.binarizationMethod = binarizationMethod != null ? binarizationMethod : BinarizationMethod.ADAPTIVE_OTSU; }

        public int getMorphologyKernelSize() { return morphologyKernelSize; }
        public void setMorphologyKernelSize(int morphologyKernelSize) { this.morphologyKernelSize = Math.max(3, Math.min(15, morphologyKernelSize)); }

        public double getSharpeningStrength() { return sharpeningStrength; }
        public void setSharpeningStrength(double sharpeningStrength) { this.sharpeningStrength = Math.max(0.1, Math.min(3.0, sharpeningStrength)); }

        public int getAdaptiveBlockSize() { return adaptiveBlockSize; }
        public void setAdaptiveBlockSize(int adaptiveBlockSize) { this.adaptiveBlockSize = Math.max(3, adaptiveBlockSize % 2 == 1 ? adaptiveBlockSize : adaptiveBlockSize + 1); }

        public double getAdaptiveC() { return adaptiveC; }
        public void setAdaptiveC(double adaptiveC) { this.adaptiveC = Math.max(0.1, Math.min(10.0, adaptiveC)); }
    }
    
    /**
     * 预处理结果
     */
    public static class PreprocessingResult {
        private BufferedImage processedImage;
        private Map<String, Object> processingInfo;
        private boolean success;
        private String errorMessage;
        
        public PreprocessingResult(BufferedImage processedImage, Map<String, Object> processingInfo) {
            this.processedImage = processedImage;
            this.processingInfo = processingInfo;
            this.success = true;
        }
        
        public PreprocessingResult(String errorMessage) {
            this.errorMessage = errorMessage;
            this.success = false;
            this.processingInfo = new HashMap<>();
        }
        
        // Getters
        public BufferedImage getProcessedImage() { return processedImage; }
        public Map<String, Object> getProcessingInfo() { return processingInfo; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
    }
    
    /**
     * 预处理图像文件
     * 
     * @param imageFile 输入图像文件
     * @param options 预处理选项
     * @return 预处理结果
     */
    public PreprocessingResult preprocessImage(File imageFile, PreprocessingOptions options) {
        if (imageFile == null || !imageFile.exists()) {
            return new PreprocessingResult("Image file does not exist");
        }
        
        try {
            logger.debug("Starting image preprocessing for file: {}", imageFile.getName());
            
            // 读取图像
            BufferedImage originalImage = ImageIO.read(imageFile);
            if (originalImage == null) {
                return new PreprocessingResult("Failed to read image file");
            }
            
            return preprocessImage(originalImage, options);
            
        } catch (IOException e) {
            logger.error("Failed to read image file: {}", imageFile.getName(), e);
            return new PreprocessingResult("Failed to read image: " + e.getMessage());
        }
    }
    
    /**
     * 预处理BufferedImage
     * 
     * @param originalImage 原始图像
     * @param options 预处理选项
     * @return 预处理结果
     */
    public PreprocessingResult preprocessImage(BufferedImage originalImage, PreprocessingOptions options) {
        if (originalImage == null) {
            return new PreprocessingResult("Original image is null");
        }
        
        if (options == null) {
            options = new PreprocessingOptions();
        }
        
        try {
            Map<String, Object> processingInfo = new HashMap<>();
            processingInfo.put("originalWidth", originalImage.getWidth());
            processingInfo.put("originalHeight", originalImage.getHeight());
            
            BufferedImage processedImage = originalImage;
            
            // 1. 分辨率优化
            if (options.isEnableResolutionOptimization()) {
                processedImage = optimizeResolution(processedImage, options);
                processingInfo.put("resolutionOptimized", true);
                processingInfo.put("newWidth", processedImage.getWidth());
                processingInfo.put("newHeight", processedImage.getHeight());
            }
            
            // 2. 自动对比度调整
            if (options.isEnableAutoContrast()) {
                processedImage = autoAdjustContrast(processedImage);
                processingInfo.put("autoContrastAdjusted", true);
            }

            // 3. 对比度和亮度增强
            if (options.isEnableContrastEnhancement()) {
                processedImage = enhanceContrastAndBrightness(processedImage, options);
                processingInfo.put("contrastEnhanced", true);
                processingInfo.put("contrastFactor", options.getContrastFactor());
                processingInfo.put("brightnessFactor", options.getBrightnessFactor());
            }

            // 4. 高级降噪
            if (options.isEnableNoiseReduction()) {
                processedImage = advancedDenoise(processedImage, options);
                processingInfo.put("advancedDenoised", true);
                processingInfo.put("denoisingMethod", options.getDenoisingMethod().toString());
            } else if (options.isEnableDenoising()) {
                // 保持向后兼容性
                processedImage = denoise(processedImage);
                processingInfo.put("denoised", true);
            }

            // 5. 图像锐化
            if (options.isEnableImageSharpening()) {
                processedImage = sharpenImage(processedImage, options);
                processingInfo.put("sharpened", true);
                processingInfo.put("sharpeningStrength", options.getSharpeningStrength());
            }

            // 6. 边缘增强
            if (options.isEnableEdgeEnhancement()) {
                processedImage = enhanceEdges(processedImage);
                processingInfo.put("edgeEnhanced", true);
            }

            // 7. 倾斜校正
            if (options.isEnableSkewCorrection()) {
                double skewAngle = detectSkew(processedImage);
                if (Math.abs(skewAngle) > 0.5) { // 只有倾斜角度大于0.5度才校正
                    processedImage = correctSkew(processedImage, skewAngle);
                    processingInfo.put("skewCorrected", true);
                    processingInfo.put("skewAngle", skewAngle);
                }
            }

            // 8. 高级二值化
            if (options.isEnableBinarization()) {
                if (options.isEnableAdaptiveThresholding()) {
                    processedImage = adaptiveBinarize(processedImage, options);
                    processingInfo.put("adaptiveBinarized", true);
                    processingInfo.put("binarizationMethod", options.getBinarizationMethod().toString());
                } else {
                    // 保持向后兼容性
                    processedImage = binarize(processedImage, options.getBinarizationThreshold());
                    processingInfo.put("binarized", true);
                    processingInfo.put("binarizationThreshold", options.getBinarizationThreshold());
                }
            }

            // 9. 形态学操作
            if (options.isEnableMorphologicalOperations()) {
                processedImage = applyMorphologicalOperations(processedImage, options);
                processingInfo.put("morphologyApplied", true);
                processingInfo.put("morphologyKernelSize", options.getMorphologyKernelSize());
            }
            
            logger.debug("Image preprocessing completed successfully");
            return new PreprocessingResult(processedImage, processingInfo);
            
        } catch (Exception e) {
            logger.error("Image preprocessing failed", e);
            return new PreprocessingResult("Preprocessing failed: " + e.getMessage());
        }
    }
    
    /**
     * 检查图像格式是否支持
     * 
     * @param file 图像文件
     * @return 如果支持返回true
     */
    public boolean isSupportedFormat(File file) {
        if (file == null || !file.exists()) {
            return false;
        }
        
        String fileName = file.getName().toLowerCase();
        for (String format : SUPPORTED_FORMATS) {
            if (fileName.endsWith("." + format)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取图像格式
     * 
     * @param file 图像文件
     * @return 图像格式字符串
     */
    public String getImageFormat(File file) {
        if (file == null) {
            return null;
        }
        
        String fileName = file.getName().toLowerCase();
        for (String format : SUPPORTED_FORMATS) {
            if (fileName.endsWith("." + format)) {
                return format.equals("jpeg") ? "jpg" : format;
            }
        }
        return null;
    }
    
    // Private helper methods
    
    private BufferedImage optimizeResolution(BufferedImage image, PreprocessingOptions options) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        // 计算目标尺寸
        double scaleFactor = calculateScaleFactor(width, height, options.getTargetDpi());
        
        if (Math.abs(scaleFactor - 1.0) < 0.1) {
            return image; // 不需要缩放
        }
        
        int newWidth = (int) (width * scaleFactor);
        int newHeight = (int) (height * scaleFactor);
        
        // 确保尺寸在合理范围内
        newWidth = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, newWidth));
        newHeight = Math.max(MIN_HEIGHT, Math.min(MAX_HEIGHT, newHeight));
        
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaledImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        g2d.drawImage(image, 0, 0, newWidth, newHeight, null);
        g2d.dispose();
        
        return scaledImage;
    }
    
    private double calculateScaleFactor(int width, int height, int targetDpi) {
        // 假设原始图像是72 DPI，计算缩放因子
        double currentDpi = 72.0;
        return targetDpi / currentDpi;
    }
    
    private BufferedImage enhanceContrastAndBrightness(BufferedImage image, PreprocessingOptions options) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage enhancedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Color originalColor = new Color(image.getRGB(x, y));
                
                // 应用亮度和对比度调整
                int red = adjustPixel(originalColor.getRed(), options.getBrightnessFactor(), options.getContrastFactor());
                int green = adjustPixel(originalColor.getGreen(), options.getBrightnessFactor(), options.getContrastFactor());
                int blue = adjustPixel(originalColor.getBlue(), options.getBrightnessFactor(), options.getContrastFactor());
                
                Color newColor = new Color(red, green, blue);
                enhancedImage.setRGB(x, y, newColor.getRGB());
            }
        }
        
        return enhancedImage;
    }
    
    private int adjustPixel(int pixel, double brightnessFactor, double contrastFactor) {
        // 应用亮度调整
        double adjusted = pixel * brightnessFactor;
        
        // 应用对比度调整
        adjusted = ((adjusted - 128) * contrastFactor) + 128;
        
        // 确保值在0-255范围内
        return Math.max(0, Math.min(255, (int) adjusted));
    }
    
    private BufferedImage denoise(BufferedImage image) {
        // 使用高斯模糊进行去噪
        float[] matrix = {
            0.0625f, 0.125f, 0.0625f,
            0.125f,  0.25f,  0.125f,
            0.0625f, 0.125f, 0.0625f
        };
        
        Kernel kernel = new Kernel(3, 3, matrix);
        ConvolveOp op = new ConvolveOp(kernel, ConvolveOp.EDGE_NO_OP, null);
        
        return op.filter(image, null);
    }
    
    private double detectSkew(BufferedImage image) {
        // 简化的倾斜检测算法
        // 在实际应用中，可以使用更复杂的算法如Hough变换
        // 这里返回一个模拟的倾斜角度
        return 0.0; // 暂时返回0，表示无倾斜
    }
    
    private BufferedImage correctSkew(BufferedImage image, double angle) {
        if (Math.abs(angle) < 0.1) {
            return image;
        }
        
        double radians = Math.toRadians(angle);
        
        // 计算旋转后的图像尺寸
        int width = image.getWidth();
        int height = image.getHeight();
        
        AffineTransform transform = new AffineTransform();
        transform.rotate(radians, width / 2.0, height / 2.0);
        
        BufferedImage rotatedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = rotatedImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        g2d.setTransform(transform);
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        
        return rotatedImage;
    }
    
    private BufferedImage binarize(BufferedImage image, int threshold) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage binaryImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                
                if (gray > threshold) {
                    binaryImage.setRGB(x, y, Color.WHITE.getRGB());
                } else {
                    binaryImage.setRGB(x, y, Color.BLACK.getRGB());
                }
            }
        }
        
        return binaryImage;
    }

    // 新增的高级图像处理方法

    /**
     * 自动对比度调整
     */
    private BufferedImage autoAdjustContrast(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 计算直方图
        int[] histogram = new int[256];
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                histogram[gray]++;
            }
        }

        // 找到有效的最小和最大灰度值（忽略极端值）
        int totalPixels = width * height;
        int threshold = totalPixels / 100; // 忽略1%的极端值

        int minGray = 0, maxGray = 255;
        int count = 0;
        for (int i = 0; i < 256; i++) {
            count += histogram[i];
            if (count > threshold) {
                minGray = i;
                break;
            }
        }

        count = 0;
        for (int i = 255; i >= 0; i--) {
            count += histogram[i];
            if (count > threshold) {
                maxGray = i;
                break;
            }
        }

        // 应用线性拉伸
        BufferedImage adjustedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        double scale = 255.0 / (maxGray - minGray);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Color originalColor = new Color(image.getRGB(x, y));

                int red = Math.max(0, Math.min(255, (int) ((originalColor.getRed() - minGray) * scale)));
                int green = Math.max(0, Math.min(255, (int) ((originalColor.getGreen() - minGray) * scale)));
                int blue = Math.max(0, Math.min(255, (int) ((originalColor.getBlue() - minGray) * scale)));

                adjustedImage.setRGB(x, y, new Color(red, green, blue).getRGB());
            }
        }

        return adjustedImage;
    }

    /**
     * 高级降噪处理
     */
    private BufferedImage advancedDenoise(BufferedImage image, PreprocessingOptions options) {
        switch (options.getDenoisingMethod()) {
            case MEDIAN:
                return medianFilter(image);
            case BILATERAL:
                return bilateralFilter(image);
            case NON_LOCAL_MEANS:
                return nonLocalMeansFilter(image);
            case ADAPTIVE:
                return adaptiveFilter(image);
            case GAUSSIAN:
            default:
                return denoise(image); // 使用原有的高斯滤波
        }
    }

    /**
     * 中值滤波
     */
    private BufferedImage medianFilter(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage filteredImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                int[] reds = new int[9];
                int[] greens = new int[9];
                int[] blues = new int[9];

                int index = 0;
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dx = -1; dx <= 1; dx++) {
                        Color color = new Color(image.getRGB(x + dx, y + dy));
                        reds[index] = color.getRed();
                        greens[index] = color.getGreen();
                        blues[index] = color.getBlue();
                        index++;
                    }
                }

                java.util.Arrays.sort(reds);
                java.util.Arrays.sort(greens);
                java.util.Arrays.sort(blues);

                Color medianColor = new Color(reds[4], greens[4], blues[4]);
                filteredImage.setRGB(x, y, medianColor.getRGB());
            }
        }

        return filteredImage;
    }

    /**
     * 双边滤波（简化版）
     */
    private BufferedImage bilateralFilter(BufferedImage image) {
        // 简化的双边滤波实现
        return denoise(image); // 暂时使用高斯滤波代替
    }

    /**
     * 非局部均值滤波（简化版）
     */
    private BufferedImage nonLocalMeansFilter(BufferedImage image) {
        // 简化的非局部均值滤波实现
        return medianFilter(image); // 暂时使用中值滤波代替
    }

    /**
     * 自适应滤波
     */
    private BufferedImage adaptiveFilter(BufferedImage image) {
        // 根据图像特征选择合适的滤波方法
        int width = image.getWidth();
        int height = image.getHeight();

        // 计算图像的噪声水平
        double noiseLevel = estimateNoiseLevel(image);

        if (noiseLevel > 0.3) {
            return medianFilter(image); // 高噪声使用中值滤波
        } else {
            return denoise(image); // 低噪声使用高斯滤波
        }
    }

    /**
     * 估计图像噪声水平
     */
    private double estimateNoiseLevel(BufferedImage image) {
        // 简化的噪声估计算法
        int width = image.getWidth();
        int height = image.getHeight();
        double variance = 0.0;
        double mean = 0.0;
        int count = 0;

        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                mean += gray;
                count++;
            }
        }

        mean /= count;

        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                variance += Math.pow(gray - mean, 2);
            }
        }

        variance /= count;
        return Math.sqrt(variance) / 255.0; // 归一化到0-1范围
    }

    /**
     * 图像锐化
     */
    private BufferedImage sharpenImage(BufferedImage image, PreprocessingOptions options) {
        double strength = options.getSharpeningStrength();

        // 锐化卷积核
        float[] sharpenKernel = {
            0, (float)(-strength), 0,
            (float)(-strength), (float)(1 + 4 * strength), (float)(-strength),
            0, (float)(-strength), 0
        };

        Kernel kernel = new Kernel(3, 3, sharpenKernel);
        ConvolveOp op = new ConvolveOp(kernel, ConvolveOp.EDGE_NO_OP, null);

        return op.filter(image, null);
    }

    /**
     * 边缘增强
     */
    private BufferedImage enhanceEdges(BufferedImage image) {
        // 使用Sobel算子进行边缘检测和增强
        float[] sobelX = {
            -1, 0, 1,
            -2, 0, 2,
            -1, 0, 1
        };

        float[] sobelY = {
            -1, -2, -1,
             0,  0,  0,
             1,  2,  1
        };

        Kernel kernelX = new Kernel(3, 3, sobelX);
        Kernel kernelY = new Kernel(3, 3, sobelY);

        ConvolveOp opX = new ConvolveOp(kernelX, ConvolveOp.EDGE_NO_OP, null);
        ConvolveOp opY = new ConvolveOp(kernelY, ConvolveOp.EDGE_NO_OP, null);

        BufferedImage edgeX = opX.filter(image, null);
        BufferedImage edgeY = opY.filter(image, null);

        // 合并X和Y方向的边缘
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage enhancedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Color originalColor = new Color(image.getRGB(x, y));
                Color edgeXColor = new Color(edgeX.getRGB(x, y));
                Color edgeYColor = new Color(edgeY.getRGB(x, y));

                // 计算边缘强度
                int edgeStrength = (int) Math.sqrt(
                    Math.pow(edgeXColor.getRed(), 2) + Math.pow(edgeYColor.getRed(), 2)
                );

                // 将边缘信息添加到原图像
                int red = Math.min(255, originalColor.getRed() + edgeStrength / 4);
                int green = Math.min(255, originalColor.getGreen() + edgeStrength / 4);
                int blue = Math.min(255, originalColor.getBlue() + edgeStrength / 4);

                enhancedImage.setRGB(x, y, new Color(red, green, blue).getRGB());
            }
        }

        return enhancedImage;
    }

    /**
     * 自适应二值化
     */
    private BufferedImage adaptiveBinarize(BufferedImage image, PreprocessingOptions options) {
        switch (options.getBinarizationMethod()) {
            case ADAPTIVE_MEAN:
                return adaptiveMeanThreshold(image, options);
            case ADAPTIVE_GAUSSIAN:
                return adaptiveGaussianThreshold(image, options);
            case OTSU:
                return otsuThreshold(image);
            case SAUVOLA:
                return sauvolaThreshold(image, options);
            case ADAPTIVE_OTSU:
                return adaptiveOtsuThreshold(image, options);
            case SIMPLE_THRESHOLD:
            default:
                return binarize(image, options.getBinarizationThreshold());
        }
    }

    /**
     * 自适应均值阈值
     */
    private BufferedImage adaptiveMeanThreshold(BufferedImage image, PreprocessingOptions options) {
        int width = image.getWidth();
        int height = image.getHeight();
        int blockSize = options.getAdaptiveBlockSize();
        double c = options.getAdaptiveC();

        BufferedImage binaryImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                // 计算局部区域的均值
                double mean = calculateLocalMean(image, x, y, blockSize);

                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());

                if (gray > mean - c) {
                    binaryImage.setRGB(x, y, Color.WHITE.getRGB());
                } else {
                    binaryImage.setRGB(x, y, Color.BLACK.getRGB());
                }
            }
        }

        return binaryImage;
    }

    /**
     * 计算局部均值
     */
    private double calculateLocalMean(BufferedImage image, int centerX, int centerY, int blockSize) {
        int width = image.getWidth();
        int height = image.getHeight();
        int halfBlock = blockSize / 2;

        double sum = 0;
        int count = 0;

        for (int y = Math.max(0, centerY - halfBlock); y <= Math.min(height - 1, centerY + halfBlock); y++) {
            for (int x = Math.max(0, centerX - halfBlock); x <= Math.min(width - 1, centerX + halfBlock); x++) {
                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                sum += gray;
                count++;
            }
        }

        return count > 0 ? sum / count : 128;
    }

    /**
     * 自适应高斯阈值
     */
    private BufferedImage adaptiveGaussianThreshold(BufferedImage image, PreprocessingOptions options) {
        // 简化实现，使用自适应均值代替
        return adaptiveMeanThreshold(image, options);
    }

    /**
     * Otsu阈值
     */
    private BufferedImage otsuThreshold(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 计算直方图
        int[] histogram = new int[256];
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                histogram[gray]++;
            }
        }

        // 计算Otsu阈值
        int totalPixels = width * height;
        double sum = 0;
        for (int i = 0; i < 256; i++) {
            sum += i * histogram[i];
        }

        double sumB = 0;
        int wB = 0;
        int wF = 0;
        double varMax = 0;
        int threshold = 0;

        for (int t = 0; t < 256; t++) {
            wB += histogram[t];
            if (wB == 0) continue;

            wF = totalPixels - wB;
            if (wF == 0) break;

            sumB += t * histogram[t];

            double mB = sumB / wB;
            double mF = (sum - sumB) / wF;

            double varBetween = wB * wF * (mB - mF) * (mB - mF);

            if (varBetween > varMax) {
                varMax = varBetween;
                threshold = t;
            }
        }

        return binarize(image, threshold);
    }

    /**
     * Sauvola阈值
     */
    private BufferedImage sauvolaThreshold(BufferedImage image, PreprocessingOptions options) {
        int width = image.getWidth();
        int height = image.getHeight();
        int blockSize = options.getAdaptiveBlockSize();
        double k = 0.5; // Sauvola参数
        double R = 128; // 动态范围

        BufferedImage binaryImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                double mean = calculateLocalMean(image, x, y, blockSize);
                double stdDev = calculateLocalStdDev(image, x, y, blockSize, mean);

                double threshold = mean * (1 + k * ((stdDev / R) - 1));

                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());

                if (gray > threshold) {
                    binaryImage.setRGB(x, y, Color.WHITE.getRGB());
                } else {
                    binaryImage.setRGB(x, y, Color.BLACK.getRGB());
                }
            }
        }

        return binaryImage;
    }

    /**
     * 自适应Otsu阈值
     */
    private BufferedImage adaptiveOtsuThreshold(BufferedImage image, PreprocessingOptions options) {
        // 将图像分块，对每块应用Otsu算法
        int width = image.getWidth();
        int height = image.getHeight();
        int blockSize = options.getAdaptiveBlockSize() * 4; // 使用较大的块

        BufferedImage binaryImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);

        for (int y = 0; y < height; y += blockSize) {
            for (int x = 0; x < width; x += blockSize) {
                int endX = Math.min(x + blockSize, width);
                int endY = Math.min(y + blockSize, height);

                // 提取子图像
                BufferedImage subImage = image.getSubimage(x, y, endX - x, endY - y);
                BufferedImage binarySubImage = otsuThreshold(subImage);

                // 复制到结果图像
                for (int sy = 0; sy < binarySubImage.getHeight(); sy++) {
                    for (int sx = 0; sx < binarySubImage.getWidth(); sx++) {
                        binaryImage.setRGB(x + sx, y + sy, binarySubImage.getRGB(sx, sy));
                    }
                }
            }
        }

        return binaryImage;
    }

    /**
     * 计算局部标准差
     */
    private double calculateLocalStdDev(BufferedImage image, int centerX, int centerY, int blockSize, double mean) {
        int width = image.getWidth();
        int height = image.getHeight();
        int halfBlock = blockSize / 2;

        double sumSquares = 0;
        int count = 0;

        for (int y = Math.max(0, centerY - halfBlock); y <= Math.min(height - 1, centerY + halfBlock); y++) {
            for (int x = Math.max(0, centerX - halfBlock); x <= Math.min(width - 1, centerX + halfBlock); x++) {
                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                sumSquares += Math.pow(gray - mean, 2);
                count++;
            }
        }

        return count > 0 ? Math.sqrt(sumSquares / count) : 0;
    }

    /**
     * 形态学操作
     */
    private BufferedImage applyMorphologicalOperations(BufferedImage image, PreprocessingOptions options) {
        int kernelSize = options.getMorphologyKernelSize();

        // 先进行开运算（腐蚀后膨胀），去除小的噪声点
        BufferedImage eroded = morphologicalErosion(image, kernelSize);
        BufferedImage opened = morphologicalDilation(eroded, kernelSize);

        // 再进行闭运算（膨胀后腐蚀），填充小的空洞
        BufferedImage dilated = morphologicalDilation(opened, kernelSize);
        BufferedImage closed = morphologicalErosion(dilated, kernelSize);

        return closed;
    }

    /**
     * 形态学腐蚀
     */
    private BufferedImage morphologicalErosion(BufferedImage image, int kernelSize) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage erodedImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);
        int halfKernel = kernelSize / 2;

        for (int y = halfKernel; y < height - halfKernel; y++) {
            for (int x = halfKernel; x < width - halfKernel; x++) {
                boolean isWhite = true;

                // 检查核内所有像素
                for (int ky = -halfKernel; ky <= halfKernel && isWhite; ky++) {
                    for (int kx = -halfKernel; kx <= halfKernel && isWhite; kx++) {
                        Color color = new Color(image.getRGB(x + kx, y + ky));
                        if (color.getRed() < 128) { // 黑色像素
                            isWhite = false;
                        }
                    }
                }

                if (isWhite) {
                    erodedImage.setRGB(x, y, Color.WHITE.getRGB());
                } else {
                    erodedImage.setRGB(x, y, Color.BLACK.getRGB());
                }
            }
        }

        return erodedImage;
    }

    /**
     * 形态学膨胀
     */
    private BufferedImage morphologicalDilation(BufferedImage image, int kernelSize) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage dilatedImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);
        int halfKernel = kernelSize / 2;

        for (int y = halfKernel; y < height - halfKernel; y++) {
            for (int x = halfKernel; x < width - halfKernel; x++) {
                boolean hasWhite = false;

                // 检查核内所有像素
                for (int ky = -halfKernel; ky <= halfKernel && !hasWhite; ky++) {
                    for (int kx = -halfKernel; kx <= halfKernel && !hasWhite; kx++) {
                        Color color = new Color(image.getRGB(x + kx, y + ky));
                        if (color.getRed() > 128) { // 白色像素
                            hasWhite = true;
                        }
                    }
                }

                if (hasWhite) {
                    dilatedImage.setRGB(x, y, Color.WHITE.getRGB());
                } else {
                    dilatedImage.setRGB(x, y, Color.BLACK.getRGB());
                }
            }
        }

        return dilatedImage;
    }
}
