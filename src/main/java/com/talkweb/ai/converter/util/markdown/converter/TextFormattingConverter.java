package com.talkweb.ai.converter.util.markdown.converter;

import com.talkweb.ai.converter.util.markdown.ConversionContext;
import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;

import java.util.Set;

import static com.talkweb.ai.converter.util.markdown.MarkdownConstants.*;

/**
 * Converter for HTML text formatting elements (bold, italic, etc.)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class TextFormattingConverter extends AbstractElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(
        TAG_STRONG, TAG_B, TAG_EM, TAG_I
    );
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String tagName = element.tagName().toLowerCase();
        
        switch (tagName) {
            case TAG_STRONG:
            case TAG_B:
                convertBold(element, builder, context);
                break;
            case TAG_EM:
            case TAG_I:
                convertItalic(element, builder, context);
                break;
            default:
                throw new ConversionException("Unsupported text formatting tag: " + tagName);
        }
    }
    
    @Override
    public int getPriority() {
        return 80; // High priority for text formatting
    }
    
    /**
     * Converts bold elements
     * 
     * @param element the bold element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertBold(Element element, MarkdownBuilder builder, ConversionContext context) {
        builder.append(BOLD_MARKER);
        processChildren(element, builder, context);
        builder.append(BOLD_MARKER);
    }
    
    /**
     * Converts italic elements
     * 
     * @param element the italic element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertItalic(Element element, MarkdownBuilder builder, ConversionContext context) {
        builder.append(ITALIC_MARKER);
        processChildren(element, builder, context);
        builder.append(ITALIC_MARKER);
    }
}
