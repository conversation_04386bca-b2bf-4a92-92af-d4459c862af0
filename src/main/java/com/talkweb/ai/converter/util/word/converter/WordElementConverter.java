package com.talkweb.ai.converter.util.word.converter;

import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import com.talkweb.ai.converter.util.word.WordConversionContext;

/**
 * Interface for converting Word elements to Markdown
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public interface WordElementConverter<T> {
    
    /**
     * Checks if this converter can handle the given element
     * 
     * @param element the Word element
     * @param context the conversion context
     * @return true if this converter can handle the element
     */
    boolean canConvert(T element, WordConversionContext context);
    
    /**
     * Converts the Word element to Markdown
     * 
     * @param element the Word element to convert
     * @param builder the markdown builder to append to
     * @param context the conversion context
     * @throws ConversionException if conversion fails
     */
    void convert(T element, MarkdownBuilder builder, WordConversionContext context) 
            throws ConversionException;
    
    /**
     * Gets the priority of this converter (higher priority converters are tried first)
     * 
     * @return the priority value
     */
    default int getPriority() {
        return 0;
    }
    
    /**
     * Checks if this converter should process child elements
     * 
     * @param element the Word element
     * @param context the conversion context
     * @return true if children should be processed
     */
    default boolean shouldProcessChildren(T element, WordConversionContext context) {
        return true;
    }
    
    /**
     * Called before converting the element (pre-processing hook)
     * 
     * @param element the Word element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    default void beforeConvert(T element, MarkdownBuilder builder, WordConversionContext context) {
        // Default implementation does nothing
    }
    
    /**
     * Called after converting the element (post-processing hook)
     * 
     * @param element the Word element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    default void afterConvert(T element, MarkdownBuilder builder, WordConversionContext context) {
        // Default implementation does nothing
    }
    
    /**
     * Validates the element before conversion
     * 
     * @param element the Word element
     * @param context the conversion context
     * @throws ConversionException if validation fails
     */
    default void validate(T element, WordConversionContext context) throws ConversionException {
        if (element == null) {
            throw new ConversionException("Element cannot be null");
        }
        if (context == null) {
            throw new ConversionException("Context cannot be null");
        }
    }
    
    /**
     * Exception thrown during element conversion
     */
    class ConversionException extends Exception {
        
        public ConversionException(String message) {
            super(message);
        }
        
        public ConversionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
