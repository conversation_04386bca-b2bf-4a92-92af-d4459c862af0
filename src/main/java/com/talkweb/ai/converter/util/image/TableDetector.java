package com.talkweb.ai.converter.util.image;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 表格检测器
 * 
 * 实现基于图像处理的表格结构检测算法，包括表格边界检测、
 * 单元格分割和表格区域识别功能。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class TableDetector {
    
    private static final Logger logger = LoggerFactory.getLogger(TableDetector.class);
    
    /**
     * 表格检测配置
     */
    public static class TableDetectionConfig {
        private int minTableWidth = 100;
        private int minTableHeight = 50;
        private int minCellWidth = 20;
        private int minCellHeight = 15;
        private double lineThreshold = 0.8;
        private int morphologyKernelSize = 3;
        private boolean enableAdvancedDetection = true;
        private double confidenceThreshold = 0.7;
        
        // Getters and setters
        public int getMinTableWidth() { return minTableWidth; }
        public void setMinTableWidth(int minTableWidth) { this.minTableWidth = minTableWidth; }
        
        public int getMinTableHeight() { return minTableHeight; }
        public void setMinTableHeight(int minTableHeight) { this.minTableHeight = minTableHeight; }
        
        public int getMinCellWidth() { return minCellWidth; }
        public void setMinCellWidth(int minCellWidth) { this.minCellWidth = minCellWidth; }
        
        public int getMinCellHeight() { return minCellHeight; }
        public void setMinCellHeight(int minCellHeight) { this.minCellHeight = minCellHeight; }
        
        public double getLineThreshold() { return lineThreshold; }
        public void setLineThreshold(double lineThreshold) { this.lineThreshold = lineThreshold; }
        
        public int getMorphologyKernelSize() { return morphologyKernelSize; }
        public void setMorphologyKernelSize(int morphologyKernelSize) { this.morphologyKernelSize = morphologyKernelSize; }
        
        public boolean isEnableAdvancedDetection() { return enableAdvancedDetection; }
        public void setEnableAdvancedDetection(boolean enableAdvancedDetection) { this.enableAdvancedDetection = enableAdvancedDetection; }
        
        public double getConfidenceThreshold() { return confidenceThreshold; }
        public void setConfidenceThreshold(double confidenceThreshold) { this.confidenceThreshold = confidenceThreshold; }
    }
    
    /**
     * 表格检测结果
     */
    public static class TableDetectionResult {
        private final List<TableRegion> tables;
        private final double confidence;
        private final boolean success;
        private final String errorMessage;
        private final Map<String, Object> metadata;
        
        public TableDetectionResult(List<TableRegion> tables, double confidence) {
            this.tables = tables != null ? new ArrayList<>(tables) : new ArrayList<>();
            this.confidence = confidence;
            this.success = true;
            this.errorMessage = null;
            this.metadata = new HashMap<>();
        }
        
        public TableDetectionResult(String errorMessage) {
            this.tables = new ArrayList<>();
            this.confidence = 0.0;
            this.success = false;
            this.errorMessage = errorMessage;
            this.metadata = new HashMap<>();
        }
        
        // Getters
        public List<TableRegion> getTables() { return new ArrayList<>(tables); }
        public double getConfidence() { return confidence; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
        
        public void addMetadata(String key, Object value) {
            this.metadata.put(key, value);
        }
    }
    
    /**
     * 表格区域
     */
    public static class TableRegion {
        private final Rectangle bounds;
        private final List<List<Rectangle>> cells;
        private final double confidence;
        private final int rows;
        private final int columns;
        
        public TableRegion(Rectangle bounds, List<List<Rectangle>> cells, double confidence) {
            this.bounds = new Rectangle(bounds);
            this.cells = cells != null ? deepCopyCells(cells) : new ArrayList<>();
            this.confidence = confidence;
            this.rows = this.cells.size();
            this.columns = this.cells.isEmpty() ? 0 : this.cells.get(0).size();
        }
        
        private List<List<Rectangle>> deepCopyCells(List<List<Rectangle>> original) {
            List<List<Rectangle>> copy = new ArrayList<>();
            for (List<Rectangle> row : original) {
                List<Rectangle> rowCopy = new ArrayList<>();
                for (Rectangle cell : row) {
                    rowCopy.add(new Rectangle(cell));
                }
                copy.add(rowCopy);
            }
            return copy;
        }
        
        // Getters
        public Rectangle getBounds() { return new Rectangle(bounds); }
        public List<List<Rectangle>> getCells() { return deepCopyCells(cells); }
        public double getConfidence() { return confidence; }
        public int getRows() { return rows; }
        public int getColumns() { return columns; }
    }
    
    /**
     * 检测图像中的表格
     * 
     * @param image 输入图像
     * @return 表格检测结果
     */
    public TableDetectionResult detectTables(BufferedImage image) {
        return detectTables(image, new TableDetectionConfig());
    }
    
    /**
     * 检测图像中的表格（带配置）
     * 
     * @param image 输入图像
     * @param config 检测配置
     * @return 表格检测结果
     */
    public TableDetectionResult detectTables(BufferedImage image, TableDetectionConfig config) {
        if (image == null) {
            return new TableDetectionResult("Input image is null");
        }
        
        if (config == null) {
            config = new TableDetectionConfig();
        }
        
        try {
            logger.debug("Starting table detection for image {}x{}", image.getWidth(), image.getHeight());
            
            // 1. 图像预处理
            BufferedImage processedImage = preprocessForTableDetection(image);
            
            // 2. 线条检测
            LineDetectionResult lineResult = detectLines(processedImage, config);
            
            // 3. 表格区域识别
            List<TableRegion> tables = identifyTableRegions(lineResult, config);
            
            // 4. 表格验证和过滤
            tables = validateAndFilterTables(tables, config);
            
            // 5. 计算整体置信度
            double overallConfidence = calculateOverallConfidence(tables);
            
            TableDetectionResult result = new TableDetectionResult(tables, overallConfidence);
            result.addMetadata("processedImageSize", processedImage.getWidth() + "x" + processedImage.getHeight());
            result.addMetadata("detectedLinesCount", lineResult.getHorizontalLines().size() + lineResult.getVerticalLines().size());
            result.addMetadata("tablesFound", tables.size());
            
            logger.debug("Table detection completed. Found {} tables with confidence {}", 
                        tables.size(), overallConfidence);
            
            return result;
            
        } catch (Exception e) {
            logger.error("Table detection failed", e);
            return new TableDetectionResult("Table detection failed: " + e.getMessage());
        }
    }
    
    /**
     * 图像预处理用于表格检测
     */
    private BufferedImage preprocessForTableDetection(BufferedImage image) {
        // 转换为灰度图像
        BufferedImage grayImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g2d = grayImage.createGraphics();
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        
        // 应用二值化
        return binarizeImage(grayImage);
    }
    
    /**
     * 二值化图像
     */
    private BufferedImage binarizeImage(BufferedImage grayImage) {
        int width = grayImage.getWidth();
        int height = grayImage.getHeight();
        BufferedImage binaryImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);
        
        // 使用Otsu阈值算法
        int threshold = calculateOtsuThreshold(grayImage);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int gray = grayImage.getRGB(x, y) & 0xFF;
                int binary = gray > threshold ? 0xFFFFFF : 0x000000;
                binaryImage.setRGB(x, y, binary);
            }
        }
        
        return binaryImage;
    }
    
    /**
     * 计算Otsu阈值
     */
    private int calculateOtsuThreshold(BufferedImage grayImage) {
        int[] histogram = new int[256];
        int width = grayImage.getWidth();
        int height = grayImage.getHeight();
        int totalPixels = width * height;
        
        // 计算直方图
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int gray = grayImage.getRGB(x, y) & 0xFF;
                histogram[gray]++;
            }
        }
        
        // Otsu算法
        double maxVariance = 0;
        int bestThreshold = 0;
        
        for (int t = 0; t < 256; t++) {
            double w0 = 0, w1 = 0;
            double sum0 = 0, sum1 = 0;
            
            for (int i = 0; i <= t; i++) {
                w0 += histogram[i];
                sum0 += i * histogram[i];
            }
            
            for (int i = t + 1; i < 256; i++) {
                w1 += histogram[i];
                sum1 += i * histogram[i];
            }
            
            if (w0 == 0 || w1 == 0) continue;
            
            double mean0 = sum0 / w0;
            double mean1 = sum1 / w1;
            double variance = (w0 / totalPixels) * (w1 / totalPixels) * Math.pow(mean0 - mean1, 2);
            
            if (variance > maxVariance) {
                maxVariance = variance;
                bestThreshold = t;
            }
        }
        
        return bestThreshold;
    }
    
    /**
     * 线条检测结果
     */
    private static class LineDetectionResult {
        private final List<Line> horizontalLines;
        private final List<Line> verticalLines;
        
        public LineDetectionResult(List<Line> horizontalLines, List<Line> verticalLines) {
            this.horizontalLines = horizontalLines != null ? new ArrayList<>(horizontalLines) : new ArrayList<>();
            this.verticalLines = verticalLines != null ? new ArrayList<>(verticalLines) : new ArrayList<>();
        }
        
        public List<Line> getHorizontalLines() { return new ArrayList<>(horizontalLines); }
        public List<Line> getVerticalLines() { return new ArrayList<>(verticalLines); }
    }
    
    /**
     * 线条类
     */
    private static class Line {
        private final Point start;
        private final Point end;
        private final double confidence;
        
        public Line(Point start, Point end, double confidence) {
            this.start = new Point(start);
            this.end = new Point(end);
            this.confidence = confidence;
        }
        
        public Point getStart() { return new Point(start); }
        public Point getEnd() { return new Point(end); }
        public double getConfidence() { return confidence; }
        
        public boolean isHorizontal() {
            return Math.abs(start.y - end.y) < Math.abs(start.x - end.x);
        }
        
        public boolean isVertical() {
            return Math.abs(start.x - end.x) < Math.abs(start.y - end.y);
        }
        
        public int getLength() {
            return (int) Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2));
        }
    }
    
    /**
     * 检测线条
     */
    private LineDetectionResult detectLines(BufferedImage binaryImage, TableDetectionConfig config) {
        List<Line> horizontalLines = detectHorizontalLines(binaryImage, config);
        List<Line> verticalLines = detectVerticalLines(binaryImage, config);

        return new LineDetectionResult(horizontalLines, verticalLines);
    }

    /**
     * 检测水平线条
     */
    private List<Line> detectHorizontalLines(BufferedImage image, TableDetectionConfig config) {
        List<Line> lines = new ArrayList<>();
        int width = image.getWidth();
        int height = image.getHeight();

        for (int y = 0; y < height; y++) {
            List<Point> linePoints = new ArrayList<>();

            for (int x = 0; x < width; x++) {
                int pixel = image.getRGB(x, y) & 0xFF;
                if (pixel == 0) { // 黑色像素
                    linePoints.add(new Point(x, y));
                } else if (!linePoints.isEmpty()) {
                    // 结束当前线段
                    if (linePoints.size() >= config.getMinTableWidth() / 4) {
                        Point start = linePoints.get(0);
                        Point end = linePoints.get(linePoints.size() - 1);
                        double confidence = calculateLineConfidence(linePoints, true);
                        if (confidence >= config.getLineThreshold()) {
                            lines.add(new Line(start, end, confidence));
                        }
                    }
                    linePoints.clear();
                }
            }

            // 处理行末的线段
            if (!linePoints.isEmpty() && linePoints.size() >= config.getMinTableWidth() / 4) {
                Point start = linePoints.get(0);
                Point end = linePoints.get(linePoints.size() - 1);
                double confidence = calculateLineConfidence(linePoints, true);
                if (confidence >= config.getLineThreshold()) {
                    lines.add(new Line(start, end, confidence));
                }
            }
        }

        return lines;
    }

    /**
     * 检测垂直线条
     */
    private List<Line> detectVerticalLines(BufferedImage image, TableDetectionConfig config) {
        List<Line> lines = new ArrayList<>();
        int width = image.getWidth();
        int height = image.getHeight();

        for (int x = 0; x < width; x++) {
            List<Point> linePoints = new ArrayList<>();

            for (int y = 0; y < height; y++) {
                int pixel = image.getRGB(x, y) & 0xFF;
                if (pixel == 0) { // 黑色像素
                    linePoints.add(new Point(x, y));
                } else if (!linePoints.isEmpty()) {
                    // 结束当前线段
                    if (linePoints.size() >= config.getMinTableHeight() / 3) {
                        Point start = linePoints.get(0);
                        Point end = linePoints.get(linePoints.size() - 1);
                        double confidence = calculateLineConfidence(linePoints, false);
                        if (confidence >= config.getLineThreshold()) {
                            lines.add(new Line(start, end, confidence));
                        }
                    }
                    linePoints.clear();
                }
            }

            // 处理列末的线段
            if (!linePoints.isEmpty() && linePoints.size() >= config.getMinTableHeight() / 3) {
                Point start = linePoints.get(0);
                Point end = linePoints.get(linePoints.size() - 1);
                double confidence = calculateLineConfidence(linePoints, false);
                if (confidence >= config.getLineThreshold()) {
                    lines.add(new Line(start, end, confidence));
                }
            }
        }

        return lines;
    }

    /**
     * 计算线条置信度
     */
    private double calculateLineConfidence(List<Point> points, boolean isHorizontal) {
        if (points.size() < 2) return 0.0;

        // 计算线条的直线度
        Point start = points.get(0);
        Point end = points.get(points.size() - 1);

        double totalDeviation = 0.0;
        for (Point point : points) {
            double deviation;
            if (isHorizontal) {
                deviation = Math.abs(point.y - start.y);
            } else {
                deviation = Math.abs(point.x - start.x);
            }
            totalDeviation += deviation;
        }

        double avgDeviation = totalDeviation / points.size();
        double maxAllowedDeviation = 2.0; // 允许的最大偏差

        return Math.max(0.0, 1.0 - (avgDeviation / maxAllowedDeviation));
    }

    /**
     * 识别表格区域
     */
    private List<TableRegion> identifyTableRegions(LineDetectionResult lineResult, TableDetectionConfig config) {
        List<TableRegion> tables = new ArrayList<>();

        List<Line> hLines = lineResult.getHorizontalLines();
        List<Line> vLines = lineResult.getVerticalLines();

        // 按位置排序线条
        hLines.sort(Comparator.comparingInt(line -> line.getStart().y));
        vLines.sort(Comparator.comparingInt(line -> line.getStart().x));

        // 查找表格边界
        List<Rectangle> potentialTables = findTableBoundaries(hLines, vLines, config);

        // 为每个潜在表格创建单元格网格
        for (Rectangle tableBounds : potentialTables) {
            List<List<Rectangle>> cells = createCellGrid(tableBounds, hLines, vLines, config);
            if (cells != null && !cells.isEmpty()) {
                double confidence = calculateTableConfidence(tableBounds, cells, hLines, vLines);
                if (confidence >= config.getConfidenceThreshold()) {
                    tables.add(new TableRegion(tableBounds, cells, confidence));
                }
            }
        }

        return tables;
    }

    /**
     * 查找表格边界
     */
    private List<Rectangle> findTableBoundaries(List<Line> hLines, List<Line> vLines, TableDetectionConfig config) {
        List<Rectangle> boundaries = new ArrayList<>();

        // 简化的表格边界检测算法
        for (int i = 0; i < hLines.size() - 1; i++) {
            for (int j = i + 1; j < hLines.size(); j++) {
                Line topLine = hLines.get(i);
                Line bottomLine = hLines.get(j);

                int tableHeight = bottomLine.getStart().y - topLine.getStart().y;
                if (tableHeight < config.getMinTableHeight()) continue;

                // 查找对应的垂直边界
                for (int k = 0; k < vLines.size() - 1; k++) {
                    for (int l = k + 1; l < vLines.size(); l++) {
                        Line leftLine = vLines.get(k);
                        Line rightLine = vLines.get(l);

                        int tableWidth = rightLine.getStart().x - leftLine.getStart().x;
                        if (tableWidth < config.getMinTableWidth()) continue;

                        // 检查线条是否形成有效的表格边界
                        if (isValidTableBoundary(topLine, bottomLine, leftLine, rightLine)) {
                            Rectangle bounds = new Rectangle(
                                leftLine.getStart().x,
                                topLine.getStart().y,
                                tableWidth,
                                tableHeight
                            );
                            boundaries.add(bounds);
                        }
                    }
                }
            }
        }

        return boundaries;
    }

    /**
     * 检查是否为有效的表格边界
     */
    private boolean isValidTableBoundary(Line top, Line bottom, Line left, Line right) {
        // 检查线条是否大致对齐
        int tolerance = 10;

        boolean topBottomAligned = Math.abs(top.getStart().x - bottom.getStart().x) <= tolerance &&
                                  Math.abs(top.getEnd().x - bottom.getEnd().x) <= tolerance;

        boolean leftRightAligned = Math.abs(left.getStart().y - right.getStart().y) <= tolerance &&
                                  Math.abs(left.getEnd().y - right.getEnd().y) <= tolerance;

        return topBottomAligned && leftRightAligned;
    }

    /**
     * 创建单元格网格
     */
    private List<List<Rectangle>> createCellGrid(Rectangle tableBounds, List<Line> hLines, List<Line> vLines, TableDetectionConfig config) {
        // 获取表格范围内的线条
        List<Line> tableHLines = getTableLines(hLines, tableBounds, true);
        List<Line> tableVLines = getTableLines(vLines, tableBounds, false);

        if (tableHLines.size() < 2 || tableVLines.size() < 2) {
            return null; // 至少需要2条水平线和2条垂直线
        }

        // 按位置排序
        tableHLines.sort(Comparator.comparingInt(line -> line.getStart().y));
        tableVLines.sort(Comparator.comparingInt(line -> line.getStart().x));

        List<List<Rectangle>> cells = new ArrayList<>();

        // 创建单元格网格
        for (int row = 0; row < tableHLines.size() - 1; row++) {
            List<Rectangle> cellRow = new ArrayList<>();

            int topY = tableHLines.get(row).getStart().y;
            int bottomY = tableHLines.get(row + 1).getStart().y;
            int cellHeight = bottomY - topY;

            if (cellHeight < config.getMinCellHeight()) continue;

            for (int col = 0; col < tableVLines.size() - 1; col++) {
                int leftX = tableVLines.get(col).getStart().x;
                int rightX = tableVLines.get(col + 1).getStart().x;
                int cellWidth = rightX - leftX;

                if (cellWidth < config.getMinCellWidth()) continue;

                Rectangle cell = new Rectangle(leftX, topY, cellWidth, cellHeight);
                cellRow.add(cell);
            }

            if (!cellRow.isEmpty()) {
                cells.add(cellRow);
            }
        }

        return cells.isEmpty() ? null : cells;
    }

    /**
     * 获取表格范围内的线条
     */
    private List<Line> getTableLines(List<Line> lines, Rectangle tableBounds, boolean isHorizontal) {
        List<Line> tableLines = new ArrayList<>();

        for (Line line : lines) {
            if (isHorizontal) {
                // 水平线：检查Y坐标是否在表格范围内
                if (line.getStart().y >= tableBounds.y &&
                    line.getStart().y <= tableBounds.y + tableBounds.height) {
                    // 检查线条是否与表格有足够的重叠
                    if (hasSignificantOverlap(line, tableBounds, true)) {
                        tableLines.add(line);
                    }
                }
            } else {
                // 垂直线：检查X坐标是否在表格范围内
                if (line.getStart().x >= tableBounds.x &&
                    line.getStart().x <= tableBounds.x + tableBounds.width) {
                    // 检查线条是否与表格有足够的重叠
                    if (hasSignificantOverlap(line, tableBounds, false)) {
                        tableLines.add(line);
                    }
                }
            }
        }

        return tableLines;
    }

    /**
     * 检查线条与表格是否有显著重叠
     */
    private boolean hasSignificantOverlap(Line line, Rectangle tableBounds, boolean isHorizontal) {
        double overlapThreshold = 0.5; // 至少50%重叠

        if (isHorizontal) {
            int lineStart = Math.min(line.getStart().x, line.getEnd().x);
            int lineEnd = Math.max(line.getStart().x, line.getEnd().x);
            int tableStart = tableBounds.x;
            int tableEnd = tableBounds.x + tableBounds.width;

            int overlapStart = Math.max(lineStart, tableStart);
            int overlapEnd = Math.min(lineEnd, tableEnd);
            int overlapLength = Math.max(0, overlapEnd - overlapStart);

            return (double) overlapLength / tableBounds.width >= overlapThreshold;
        } else {
            int lineStart = Math.min(line.getStart().y, line.getEnd().y);
            int lineEnd = Math.max(line.getStart().y, line.getEnd().y);
            int tableStart = tableBounds.y;
            int tableEnd = tableBounds.y + tableBounds.height;

            int overlapStart = Math.max(lineStart, tableStart);
            int overlapEnd = Math.min(lineEnd, tableEnd);
            int overlapLength = Math.max(0, overlapEnd - overlapStart);

            return (double) overlapLength / tableBounds.height >= overlapThreshold;
        }
    }

    /**
     * 计算表格置信度
     */
    private double calculateTableConfidence(Rectangle tableBounds, List<List<Rectangle>> cells,
                                          List<Line> hLines, List<Line> vLines) {
        double confidence = 0.0;
        int factors = 0;

        // 因子1：单元格规整度
        double cellRegularity = calculateCellRegularity(cells);
        confidence += cellRegularity;
        factors++;

        // 因子2：线条质量
        double lineQuality = calculateLineQuality(hLines, vLines, tableBounds);
        confidence += lineQuality;
        factors++;

        // 因子3：表格大小合理性
        double sizeReasonableness = calculateSizeReasonableness(tableBounds, cells);
        confidence += sizeReasonableness;
        factors++;

        return factors > 0 ? confidence / factors : 0.0;
    }

    /**
     * 计算单元格规整度
     */
    private double calculateCellRegularity(List<List<Rectangle>> cells) {
        if (cells.isEmpty()) return 0.0;

        // 检查行列数的一致性
        int expectedCols = cells.get(0).size();
        boolean consistent = cells.stream().allMatch(row -> row.size() == expectedCols);

        if (!consistent) return 0.3; // 不一致的表格置信度较低

        // 检查单元格大小的一致性
        double avgWidth = cells.stream()
            .flatMap(List::stream)
            .mapToDouble(rect -> rect.getWidth())
            .average()
            .orElse(0.0);

        double avgHeight = cells.stream()
            .flatMap(List::stream)
            .mapToDouble(rect -> rect.getHeight())
            .average()
            .orElse(0.0);

        // 计算大小变异系数
        double widthVariance = cells.stream()
            .flatMap(List::stream)
            .mapToDouble(cell -> Math.pow(cell.width - avgWidth, 2))
            .average()
            .orElse(0.0);

        double heightVariance = cells.stream()
            .flatMap(List::stream)
            .mapToDouble(cell -> Math.pow(cell.height - avgHeight, 2))
            .average()
            .orElse(0.0);

        double widthCV = avgWidth > 0 ? Math.sqrt(widthVariance) / avgWidth : 1.0;
        double heightCV = avgHeight > 0 ? Math.sqrt(heightVariance) / avgHeight : 1.0;

        // 变异系数越小，规整度越高
        return Math.max(0.0, 1.0 - (widthCV + heightCV) / 2.0);
    }

    /**
     * 计算线条质量
     */
    private double calculateLineQuality(List<Line> hLines, List<Line> vLines, Rectangle tableBounds) {
        double totalConfidence = 0.0;
        int lineCount = 0;

        for (Line line : hLines) {
            if (isLineInBounds(line, tableBounds)) {
                totalConfidence += line.getConfidence();
                lineCount++;
            }
        }

        for (Line line : vLines) {
            if (isLineInBounds(line, tableBounds)) {
                totalConfidence += line.getConfidence();
                lineCount++;
            }
        }

        return lineCount > 0 ? totalConfidence / lineCount : 0.0;
    }

    /**
     * 检查线条是否在边界内
     */
    private boolean isLineInBounds(Line line, Rectangle bounds) {
        return bounds.contains(line.getStart()) || bounds.contains(line.getEnd()) ||
               bounds.intersectsLine(line.getStart().x, line.getStart().y,
                                   line.getEnd().x, line.getEnd().y);
    }

    /**
     * 计算大小合理性
     */
    private double calculateSizeReasonableness(Rectangle tableBounds, List<List<Rectangle>> cells) {
        // 检查表格大小是否合理
        int minReasonableWidth = 100;
        int minReasonableHeight = 50;
        int maxReasonableWidth = 2000;
        int maxReasonableHeight = 1500;

        double widthScore = 1.0;
        if (tableBounds.width < minReasonableWidth) {
            widthScore = (double) tableBounds.width / minReasonableWidth;
        } else if (tableBounds.width > maxReasonableWidth) {
            widthScore = (double) maxReasonableWidth / tableBounds.width;
        }

        double heightScore = 1.0;
        if (tableBounds.height < minReasonableHeight) {
            heightScore = (double) tableBounds.height / minReasonableHeight;
        } else if (tableBounds.height > maxReasonableHeight) {
            heightScore = (double) maxReasonableHeight / tableBounds.height;
        }

        // 检查单元格数量是否合理
        int totalCells = cells.stream().mapToInt(List::size).sum();
        double cellCountScore = 1.0;
        if (totalCells < 4) { // 至少2x2表格
            cellCountScore = (double) totalCells / 4.0;
        } else if (totalCells > 200) { // 避免过大的表格
            cellCountScore = 200.0 / totalCells;
        }

        return (widthScore + heightScore + cellCountScore) / 3.0;
    }

    /**
     * 验证和过滤表格
     */
    private List<TableRegion> validateAndFilterTables(List<TableRegion> tables, TableDetectionConfig config) {
        return tables.stream()
            .filter(table -> validateTable(table, config))
            .sorted(Comparator.comparingDouble(TableRegion::getConfidence).reversed())
            .collect(Collectors.toList());
    }

    /**
     * 验证单个表格
     */
    private boolean validateTable(TableRegion table, TableDetectionConfig config) {
        // 检查表格大小
        if (table.getBounds().width < config.getMinTableWidth() ||
            table.getBounds().height < config.getMinTableHeight()) {
            return false;
        }

        // 检查单元格数量
        if (table.getRows() < 2 || table.getColumns() < 2) {
            return false;
        }

        // 检查置信度
        if (table.getConfidence() < config.getConfidenceThreshold()) {
            return false;
        }

        return true;
    }

    /**
     * 计算整体置信度
     */
    private double calculateOverallConfidence(List<TableRegion> tables) {
        if (tables.isEmpty()) {
            return 0.0;
        }

        // 使用加权平均，较大的表格权重更高
        double totalWeightedConfidence = 0.0;
        double totalWeight = 0.0;

        for (TableRegion table : tables) {
            double weight = table.getBounds().width * table.getBounds().height;
            totalWeightedConfidence += table.getConfidence() * weight;
            totalWeight += weight;
        }

        return totalWeight > 0 ? totalWeightedConfidence / totalWeight : 0.0;
    }

    /**
     * 获取表格检测的调试信息
     */
    public String getDebugInfo(BufferedImage image, TableDetectionConfig config) {
        StringBuilder debug = new StringBuilder();
        debug.append("=== Table Detection Debug Info ===\n");
        debug.append("Image Size: ").append(image.getWidth()).append("x").append(image.getHeight()).append("\n");
        debug.append("Config: ").append(configToString(config)).append("\n");

        try {
            TableDetectionResult result = detectTables(image, config);
            debug.append("Detection Result: ").append(result.isSuccess() ? "SUCCESS" : "FAILED").append("\n");
            debug.append("Overall Confidence: ").append(String.format("%.3f", result.getConfidence())).append("\n");
            debug.append("Tables Found: ").append(result.getTables().size()).append("\n");

            for (int i = 0; i < result.getTables().size(); i++) {
                TableRegion table = result.getTables().get(i);
                debug.append("Table ").append(i + 1).append(": ")
                     .append(table.getBounds().width).append("x").append(table.getBounds().height)
                     .append(" at (").append(table.getBounds().x).append(",").append(table.getBounds().y).append(")")
                     .append(" - ").append(table.getRows()).append("x").append(table.getColumns())
                     .append(" cells, confidence: ").append(String.format("%.3f", table.getConfidence()))
                     .append("\n");
            }

            debug.append("Metadata: ").append(result.getMetadata()).append("\n");

        } catch (Exception e) {
            debug.append("Error during detection: ").append(e.getMessage()).append("\n");
        }

        debug.append("=== End Debug Info ===\n");
        return debug.toString();
    }

    /**
     * 配置转字符串
     */
    private String configToString(TableDetectionConfig config) {
        return String.format("minTable=%dx%d, minCell=%dx%d, lineThreshold=%.2f, confidence=%.2f",
            config.getMinTableWidth(), config.getMinTableHeight(),
            config.getMinCellWidth(), config.getMinCellHeight(),
            config.getLineThreshold(), config.getConfidenceThreshold());
    }
}
