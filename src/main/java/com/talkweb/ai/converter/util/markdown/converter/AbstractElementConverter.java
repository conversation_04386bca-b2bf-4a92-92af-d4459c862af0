package com.talkweb.ai.converter.util.markdown.converter;

import com.talkweb.ai.converter.util.markdown.ConversionContext;
import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;

import java.util.Set;
import java.util.regex.Pattern;

import static com.talkweb.ai.converter.util.markdown.MarkdownConstants.*;

/**
 * Abstract base class for element converters providing common functionality
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public abstract class AbstractElementConverter implements ElementConverter {
    
    private static final Pattern BLOCK_ELEMENT_REGEX = Pattern.compile(BLOCK_ELEMENT_PATTERN);
    
    /**
     * Gets the supported tags for this converter
     * Must be implemented by subclasses
     */
    @Override
    public abstract Set<String> getSupportedTags();
    
    /**
     * Processes child elements of the given element
     * 
     * @param element the parent element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    protected void processChildren(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (element == null || !shouldProcessChildren(element, context)) {
            return;
        }
        
        context.pushElement(element);
        try {
            for (Node child : element.childNodes()) {
                if (child instanceof Element) {
                    processChildElement((Element) child, builder, context);
                } else if (child instanceof TextNode) {
                    processTextNode((TextNode) child, builder, context);
                }
            }
        } finally {
            context.popElement();
        }
    }
    
    /**
     * Processes a child element
     *
     * @param child the child element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    protected void processChildElement(Element child, MarkdownBuilder builder, ConversionContext context) {
        // Use the element processor if available, otherwise fallback to text extraction
        if (elementProcessor != null) {
            elementProcessor.processElement(child, builder, context);
        } else {
            // Default implementation just processes text content
            String text = extractTextContent(child);
            if (!text.isEmpty()) {
                builder.append(text);
            }
        }
    }
    
    /**
     * Processes a text node
     * 
     * @param textNode the text node
     * @param builder the markdown builder
     * @param context the conversion context
     */
    protected void processTextNode(TextNode textNode, MarkdownBuilder builder, ConversionContext context) {
        String text = textNode.text();
        if (text != null && !text.trim().isEmpty()) {
            // Decode HTML entities
            text = decodeHtmlEntities(text);
            
            // Normalize whitespace unless in code block
            if (!context.isInCodeBlock()) {
                text = normalizeWhitespace(text);
            }
            
            builder.append(text);
        }
    }
    
    /**
     * Extracts text content from an element
     * 
     * @param element the element
     * @return the text content
     */
    protected String extractTextContent(Element element) {
        if (element == null) {
            return EMPTY_STRING;
        }
        
        StringBuilder text = new StringBuilder();
        extractTextRecursively(element, text);
        return text.toString().trim();
    }
    
    /**
     * Recursively extracts text from element and its children
     * 
     * @param element the element
     * @param text the text buffer
     */
    private void extractTextRecursively(Element element, StringBuilder text) {
        for (Node child : element.childNodes()) {
            if (child instanceof TextNode) {
                String nodeText = ((TextNode) child).text();
                if (!nodeText.trim().isEmpty()) {
                    text.append(nodeText);
                }
            } else if (child instanceof Element) {
                Element childElement = (Element) child;
                String tagName = childElement.tagName().toLowerCase();
                
                // Add line breaks for block elements
                if (isBlockElement(tagName)) {
                    text.append(NEWLINE);
                }
                
                extractTextRecursively(childElement, text);
                
                if (isBlockElement(tagName)) {
                    text.append(NEWLINE);
                }
            }
        }
    }
    
    /**
     * Checks if an element is a block-level element
     * 
     * @param tagName the tag name
     * @return true if block element
     */
    protected boolean isBlockElement(String tagName) {
        return BLOCK_ELEMENT_REGEX.matcher(tagName).matches();
    }
    
    /**
     * Decodes HTML entities in text
     * 
     * @param text the text to decode
     * @return the decoded text
     */
    protected String decodeHtmlEntities(String text) {
        if (text == null) {
            return EMPTY_STRING;
        }
        
        return text.replace(ENTITY_LT, DECODED_LT)
                  .replace(ENTITY_GT, DECODED_GT)
                  .replace(ENTITY_AMP, DECODED_AMP)
                  .replace(ENTITY_QUOT, DECODED_QUOT)
                  .replace(ENTITY_APOS, DECODED_APOS)
                  .replace(ENTITY_NBSP, DECODED_NBSP)
                  .replace(ENTITY_COPY, DECODED_COPY)
                  .replace(ENTITY_REG, DECODED_REG)
                  .replace(ENTITY_TRADE, DECODED_TRADE)
                  .replace(ENTITY_HELLIP, DECODED_HELLIP)
                  .replace(ENTITY_MDASH, DECODED_MDASH)
                  .replace(ENTITY_NDASH, DECODED_NDASH)
                  .replace(ENTITY_LSQUO, DECODED_LSQUO)
                  .replace(ENTITY_RSQUO, DECODED_RSQUO)
                  .replace(ENTITY_LDQUO, DECODED_LDQUO)
                  .replace(ENTITY_RDQUO, DECODED_RDQUO);
    }
    
    /**
     * Normalizes whitespace in text
     * 
     * @param text the text to normalize
     * @return the normalized text
     */
    protected String normalizeWhitespace(String text) {
        if (text == null) {
            return EMPTY_STRING;
        }
        
        // Replace multiple whitespace with single space
        return text.replaceAll(WHITESPACE_PATTERN, SPACE);
    }
    
    /**
     * Gets an attribute value from an element
     * 
     * @param element the element
     * @param attributeName the attribute name
     * @return the attribute value, or empty string if not found
     */
    protected String getAttribute(Element element, String attributeName) {
        return element.attr(attributeName);
    }
    
    /**
     * Gets an attribute value with a default
     * 
     * @param element the element
     * @param attributeName the attribute name
     * @param defaultValue the default value
     * @return the attribute value or default
     */
    protected String getAttribute(Element element, String attributeName, String defaultValue) {
        String value = element.attr(attributeName);
        return value.isEmpty() ? defaultValue : value;
    }
    
    /**
     * Checks if an element has an attribute
     * 
     * @param element the element
     * @param attributeName the attribute name
     * @return true if the attribute exists
     */
    protected boolean hasAttribute(Element element, String attributeName) {
        return element.hasAttr(attributeName);
    }
    
    /**
     * Sets the element processor for handling child elements
     * This is used by the registry to avoid circular dependencies
     *
     * @param processor the element processor
     */
    private static ElementProcessor elementProcessor;

    public static void setElementProcessor(ElementProcessor processor) {
        elementProcessor = processor;
    }

    /**
     * Interface for processing elements (used to break circular dependency)
     */
    public interface ElementProcessor {
        void processElement(Element element, MarkdownBuilder builder, ConversionContext context);
    }
    
    /**
     * Validates common element properties
     * 
     * @param element the element
     * @param context the context
     * @throws ConversionException if validation fails
     */
    @Override
    public void validate(Element element, ConversionContext context) throws ConversionException {
        ElementConverter.super.validate(element, context);
        
        // Additional validation can be added here
        if (!getSupportedTags().contains(element.tagName().toLowerCase())) {
            throw new ConversionException("Unsupported element: " + element.tagName());
        }
    }
}
