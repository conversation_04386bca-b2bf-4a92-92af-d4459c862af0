package com.talkweb.ai.converter.util.markdown.converter;

import com.talkweb.ai.converter.util.markdown.ConversionContext;
import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;

import java.util.Set;

/**
 * Interface for converting HTML elements to Markdown
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public interface ElementConverter {
    
    /**
     * Gets the HTML tag names this converter supports
     * 
     * @return set of supported tag names (lowercase)
     */
    Set<String> getSupportedTags();
    
    /**
     * Checks if this converter can handle the given element
     * 
     * @param element the HTML element
     * @param context the conversion context
     * @return true if this converter can handle the element
     */
    default boolean canConvert(Element element, ConversionContext context) {
        return element != null && getSupportedTags().contains(element.tagName().toLowerCase());
    }
    
    /**
     * Converts the HTML element to Markdown
     * 
     * @param element the HTML element to convert
     * @param builder the markdown builder to append to
     * @param context the conversion context
     * @throws ConversionException if conversion fails
     */
    void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException;
    
    /**
     * Gets the priority of this converter (higher priority converters are tried first)
     * 
     * @return the priority value
     */
    default int getPriority() {
        return 0;
    }
    
    /**
     * Checks if this converter should process child elements
     * 
     * @param element the HTML element
     * @param context the conversion context
     * @return true if children should be processed
     */
    default boolean shouldProcessChildren(Element element, ConversionContext context) {
        return true;
    }
    
    /**
     * Called before converting the element (pre-processing hook)
     * 
     * @param element the HTML element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    default void beforeConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        // Default implementation does nothing
    }
    
    /**
     * Called after converting the element (post-processing hook)
     * 
     * @param element the HTML element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    default void afterConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        // Default implementation does nothing
    }
    
    /**
     * Validates the element before conversion
     * 
     * @param element the HTML element
     * @param context the conversion context
     * @throws ConversionException if validation fails
     */
    default void validate(Element element, ConversionContext context) throws ConversionException {
        if (element == null) {
            throw new ConversionException("Element cannot be null");
        }
        if (context == null) {
            throw new ConversionException("Context cannot be null");
        }
    }
    
    /**
     * Exception thrown during element conversion
     */
    class ConversionException extends Exception {
        
        public ConversionException(String message) {
            super(message);
        }
        
        public ConversionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
