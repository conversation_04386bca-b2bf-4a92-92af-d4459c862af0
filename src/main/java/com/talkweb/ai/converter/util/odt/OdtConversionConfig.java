package com.talkweb.ai.converter.util.odt;

/**
 * Configuration class for ODT to Markdown conversion
 * 
 * This class provides various configuration options to control the behavior
 * of ODT document conversion, including parsing modes, formatting options,
 * and error handling strategies.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class OdtConversionConfig {

    private OdtConversionMode mode = OdtConversionMode.LOOSE;
    private boolean preserveFormatting = true;
    private boolean convertTables = true;
    private boolean convertImages = false; // ODT images require special handling, disabled by default
    private boolean convertLists = true;
    private boolean preserveHeadingLevels = true;
    private boolean convertHyperlinks = true;
    private boolean includeMetadata = false;
    private boolean preservePageBreaks = false;
    private boolean convertFootnotes = false; // Complex feature, disabled by default
    private boolean useAdvancedParsing = true; // Use enhanced parser by default
    private boolean normalizeWhitespace = true; // Normalize whitespace by default
    private int maxDocumentSize = 100 * 1024 * 1024; // 100MB limit
    private String defaultEncoding = "UTF-8";

    /**
     * Default constructor with standard settings
     */
    public OdtConversionConfig() {
        // Use default values
    }

    /**
     * Constructor with conversion mode
     */
    public OdtConversionConfig(OdtConversionMode mode) {
        this.mode = mode;
    }

    /**
     * Gets the conversion mode
     */
    public OdtConversionMode getMode() {
        return mode;
    }

    /**
     * Sets the conversion mode
     */
    public void setMode(OdtConversionMode mode) {
        this.mode = mode != null ? mode : OdtConversionMode.LOOSE;
    }

    /**
     * Checks if formatting should be preserved
     */
    public boolean isPreserveFormatting() {
        return preserveFormatting;
    }

    /**
     * Sets whether to preserve formatting
     */
    public void setPreserveFormatting(boolean preserveFormatting) {
        this.preserveFormatting = preserveFormatting;
    }

    /**
     * Checks if tables should be converted
     */
    public boolean isConvertTables() {
        return convertTables;
    }

    /**
     * Sets whether to convert tables
     */
    public void setConvertTables(boolean convertTables) {
        this.convertTables = convertTables;
    }

    /**
     * Checks if images should be converted
     */
    public boolean isConvertImages() {
        return convertImages;
    }

    /**
     * Sets whether to convert images
     */
    public void setConvertImages(boolean convertImages) {
        this.convertImages = convertImages;
    }

    /**
     * Checks if lists should be converted
     */
    public boolean isConvertLists() {
        return convertLists;
    }

    /**
     * Sets whether to convert lists
     */
    public void setConvertLists(boolean convertLists) {
        this.convertLists = convertLists;
    }

    /**
     * Checks if heading levels should be preserved
     */
    public boolean isPreserveHeadingLevels() {
        return preserveHeadingLevels;
    }

    /**
     * Sets whether to preserve heading levels
     */
    public void setPreserveHeadingLevels(boolean preserveHeadingLevels) {
        this.preserveHeadingLevels = preserveHeadingLevels;
    }

    /**
     * Checks if hyperlinks should be converted
     */
    public boolean isConvertHyperlinks() {
        return convertHyperlinks;
    }

    /**
     * Sets whether to convert hyperlinks
     */
    public void setConvertHyperlinks(boolean convertHyperlinks) {
        this.convertHyperlinks = convertHyperlinks;
    }

    /**
     * Checks if metadata should be included
     */
    public boolean isIncludeMetadata() {
        return includeMetadata;
    }

    /**
     * Sets whether to include metadata
     */
    public void setIncludeMetadata(boolean includeMetadata) {
        this.includeMetadata = includeMetadata;
    }

    /**
     * Checks if page breaks should be preserved
     */
    public boolean isPreservePageBreaks() {
        return preservePageBreaks;
    }

    /**
     * Sets whether to preserve page breaks
     */
    public void setPreservePageBreaks(boolean preservePageBreaks) {
        this.preservePageBreaks = preservePageBreaks;
    }

    /**
     * Checks if footnotes should be converted
     */
    public boolean isConvertFootnotes() {
        return convertFootnotes;
    }

    /**
     * Sets whether to convert footnotes
     */
    public void setConvertFootnotes(boolean convertFootnotes) {
        this.convertFootnotes = convertFootnotes;
    }

    /**
     * Checks if advanced parsing should be used
     */
    public boolean isUseAdvancedParsing() {
        return useAdvancedParsing;
    }

    /**
     * Sets whether to use advanced parsing
     */
    public void setUseAdvancedParsing(boolean useAdvancedParsing) {
        this.useAdvancedParsing = useAdvancedParsing;
    }

    /**
     * Checks if whitespace should be normalized
     */
    public boolean isNormalizeWhitespace() {
        return normalizeWhitespace;
    }

    /**
     * Sets whether to normalize whitespace
     */
    public void setNormalizeWhitespace(boolean normalizeWhitespace) {
        this.normalizeWhitespace = normalizeWhitespace;
    }

    /**
     * Gets the maximum document size limit
     */
    public int getMaxDocumentSize() {
        return maxDocumentSize;
    }

    /**
     * Sets the maximum document size limit
     */
    public void setMaxDocumentSize(int maxDocumentSize) {
        this.maxDocumentSize = Math.max(1024, maxDocumentSize); // Minimum 1KB
    }

    /**
     * Gets the default encoding
     */
    public String getDefaultEncoding() {
        return defaultEncoding;
    }

    /**
     * Sets the default encoding
     */
    public void setDefaultEncoding(String defaultEncoding) {
        this.defaultEncoding = defaultEncoding != null ? defaultEncoding : "UTF-8";
    }

    /**
     * Creates a builder for fluent configuration
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for fluent configuration
     */
    public static class Builder {
        private final OdtConversionConfig config = new OdtConversionConfig();

        public Builder mode(OdtConversionMode mode) {
            config.setMode(mode);
            return this;
        }

        public Builder preserveFormatting(boolean preserveFormatting) {
            config.setPreserveFormatting(preserveFormatting);
            return this;
        }

        public Builder convertTables(boolean convertTables) {
            config.setConvertTables(convertTables);
            return this;
        }

        public Builder convertImages(boolean convertImages) {
            config.setConvertImages(convertImages);
            return this;
        }

        public Builder convertLists(boolean convertLists) {
            config.setConvertLists(convertLists);
            return this;
        }

        public Builder preserveHeadingLevels(boolean preserveHeadingLevels) {
            config.setPreserveHeadingLevels(preserveHeadingLevels);
            return this;
        }

        public Builder convertHyperlinks(boolean convertHyperlinks) {
            config.setConvertHyperlinks(convertHyperlinks);
            return this;
        }

        public Builder includeMetadata(boolean includeMetadata) {
            config.setIncludeMetadata(includeMetadata);
            return this;
        }

        public Builder preservePageBreaks(boolean preservePageBreaks) {
            config.setPreservePageBreaks(preservePageBreaks);
            return this;
        }

        public Builder convertFootnotes(boolean convertFootnotes) {
            config.setConvertFootnotes(convertFootnotes);
            return this;
        }

        public Builder useAdvancedParsing(boolean useAdvancedParsing) {
            config.setUseAdvancedParsing(useAdvancedParsing);
            return this;
        }

        public Builder maxDocumentSize(int maxDocumentSize) {
            config.setMaxDocumentSize(maxDocumentSize);
            return this;
        }

        public Builder defaultEncoding(String defaultEncoding) {
            config.setDefaultEncoding(defaultEncoding);
            return this;
        }

        public OdtConversionConfig build() {
            return config;
        }
    }

    @Override
    public String toString() {
        return "OdtConversionConfig{" +
                "mode=" + mode +
                ", preserveFormatting=" + preserveFormatting +
                ", convertTables=" + convertTables +
                ", convertImages=" + convertImages +
                ", convertLists=" + convertLists +
                ", preserveHeadingLevels=" + preserveHeadingLevels +
                ", convertHyperlinks=" + convertHyperlinks +
                ", includeMetadata=" + includeMetadata +
                ", preservePageBreaks=" + preservePageBreaks +
                ", convertFootnotes=" + convertFootnotes +
                ", useAdvancedParsing=" + useAdvancedParsing +
                ", maxDocumentSize=" + maxDocumentSize +
                ", defaultEncoding='" + defaultEncoding + '\'' +
                '}';
    }
}
