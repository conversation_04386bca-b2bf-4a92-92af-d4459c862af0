package com.talkweb.ai.converter.util.ppt.converter;

import com.talkweb.ai.converter.util.ppt.PptConversionContext;
import org.apache.poi.sl.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Converter for PowerPoint tables
 * 
 * Handles the conversion of table shapes to Markdown table format,
 * preserving table structure and cell content while applying enhanced
 * processing patterns similar to the HTML table converter.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class PptTableConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(PptTableConverter.class);
    
    /**
     * Converts a table shape to Markdown format
     */
    public void convertTable(TableShape<?,?> tableShape, StringBuilder markdown, 
                           PptConversionContext context) {
        
        if (tableShape == null) {
            context.addWarning("Encountered null table shape");
            return;
        }
        
        if (!context.getConfig().isExtractTables()) {
            logger.debug("Table extraction disabled, skipping table");
            return;
        }
        
        try {
            logger.debug("Converting table with {} rows and {} columns", 
                        tableShape.getNumberOfRows(), tableShape.getNumberOfColumns());
            
            // Extract table data
            List<List<String>> tableData = extractTableData(tableShape, context);
            
            if (tableData.isEmpty()) {
                context.addWarning("Table contains no data");
                return;
            }
            
            // Determine maximum number of columns
            int maxCols = tableData.stream().mapToInt(List::size).max().orElse(0);
            if (maxCols == 0) {
                context.addWarning("Table has no columns");
                return;
            }
            
            // Normalize table data (ensure all rows have same number of columns)
            normalizeTableData(tableData, maxCols);
            
            // Write enhanced Markdown table
            writeEnhancedMarkdownTable(tableData, maxCols, markdown, context);
            
            context.incrementExtractedTablesCount();
            
        } catch (Exception e) {
            String error = "Failed to convert table: " + e.getMessage();
            context.addError(error);
            
            if (context.getConfig().isStrictMode()) {
                throw new RuntimeException(error, e);
            }
            
            // Add error placeholder in loose mode
            markdown.append("*[Table conversion failed: ").append(e.getMessage()).append("]*\n\n");
            logger.debug("Table conversion error", e);
        }
    }
    
    /**
     * Extracts data from the table shape
     */
    private List<List<String>> extractTableData(TableShape<?,?> tableShape, 
                                               PptConversionContext context) {
        
        List<List<String>> tableData = new ArrayList<>();
        
        try {
            int numRows = tableShape.getNumberOfRows();
            int numCols = tableShape.getNumberOfColumns();
            
            for (int row = 0; row < numRows; row++) {
                List<String> rowData = new ArrayList<>();
                
                for (int col = 0; col < numCols; col++) {
                    TableCell<?,?> cell = tableShape.getCell(row, col);
                    String cellContent = extractCellContent(cell, context);
                    rowData.add(cellContent);
                }
                
                tableData.add(rowData);
            }
            
        } catch (Exception e) {
            logger.debug("Error extracting table data", e);
            context.addWarning("Failed to extract complete table data: " + e.getMessage());
        }
        
        return tableData;
    }
    
    /**
     * Extracts content from a table cell
     */
    private String extractCellContent(TableCell<?,?> cell, PptConversionContext context) {
        if (cell == null) {
            return "";
        }
        
        try {
            StringBuilder content = new StringBuilder();
            
            // Extract text from cell paragraphs
            List<? extends TextParagraph<?,?,?>> paragraphs = cell.getTextParagraphs();
            if (paragraphs != null) {
                for (TextParagraph<?,?,?> paragraph : paragraphs) {
                    String paragraphText = extractParagraphText(paragraph, context);
                    if (paragraphText != null && !paragraphText.trim().isEmpty()) {
                        if (content.length() > 0) {
                            content.append(" ");
                        }
                        content.append(paragraphText.trim());
                    }
                }
            }
            
            String result = content.toString().trim();
            
            // Clean and escape the content for Markdown
            result = cleanCellContent(result, context);
            
            return result;
            
        } catch (Exception e) {
            logger.debug("Failed to extract cell content", e);
            return "";
        }
    }
    
    /**
     * Extracts text from a paragraph with formatting
     */
    private String extractParagraphText(TextParagraph<?,?,?> paragraph, 
                                       PptConversionContext context) {
        
        if (paragraph == null) {
            return "";
        }
        
        try {
            StringBuilder text = new StringBuilder();
            
            List<? extends TextRun> runs = paragraph.getTextRuns();
            if (runs != null) {
                for (TextRun run : runs) {
                    String runText = run.getRawText();
                    if (runText != null && !runText.trim().isEmpty()) {
                        
                        // Apply formatting if preserve formatting is enabled
                        if (context.getConfig().isPreserveFormatting()) {
                            runText = applyTextFormatting(runText, run);
                        }
                        
                        text.append(runText);
                    }
                }
            }
            
            return text.toString();
            
        } catch (Exception e) {
            logger.debug("Failed to extract paragraph text", e);
            return "";
        }
    }
    
    /**
     * Applies text formatting (bold, italic) to text runs
     */
    private String applyTextFormatting(String text, TextRun run) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }
        
        try {
            String formatted = text;
            
            // Apply bold and italic formatting
            if (run.isBold() && run.isItalic()) {
                formatted = "***" + formatted + "***";
            } else if (run.isBold()) {
                formatted = "**" + formatted + "**";
            } else if (run.isItalic()) {
                formatted = "*" + formatted + "*";
            }
            
            // Could add more formatting like underline, strikethrough, etc.
            // if (run.isUnderlined()) {
            //     formatted = "<u>" + formatted + "</u>";
            // }
            
            return formatted;
            
        } catch (Exception e) {
            logger.debug("Failed to apply text formatting", e);
            return text;
        }
    }
    
    /**
     * Cleans and escapes cell content for Markdown
     */
    private String cleanCellContent(String content, PptConversionContext context) {
        if (content == null) {
            return "";
        }
        
        String cleaned = content;
        
        // Normalize whitespace if enabled
        if (context.getConfig().isNormalizeWhitespace()) {
            cleaned = cleaned.replaceAll("\\s+", " ");
        }
        
        // Escape pipe characters for Markdown table compatibility
        cleaned = cleaned.replace("|", "\\|");
        
        // Escape other problematic characters
        cleaned = cleaned.replace("\n", " ")
                        .replace("\r", " ")
                        .replace("\t", " ");
        
        // Remove excessive whitespace
        cleaned = cleaned.trim();
        
        return cleaned;
    }
    
    /**
     * Normalizes table data to ensure all rows have the same number of columns
     */
    private void normalizeTableData(List<List<String>> tableData, int maxCols) {
        for (List<String> row : tableData) {
            while (row.size() < maxCols) {
                row.add("");
            }
            // Trim excess columns if any
            while (row.size() > maxCols) {
                row.remove(row.size() - 1);
            }
        }
    }
    
    /**
     * Writes enhanced Markdown table with improved formatting
     */
    private void writeEnhancedMarkdownTable(List<List<String>> tableData, int maxCols, 
                                          StringBuilder markdown, PptConversionContext context) {
        
        if (tableData.isEmpty() || maxCols == 0) {
            return;
        }
        
        markdown.append("\n");
        
        // Determine column widths for better formatting
        int[] columnWidths = calculateColumnWidths(tableData, maxCols);
        
        // Write table header (first row)
        List<String> headerRow = tableData.get(0);
        writeTableRow(headerRow, columnWidths, markdown);
        
        // Write separator row
        writeTableSeparator(columnWidths, markdown);
        
        // Write data rows
        for (int i = 1; i < tableData.size(); i++) {
            List<String> row = tableData.get(i);
            writeTableRow(row, columnWidths, markdown);
        }
        
        markdown.append("\n");
    }
    
    /**
     * Calculates optimal column widths for table formatting
     */
    private int[] calculateColumnWidths(List<List<String>> tableData, int maxCols) {
        int[] widths = new int[maxCols];
        
        // Initialize with minimum widths
        for (int i = 0; i < maxCols; i++) {
            widths[i] = 3; // Minimum width for "---"
        }
        
        // Calculate maximum content width for each column
        for (List<String> row : tableData) {
            for (int col = 0; col < Math.min(row.size(), maxCols); col++) {
                String cell = row.get(col);
                if (cell != null) {
                    widths[col] = Math.max(widths[col], cell.length());
                }
            }
        }
        
        // Cap maximum width to prevent overly wide tables
        for (int i = 0; i < maxCols; i++) {
            widths[i] = Math.min(widths[i], 50);
        }
        
        return widths;
    }
    
    /**
     * Writes a table row with proper formatting
     */
    private void writeTableRow(List<String> row, int[] columnWidths, StringBuilder markdown) {
        markdown.append("|");
        
        for (int col = 0; col < columnWidths.length; col++) {
            String cell = col < row.size() ? row.get(col) : "";
            if (cell == null) cell = "";
            
            // Pad cell content to column width
            String paddedCell = String.format(" %-" + columnWidths[col] + "s ", cell);
            markdown.append(paddedCell).append("|");
        }
        
        markdown.append("\n");
    }
    
    /**
     * Writes the table separator row
     */
    private void writeTableSeparator(int[] columnWidths, StringBuilder markdown) {
        markdown.append("|");
        
        for (int width : columnWidths) {
            markdown.append(" ");
            for (int i = 0; i < width; i++) {
                markdown.append("-");
            }
            markdown.append(" |");
        }
        
        markdown.append("\n");
    }
    
    /**
     * Checks if a table has meaningful content
     */
    public boolean hasContent(TableShape<?,?> tableShape) {
        if (tableShape == null) {
            return false;
        }
        
        try {
            int numRows = tableShape.getNumberOfRows();
            int numCols = tableShape.getNumberOfColumns();
            
            if (numRows == 0 || numCols == 0) {
                return false;
            }
            
            // Check if any cell has content
            for (int row = 0; row < numRows; row++) {
                for (int col = 0; col < numCols; col++) {
                    TableCell<?,?> cell = tableShape.getCell(row, col);
                    if (cell != null) {
                        String content = extractCellContent(cell, 
                            new PptConversionContext(null, null));
                        if (content != null && !content.trim().isEmpty()) {
                            return true;
                        }
                    }
                }
            }
            
            return false;
            
        } catch (Exception e) {
            logger.debug("Error checking table content", e);
            return false;
        }
    }
    
    /**
     * Gets table dimensions as a string for debugging
     */
    public String getTableDimensions(TableShape<?,?> tableShape) {
        if (tableShape == null) {
            return "null";
        }
        
        try {
            return tableShape.getNumberOfRows() + "x" + tableShape.getNumberOfColumns();
        } catch (Exception e) {
            return "unknown";
        }
    }
    
    /**
     * Validates table structure
     */
    private boolean isValidTable(TableShape<?,?> tableShape) {
        if (tableShape == null) {
            return false;
        }
        
        try {
            int rows = tableShape.getNumberOfRows();
            int cols = tableShape.getNumberOfColumns();
            
            return rows > 0 && cols > 0 && rows <= 1000 && cols <= 100; // Reasonable limits
            
        } catch (Exception e) {
            logger.debug("Error validating table", e);
            return false;
        }
    }
}
