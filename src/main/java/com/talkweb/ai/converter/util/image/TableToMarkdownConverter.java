package com.talkweb.ai.converter.util.image;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 表格到Markdown转换器
 * 
 * 优化表格到Markdown的转换逻辑，支持复杂表格结构、
 * 合并单元格和表格格式化。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class TableToMarkdownConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(TableToMarkdownConverter.class);
    
    // Markdown特殊字符转义模式
    private static final Pattern PIPE_PATTERN = Pattern.compile("\\|");
    private static final Pattern NEWLINE_PATTERN = Pattern.compile("\\r?\\n");
    
    /**
     * 转换配置
     */
    public static class ConversionConfig {
        private boolean enableCellMerging = true;
        private boolean enableFormatting = true;
        private boolean enableAlignment = true;
        private boolean escapeSpecialChars = true;
        private int maxCellWidth = 50;
        private String emptyCellPlaceholder = " ";
        private boolean includeTableMetadata = false;
        private boolean enableSmartFormatting = true;
        
        // Getters and setters
        public boolean isEnableCellMerging() { return enableCellMerging; }
        public void setEnableCellMerging(boolean enableCellMerging) { this.enableCellMerging = enableCellMerging; }
        
        public boolean isEnableFormatting() { return enableFormatting; }
        public void setEnableFormatting(boolean enableFormatting) { this.enableFormatting = enableFormatting; }
        
        public boolean isEnableAlignment() { return enableAlignment; }
        public void setEnableAlignment(boolean enableAlignment) { this.enableAlignment = enableAlignment; }
        
        public boolean isEscapeSpecialChars() { return escapeSpecialChars; }
        public void setEscapeSpecialChars(boolean escapeSpecialChars) { this.escapeSpecialChars = escapeSpecialChars; }
        
        public int getMaxCellWidth() { return maxCellWidth; }
        public void setMaxCellWidth(int maxCellWidth) { this.maxCellWidth = maxCellWidth; }
        
        public String getEmptyCellPlaceholder() { return emptyCellPlaceholder; }
        public void setEmptyCellPlaceholder(String emptyCellPlaceholder) { this.emptyCellPlaceholder = emptyCellPlaceholder; }
        
        public boolean isIncludeTableMetadata() { return includeTableMetadata; }
        public void setIncludeTableMetadata(boolean includeTableMetadata) { this.includeTableMetadata = includeTableMetadata; }
        
        public boolean isEnableSmartFormatting() { return enableSmartFormatting; }
        public void setEnableSmartFormatting(boolean enableSmartFormatting) { this.enableSmartFormatting = enableSmartFormatting; }
    }
    
    /**
     * 转换结果
     */
    public static class ConversionResult {
        private final String markdown;
        private final boolean success;
        private final String errorMessage;
        private final Map<String, Object> metadata;
        
        public ConversionResult(String markdown) {
            this.markdown = markdown != null ? markdown : "";
            this.success = true;
            this.errorMessage = null;
            this.metadata = new HashMap<>();
        }
        
        public ConversionResult(String errorMessage, boolean isError) {
            this.markdown = "";
            this.success = false;
            this.errorMessage = errorMessage;
            this.metadata = new HashMap<>();
        }
        
        // Getters
        public String getMarkdown() { return markdown; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
        
        public void addMetadata(String key, Object value) {
            this.metadata.put(key, value);
        }
    }
    
    /**
     * 转换单个表格到Markdown
     * 
     * @param tableData 表格数据
     * @return 转换结果
     */
    public ConversionResult convertTable(TableExtractor.TableData tableData) {
        return convertTable(tableData, new ConversionConfig());
    }
    
    /**
     * 转换单个表格到Markdown（带配置）
     * 
     * @param tableData 表格数据
     * @param config 转换配置
     * @return 转换结果
     */
    public ConversionResult convertTable(TableExtractor.TableData tableData, ConversionConfig config) {
        if (tableData == null) {
            return new ConversionResult("Table data is null", true);
        }
        
        if (config == null) {
            config = new ConversionConfig();
        }
        
        try {
            logger.debug("Converting table {}x{} to Markdown", tableData.getRows(), tableData.getColumns());
            
            StringBuilder markdown = new StringBuilder();
            
            // 添加表格元数据（如果启用）
            if (config.isIncludeTableMetadata()) {
                addTableMetadata(markdown, tableData);
            }
            
            // 获取表格数据
            String[][] data = tableData.toStringArray();
            
            // 预处理数据
            data = preprocessTableData(data, config);
            
            // 生成Markdown表格
            generateMarkdownTable(markdown, data, config);
            
            ConversionResult result = new ConversionResult(markdown.toString());
            result.addMetadata("originalRows", tableData.getRows());
            result.addMetadata("originalColumns", tableData.getColumns());
            result.addMetadata("processedRows", data.length);
            result.addMetadata("processedColumns", data.length > 0 ? data[0].length : 0);
            result.addMetadata("confidence", tableData.getConfidence());
            
            logger.debug("Table conversion completed successfully");
            return result;
            
        } catch (Exception e) {
            logger.error("Table conversion failed", e);
            return new ConversionResult("Table conversion failed: " + e.getMessage(), true);
        }
    }
    
    /**
     * 转换多个表格到Markdown
     * 
     * @param tables 表格列表
     * @return 转换结果
     */
    public ConversionResult convertTables(List<TableExtractor.TableData> tables) {
        return convertTables(tables, new ConversionConfig());
    }
    
    /**
     * 转换多个表格到Markdown（带配置）
     * 
     * @param tables 表格列表
     * @param config 转换配置
     * @return 转换结果
     */
    public ConversionResult convertTables(List<TableExtractor.TableData> tables, ConversionConfig config) {
        if (tables == null || tables.isEmpty()) {
            return new ConversionResult("No tables to convert", true);
        }
        
        if (config == null) {
            config = new ConversionConfig();
        }
        
        try {
            logger.debug("Converting {} tables to Markdown", tables.size());
            
            StringBuilder markdown = new StringBuilder();
            int successCount = 0;
            
            for (int i = 0; i < tables.size(); i++) {
                TableExtractor.TableData table = tables.get(i);
                
                // 添加表格标题
                markdown.append("## Table ").append(i + 1).append("\n\n");
                
                ConversionResult tableResult = convertTable(table, config);
                if (tableResult.isSuccess()) {
                    markdown.append(tableResult.getMarkdown());
                    successCount++;
                } else {
                    markdown.append("*Failed to convert table: ").append(tableResult.getErrorMessage()).append("*\n");
                }
                
                markdown.append("\n");
            }
            
            ConversionResult result = new ConversionResult(markdown.toString());
            result.addMetadata("totalTables", tables.size());
            result.addMetadata("successfulConversions", successCount);
            result.addMetadata("conversionRate", (double) successCount / tables.size());
            
            logger.debug("Multiple table conversion completed. {} of {} tables converted successfully", 
                        successCount, tables.size());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Multiple table conversion failed", e);
            return new ConversionResult("Multiple table conversion failed: " + e.getMessage(), true);
        }
    }
    
    /**
     * 添加表格元数据
     */
    private void addTableMetadata(StringBuilder markdown, TableExtractor.TableData tableData) {
        markdown.append("<!-- Table Metadata:\n");
        markdown.append("  Size: ").append(tableData.getRows()).append("x").append(tableData.getColumns()).append("\n");
        markdown.append("  Confidence: ").append(String.format("%.3f", tableData.getConfidence())).append("\n");
        markdown.append("  Bounds: ").append(tableData.getBounds().toString()).append("\n");
        markdown.append("-->\n\n");
    }
    
    /**
     * 预处理表格数据
     */
    private String[][] preprocessTableData(String[][] data, ConversionConfig config) {
        if (data == null || data.length == 0) {
            return new String[0][0];
        }
        
        int rows = data.length;
        int cols = data[0].length;
        String[][] processed = new String[rows][cols];
        
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                String cellContent = data[i][j];
                
                // 处理空单元格
                if (cellContent == null || cellContent.trim().isEmpty()) {
                    processed[i][j] = config.getEmptyCellPlaceholder();
                } else {
                    // 清理和格式化单元格内容
                    processed[i][j] = cleanCellContent(cellContent, config);
                }
            }
        }
        
        return processed;
    }
    
    /**
     * 清理单元格内容
     */
    private String cleanCellContent(String content, ConversionConfig config) {
        if (content == null) {
            return config.getEmptyCellPlaceholder();
        }
        
        String cleaned = content;
        
        // 转义特殊字符
        if (config.isEscapeSpecialChars()) {
            cleaned = escapeMarkdownChars(cleaned);
        }
        
        // 移除换行符
        cleaned = NEWLINE_PATTERN.matcher(cleaned).replaceAll(" ");
        
        // 限制单元格宽度
        if (cleaned.length() > config.getMaxCellWidth()) {
            cleaned = cleaned.substring(0, config.getMaxCellWidth() - 3) + "...";
        }
        
        // 智能格式化
        if (config.isEnableSmartFormatting()) {
            cleaned = applySmartFormatting(cleaned);
        }
        
        return cleaned.trim();
    }
    
    /**
     * 转义Markdown特殊字符
     */
    private String escapeMarkdownChars(String text) {
        // 转义管道符
        text = PIPE_PATTERN.matcher(text).replaceAll("\\\\|");
        
        // 转义其他Markdown特殊字符
        text = text.replace("*", "\\*")
                  .replace("_", "\\_")
                  .replace("`", "\\`")
                  .replace("#", "\\#")
                  .replace("[", "\\[")
                  .replace("]", "\\]");
        
        return text;
    }
    
    /**
     * 应用智能格式化
     */
    private String applySmartFormatting(String text) {
        // 数字格式化
        if (text.matches("^\\d+(\\.\\d+)?$")) {
            try {
                double number = Double.parseDouble(text);
                if (number == (long) number) {
                    return String.valueOf((long) number);
                } else {
                    return String.format("%.2f", number);
                }
            } catch (NumberFormatException e) {
                // 忽略格式化错误
            }
        }
        
        // 移除多余空格
        text = text.replaceAll("\\s+", " ");
        
        return text;
    }

    /**
     * 生成Markdown表格
     */
    private void generateMarkdownTable(StringBuilder markdown, String[][] data, ConversionConfig config) {
        if (data.length == 0) {
            markdown.append("*Empty table*\n");
            return;
        }

        int rows = data.length;
        int cols = data[0].length;

        // 计算每列的最大宽度（用于对齐）
        int[] columnWidths = calculateColumnWidths(data, config);

        // 生成表头
        generateTableHeader(markdown, data[0], columnWidths, config);

        // 生成分隔行
        generateSeparatorRow(markdown, columnWidths, config);

        // 生成数据行
        for (int i = 1; i < rows; i++) {
            generateTableRow(markdown, data[i], columnWidths, config);
        }
    }

    /**
     * 计算列宽度
     */
    private int[] calculateColumnWidths(String[][] data, ConversionConfig config) {
        if (data.length == 0) {
            return new int[0];
        }

        int cols = data[0].length;
        int[] widths = new int[cols];

        // 计算每列的最大宽度
        for (int col = 0; col < cols; col++) {
            int maxWidth = 3; // 最小宽度

            for (int row = 0; row < data.length; row++) {
                if (col < data[row].length) {
                    int cellWidth = data[row][col].length();
                    maxWidth = Math.max(maxWidth, cellWidth);
                }
            }

            // 限制最大宽度
            widths[col] = Math.min(maxWidth, config.getMaxCellWidth());
        }

        return widths;
    }

    /**
     * 生成表头
     */
    private void generateTableHeader(StringBuilder markdown, String[] headerRow,
                                   int[] columnWidths, ConversionConfig config) {
        markdown.append("|");

        for (int i = 0; i < headerRow.length; i++) {
            String cell = headerRow[i];

            if (config.isEnableAlignment()) {
                cell = padCell(cell, columnWidths[i]);
            }

            markdown.append(" ").append(cell).append(" |");
        }

        markdown.append("\n");
    }

    /**
     * 生成分隔行
     */
    private void generateSeparatorRow(StringBuilder markdown, int[] columnWidths, ConversionConfig config) {
        markdown.append("|");

        for (int width : columnWidths) {
            markdown.append(" ");

            if (config.isEnableAlignment()) {
                // 生成对齐的分隔符
                for (int i = 0; i < width; i++) {
                    markdown.append("-");
                }
            } else {
                markdown.append("---");
            }

            markdown.append(" |");
        }

        markdown.append("\n");
    }

    /**
     * 生成表格行
     */
    private void generateTableRow(StringBuilder markdown, String[] row,
                                int[] columnWidths, ConversionConfig config) {
        markdown.append("|");

        for (int i = 0; i < row.length; i++) {
            String cell = row[i];

            if (config.isEnableAlignment()) {
                cell = padCell(cell, columnWidths[i]);
            }

            markdown.append(" ").append(cell).append(" |");
        }

        markdown.append("\n");
    }

    /**
     * 填充单元格以对齐
     */
    private String padCell(String cell, int width) {
        if (cell.length() >= width) {
            return cell;
        }

        int padding = width - cell.length();
        StringBuilder padded = new StringBuilder(cell);

        // 右填充空格
        for (int i = 0; i < padding; i++) {
            padded.append(" ");
        }

        return padded.toString();
    }

    /**
     * 获取转换调试信息
     */
    public String getDebugInfo(List<TableExtractor.TableData> tables, ConversionConfig config) {
        StringBuilder debug = new StringBuilder();
        debug.append("=== Table to Markdown Conversion Debug Info ===\n");
        debug.append("Tables: ").append(tables.size()).append("\n");
        debug.append("Config: ").append(configToString(config)).append("\n");

        try {
            ConversionResult result = convertTables(tables, config);
            debug.append("Conversion Result: ").append(result.isSuccess() ? "SUCCESS" : "FAILED").append("\n");
            debug.append("Markdown Length: ").append(result.getMarkdown().length()).append(" characters\n");
            debug.append("Metadata: ").append(result.getMetadata()).append("\n");

            if (!result.isSuccess()) {
                debug.append("Error: ").append(result.getErrorMessage()).append("\n");
            }

        } catch (Exception e) {
            debug.append("Error during conversion: ").append(e.getMessage()).append("\n");
        }

        debug.append("=== End Debug Info ===\n");
        return debug.toString();
    }

    /**
     * 配置转字符串
     */
    private String configToString(ConversionConfig config) {
        return String.format("formatting=%s, alignment=%s, escaping=%s, maxWidth=%d",
            config.isEnableFormatting(),
            config.isEnableAlignment(),
            config.isEscapeSpecialChars(),
            config.getMaxCellWidth());
    }

    /**
     * 验证表格数据
     */
    public boolean validateTableData(TableExtractor.TableData tableData) {
        if (tableData == null) {
            return false;
        }

        if (tableData.getRows() <= 0 || tableData.getColumns() <= 0) {
            return false;
        }

        // 检查数据完整性
        List<List<TableExtractor.CellData>> cells = tableData.getCells();
        if (cells.size() != tableData.getRows()) {
            return false;
        }

        for (List<TableExtractor.CellData> row : cells) {
            if (row.size() != tableData.getColumns()) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取表格统计信息
     */
    public Map<String, Object> getTableStatistics(List<TableExtractor.TableData> tables) {
        Map<String, Object> stats = new HashMap<>();

        if (tables == null || tables.isEmpty()) {
            stats.put("totalTables", 0);
            return stats;
        }

        int totalTables = tables.size();
        int totalCells = 0;
        int totalRows = 0;
        int totalColumns = 0;
        double avgConfidence = 0.0;
        int emptyTables = 0;

        for (TableExtractor.TableData table : tables) {
            totalRows += table.getRows();
            totalColumns += table.getColumns();
            totalCells += table.getRows() * table.getColumns();
            avgConfidence += table.getConfidence();

            if (table.getRows() == 0 || table.getColumns() == 0) {
                emptyTables++;
            }
        }

        stats.put("totalTables", totalTables);
        stats.put("totalCells", totalCells);
        stats.put("totalRows", totalRows);
        stats.put("totalColumns", totalColumns);
        stats.put("avgConfidence", totalTables > 0 ? avgConfidence / totalTables : 0.0);
        stats.put("emptyTables", emptyTables);
        stats.put("avgRowsPerTable", totalTables > 0 ? (double) totalRows / totalTables : 0.0);
        stats.put("avgColumnsPerTable", totalTables > 0 ? (double) totalColumns / totalTables : 0.0);

        return stats;
    }
}
