package com.talkweb.ai.converter.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * 大文件处理优化工具类
 * 
 * 提供大图像文件的优化处理功能，包括：
 * - 图像分割处理
 * - 内存优化
 * - 分块处理
 * - 临时文件管理
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class LargeFileProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(LargeFileProcessor.class);
    
    // 大文件阈值配置
    private static final long LARGE_FILE_SIZE_BYTES = 50 * 1024 * 1024; // 50MB
    private static final int LARGE_IMAGE_PIXELS = 4000 * 3000; // 12MP
    private static final int MAX_CHUNK_SIZE = 2048; // 最大块尺寸
    
    /**
     * 检查是否为大文件
     */
    public boolean isLargeFile(File file) {
        try {
            return Files.size(file.toPath()) > LARGE_FILE_SIZE_BYTES;
        } catch (IOException e) {
            logger.warn("Failed to check file size for: {}", file.getName(), e);
            return false;
        }
    }
    
    /**
     * 检查是否为大图像
     */
    public boolean isLargeImage(BufferedImage image) {
        return image.getWidth() * image.getHeight() > LARGE_IMAGE_PIXELS;
    }
    
    /**
     * 处理大图像文件
     * 
     * @param imageFile 图像文件
     * @param options 处理选项
     * @return 处理结果
     */
    public LargeFileProcessingResult processLargeImageFile(File imageFile, LargeFileProcessingOptions options) {
        logger.info("Processing large image file: {} (size: {} bytes)", 
                   imageFile.getName(), imageFile.length());
        
        try {
            BufferedImage image = ImageIO.read(imageFile);
            if (image == null) {
                throw new IOException("Failed to read image file: " + imageFile.getName());
            }
            
            return processLargeImage(image, imageFile.getName(), options);
            
        } catch (IOException e) {
            logger.error("Failed to process large image file: {}", imageFile.getName(), e);
            return LargeFileProcessingResult.failure("Failed to read image: " + e.getMessage());
        }
    }
    
    /**
     * 处理大图像
     * 
     * @param image 图像对象
     * @param imageName 图像名称
     * @param options 处理选项
     * @return 处理结果
     */
    public LargeFileProcessingResult processLargeImage(BufferedImage image, String imageName, LargeFileProcessingOptions options) {
        if (!isLargeImage(image)) {
            // 不是大图像，直接返回原图
            return LargeFileProcessingResult.success(List.of(new ImageChunk(image, imageName, 0, 0, image.getWidth(), image.getHeight())));
        }
        
        logger.info("Processing large image: {} ({}x{})", imageName, image.getWidth(), image.getHeight());
        
        try {
            List<ImageChunk> chunks;
            
            switch (options.getProcessingStrategy()) {
                case SPLIT_HORIZONTAL:
                    chunks = splitImageHorizontally(image, imageName, options);
                    break;
                case SPLIT_VERTICAL:
                    chunks = splitImageVertically(image, imageName, options);
                    break;
                case SPLIT_GRID:
                    chunks = splitImageGrid(image, imageName, options);
                    break;
                case RESIZE_AND_SPLIT:
                    chunks = resizeAndSplit(image, imageName, options);
                    break;
                case ADAPTIVE:
                default:
                    chunks = adaptiveSplit(image, imageName, options);
                    break;
            }
            
            logger.info("Large image {} split into {} chunks", imageName, chunks.size());
            return LargeFileProcessingResult.success(chunks);
            
        } catch (Exception e) {
            logger.error("Failed to process large image: {}", imageName, e);
            return LargeFileProcessingResult.failure("Processing failed: " + e.getMessage());
        }
    }
    
    /**
     * 水平分割图像
     */
    private List<ImageChunk> splitImageHorizontally(BufferedImage image, String imageName, LargeFileProcessingOptions options) {
        List<ImageChunk> chunks = new ArrayList<>();
        int width = image.getWidth();
        int height = image.getHeight();
        int chunkHeight = Math.min(options.getMaxChunkSize(), height / options.getMinChunks());
        
        for (int y = 0; y < height; y += chunkHeight) {
            int actualHeight = Math.min(chunkHeight, height - y);
            BufferedImage chunk = image.getSubimage(0, y, width, actualHeight);
            chunks.add(new ImageChunk(chunk, imageName, 0, y, width, actualHeight));
        }
        
        return chunks;
    }
    
    /**
     * 垂直分割图像
     */
    private List<ImageChunk> splitImageVertically(BufferedImage image, String imageName, LargeFileProcessingOptions options) {
        List<ImageChunk> chunks = new ArrayList<>();
        int width = image.getWidth();
        int height = image.getHeight();
        int chunkWidth = Math.min(options.getMaxChunkSize(), width / options.getMinChunks());
        
        for (int x = 0; x < width; x += chunkWidth) {
            int actualWidth = Math.min(chunkWidth, width - x);
            BufferedImage chunk = image.getSubimage(x, 0, actualWidth, height);
            chunks.add(new ImageChunk(chunk, imageName, x, 0, actualWidth, height));
        }
        
        return chunks;
    }
    
    /**
     * 网格分割图像
     */
    private List<ImageChunk> splitImageGrid(BufferedImage image, String imageName, LargeFileProcessingOptions options) {
        List<ImageChunk> chunks = new ArrayList<>();
        int width = image.getWidth();
        int height = image.getHeight();
        int maxChunkSize = options.getMaxChunkSize();
        
        int chunksX = (int) Math.ceil((double) width / maxChunkSize);
        int chunksY = (int) Math.ceil((double) height / maxChunkSize);
        
        for (int y = 0; y < chunksY; y++) {
            for (int x = 0; x < chunksX; x++) {
                int startX = x * maxChunkSize;
                int startY = y * maxChunkSize;
                int chunkWidth = Math.min(maxChunkSize, width - startX);
                int chunkHeight = Math.min(maxChunkSize, height - startY);
                
                BufferedImage chunk = image.getSubimage(startX, startY, chunkWidth, chunkHeight);
                chunks.add(new ImageChunk(chunk, imageName, startX, startY, chunkWidth, chunkHeight));
            }
        }
        
        return chunks;
    }
    
    /**
     * 缩放后分割
     */
    private List<ImageChunk> resizeAndSplit(BufferedImage image, String imageName, LargeFileProcessingOptions options) {
        // 先缩放图像
        int maxSize = options.getMaxChunkSize() * 2; // 缩放后的最大尺寸
        BufferedImage resized = resizeImage(image, maxSize);
        
        // 如果缩放后仍然很大，继续分割
        if (isLargeImage(resized)) {
            return splitImageGrid(resized, imageName, options);
        } else {
            return List.of(new ImageChunk(resized, imageName, 0, 0, resized.getWidth(), resized.getHeight()));
        }
    }
    
    /**
     * 自适应分割
     */
    private List<ImageChunk> adaptiveSplit(BufferedImage image, String imageName, LargeFileProcessingOptions options) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        // 根据图像的宽高比选择分割策略
        double aspectRatio = (double) width / height;
        
        if (aspectRatio > 2.0) {
            // 宽图像，垂直分割
            return splitImageVertically(image, imageName, options);
        } else if (aspectRatio < 0.5) {
            // 高图像，水平分割
            return splitImageHorizontally(image, imageName, options);
        } else {
            // 接近正方形，网格分割
            return splitImageGrid(image, imageName, options);
        }
    }
    
    /**
     * 缩放图像
     */
    private BufferedImage resizeImage(BufferedImage original, int maxSize) {
        int width = original.getWidth();
        int height = original.getHeight();
        
        // 计算缩放比例
        double scale = Math.min((double) maxSize / width, (double) maxSize / height);
        if (scale >= 1.0) {
            return original; // 不需要缩放
        }
        
        int newWidth = (int) (width * scale);
        int newHeight = (int) (height * scale);
        
        BufferedImage resized = new BufferedImage(newWidth, newHeight, original.getType());
        Graphics2D g2d = resized.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(original, 0, 0, newWidth, newHeight, null);
        g2d.dispose();
        
        return resized;
    }
    
    /**
     * 图像块类
     */
    public static class ImageChunk {
        private final BufferedImage image;
        private final String originalName;
        private final int x, y, width, height;
        
        public ImageChunk(BufferedImage image, String originalName, int x, int y, int width, int height) {
            this.image = image;
            this.originalName = originalName;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
        
        public BufferedImage getImage() { return image; }
        public String getOriginalName() { return originalName; }
        public int getX() { return x; }
        public int getY() { return y; }
        public int getWidth() { return width; }
        public int getHeight() { return height; }
        
        public String getChunkName() {
            return String.format("%s_chunk_%d_%d_%dx%d", originalName, x, y, width, height);
        }
    }
    
    /**
     * 处理选项
     */
    public static class LargeFileProcessingOptions {
        public enum ProcessingStrategy {
            SPLIT_HORIZONTAL,   // 水平分割
            SPLIT_VERTICAL,     // 垂直分割
            SPLIT_GRID,         // 网格分割
            RESIZE_AND_SPLIT,   // 缩放后分割
            ADAPTIVE            // 自适应分割
        }
        
        private ProcessingStrategy processingStrategy = ProcessingStrategy.ADAPTIVE;
        private int maxChunkSize = MAX_CHUNK_SIZE;
        private int minChunks = 2;
        private boolean preserveAspectRatio = true;
        
        public static LargeFileProcessingOptions createDefault() {
            return new LargeFileProcessingOptions();
        }
        
        // Getters and Setters
        public ProcessingStrategy getProcessingStrategy() { return processingStrategy; }
        public void setProcessingStrategy(ProcessingStrategy processingStrategy) { this.processingStrategy = processingStrategy; }
        
        public int getMaxChunkSize() { return maxChunkSize; }
        public void setMaxChunkSize(int maxChunkSize) { this.maxChunkSize = Math.max(512, maxChunkSize); }
        
        public int getMinChunks() { return minChunks; }
        public void setMinChunks(int minChunks) { this.minChunks = Math.max(1, minChunks); }
        
        public boolean isPreserveAspectRatio() { return preserveAspectRatio; }
        public void setPreserveAspectRatio(boolean preserveAspectRatio) { this.preserveAspectRatio = preserveAspectRatio; }
    }
    
    /**
     * 处理结果
     */
    public static class LargeFileProcessingResult {
        private final boolean success;
        private final List<ImageChunk> chunks;
        private final String errorMessage;
        
        private LargeFileProcessingResult(boolean success, List<ImageChunk> chunks, String errorMessage) {
            this.success = success;
            this.chunks = chunks;
            this.errorMessage = errorMessage;
        }
        
        public static LargeFileProcessingResult success(List<ImageChunk> chunks) {
            return new LargeFileProcessingResult(true, chunks, null);
        }
        
        public static LargeFileProcessingResult failure(String errorMessage) {
            return new LargeFileProcessingResult(false, null, errorMessage);
        }
        
        public boolean isSuccess() { return success; }
        public List<ImageChunk> getChunks() { return chunks; }
        public String getErrorMessage() { return errorMessage; }
    }
}
