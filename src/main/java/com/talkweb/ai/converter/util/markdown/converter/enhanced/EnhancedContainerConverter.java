package com.talkweb.ai.converter.util.markdown.converter.enhanced;

import com.talkweb.ai.converter.util.markdown.ConversionContext;
import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import com.talkweb.ai.converter.util.markdown.converter.EnhancedElementConverter;
import org.jsoup.nodes.Element;

import java.util.*;

import static com.talkweb.ai.converter.util.markdown.MarkdownConstants.*;

/**
 * Enhanced converter for HTML container elements with framework compatibility
 * 
 * Supports:
 * - Bootstrap containers (card, panel, modal, alert, jumbotron, etc.)
 * - Ant Design containers (ant-card, ant-modal, ant-alert, ant-drawer, etc.)
 * - Element UI containers (el-card, el-dialog, el-alert, el-drawer, etc.)
 * - Vuetify containers (v-card, v-dialog, v-alert, v-navigation-drawer, etc.)
 * - Material-UI containers (MuiCard, MuiDialog, MuiAlert, MuiDrawer, etc.)
 * - Semantic UI containers (ui card, modal, message, etc.)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class EnhancedContainerConverter extends EnhancedElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(
        TAG_DIV, TAG_SECTION, TAG_ARTICLE, TAG_ASIDE, "details", "summary"
    );
    
    // Container-specific CSS classes for identification
    private static final Set<String> CONTAINER_CLASSES = Set.of(
        "card", "panel", "modal", "alert", "jumbotron", "hero", "banner",
        "ant-card", "ant-modal", "ant-alert", "ant-drawer", "ant-notification",
        "el-card", "el-dialog", "el-alert", "el-drawer", "el-notification",
        "v-card", "v-dialog", "v-alert", "v-navigation-drawer", "v-snackbar",
        "MuiCard", "MuiDialog", "MuiAlert", "MuiDrawer", "MuiSnackbar",
        "ui card", "ui modal", "ui message", "ui segment"
    );
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public boolean canConvert(Element element, ConversionContext context) {
        // Only convert if it's clearly a container element
        return super.canConvert(element, context) && isContainerElement(element);
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        UIFramework framework = detectUIFramework(element);
        ContainerType containerType = detectContainerType(element, framework);
        
        switch (containerType) {
            case CARD:
                convertCard(element, builder, context, framework);
                break;
            case MODAL:
                convertModal(element, builder, context, framework);
                break;
            case ALERT:
                convertAlert(element, builder, context, framework);
                break;
            case HERO:
                convertHero(element, builder, context, framework);
                break;
            case PANEL:
                convertPanel(element, builder, context, framework);
                break;
            case DETAILS:
                convertDetails(element, builder, context, framework);
                break;
            default:
                convertGenericContainer(element, builder, context, framework);
                break;
        }
    }
    
    @Override
    public int getPriority() {
        return 55; // Medium priority for containers
    }
    
    /**
     * Enum for different types of containers
     */
    private enum ContainerType {
        CARD,
        MODAL,
        ALERT,
        HERO,
        PANEL,
        DETAILS,
        GENERIC
    }
    
    /**
     * Checks if an element is a container element
     */
    private boolean isContainerElement(Element element) {
        String tagName = element.tagName().toLowerCase();
        
        // details tag is always a container
        if ("details".equals(tagName)) {
            return true;
        }
        
        // Check for container-specific classes
        String className = element.className().toLowerCase();
        return CONTAINER_CLASSES.stream().anyMatch(className::contains);
    }
    
    /**
     * Detects the type of container
     */
    private ContainerType detectContainerType(Element element, UIFramework framework) {
        String className = element.className().toLowerCase();
        String tagName = element.tagName().toLowerCase();
        
        // Check tag-based containers
        if ("details".equals(tagName)) {
            return ContainerType.DETAILS;
        }
        
        // Check for alert patterns
        if (className.contains("alert") || className.contains("message") || 
            className.contains("notification") || className.contains("snackbar")) {
            return ContainerType.ALERT;
        }
        
        // Check for modal patterns
        if (className.contains("modal") || className.contains("dialog") || 
            className.contains("drawer")) {
            return ContainerType.MODAL;
        }
        
        // Check for hero/banner patterns
        if (className.contains("hero") || className.contains("jumbotron") || 
            className.contains("banner")) {
            return ContainerType.HERO;
        }
        
        // Check for panel patterns
        if (className.contains("panel") || className.contains("segment")) {
            return ContainerType.PANEL;
        }
        
        // Check for card patterns
        if (className.contains("card")) {
            return ContainerType.CARD;
        }
        
        return ContainerType.GENERIC;
    }
    
    /**
     * Converts card containers
     */
    private void convertCard(Element card, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        CardContent content = extractCardContent(card, framework);
        
        // Card header
        if (!content.title.isEmpty()) {
            builder.heading(3, content.title);
        }
        
        // Card image
        if (!content.imageUrl.isEmpty()) {
            String altText = content.imageAlt.isEmpty() ? "Card image" : content.imageAlt;
            builder.image(altText, content.imageUrl);
            builder.newline();
        }
        
        // Card body
        if (!content.body.isEmpty()) {
            builder.append(content.body).newline();
        }
        
        // Card footer
        if (!content.footer.isEmpty()) {
            builder.append("*").append(content.footer).append("*").newline();
        }
        
        builder.newline();
    }
    
    /**
     * Converts modal containers
     */
    private void convertModal(Element modal, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        ModalContent content = extractModalContent(modal, framework);
        
        builder.append("**Modal: ").append(content.title).append("**").newline().newline();
        
        if (!content.body.isEmpty()) {
            builder.append(content.body).newline();
        }
        
        if (!content.actions.isEmpty()) {
            builder.append("*Actions: ").append(String.join(", ", content.actions)).append("*").newline();
        }
        
        builder.newline();
    }
    
    /**
     * Converts alert containers
     */
    private void convertAlert(Element alert, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        AlertContent content = extractAlertContent(alert, framework);
        
        String alertIcon = getAlertIcon(content.type);
        
        builder.append(alertIcon).append(" **").append(content.type.toUpperCase()).append("**");
        
        if (!content.title.isEmpty()) {
            builder.append(": ").append(content.title);
        }
        
        builder.newline();
        
        if (!content.message.isEmpty()) {
            builder.append(content.message).newline();
        }
        
        builder.newline();
    }
    
    /**
     * Converts hero/banner containers
     */
    private void convertHero(Element hero, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        HeroContent content = extractHeroContent(hero, framework);
        
        if (!content.title.isEmpty()) {
            builder.heading(1, content.title);
        }
        
        if (!content.subtitle.isEmpty()) {
            builder.heading(2, content.subtitle);
        }
        
        if (!content.description.isEmpty()) {
            builder.append(content.description).newline().newline();
        }
        
        if (!content.actions.isEmpty()) {
            for (String action : content.actions) {
                builder.append("[").append(action).append("] ");
            }
            builder.newline();
        }
        
        builder.newline();
    }
    
    /**
     * Converts panel containers
     */
    private void convertPanel(Element panel, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        PanelContent content = extractPanelContent(panel, framework);
        
        if (!content.title.isEmpty()) {
            builder.heading(4, content.title);
        }
        
        if (!content.body.isEmpty()) {
            builder.append(content.body).newline();
        }
        
        builder.newline();
    }
    
    /**
     * Converts details/summary containers
     */
    private void convertDetails(Element details, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        Element summary = details.selectFirst("summary");
        String summaryText = summary != null ? extractTextContent(summary) : "Details";
        
        boolean isOpen = details.hasAttr("open");
        String indicator = isOpen ? "▼" : "▶";
        
        builder.append(indicator).append(" **").append(summaryText).append("**").newline();
        
        if (isOpen) {
            // Process the content excluding the summary
            Element contentClone = details.clone();
            Element summaryClone = contentClone.selectFirst("summary");
            if (summaryClone != null) {
                summaryClone.remove();
            }
            
            String content = extractTextContent(contentClone);
            if (!content.isEmpty()) {
                builder.append(content).newline();
            }
        }
        
        builder.newline();
    }
    
    /**
     * Converts generic containers
     */
    private void convertGenericContainer(Element container, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        // For generic containers, just process children
        processChildren(container, builder, context);
    }
    
    // Data classes for container content
    
    private static class CardContent {
        String title;
        String imageUrl;
        String imageAlt;
        String body;
        String footer;
        
        CardContent(String title, String imageUrl, String imageAlt, String body, String footer) {
            this.title = title;
            this.imageUrl = imageUrl;
            this.imageAlt = imageAlt;
            this.body = body;
            this.footer = footer;
        }
    }
    
    private static class ModalContent {
        String title;
        String body;
        List<String> actions;
        
        ModalContent(String title, String body, List<String> actions) {
            this.title = title;
            this.body = body;
            this.actions = actions;
        }
    }
    
    private static class AlertContent {
        String type;
        String title;
        String message;
        
        AlertContent(String type, String title, String message) {
            this.type = type;
            this.title = title;
            this.message = message;
        }
    }
    
    private static class HeroContent {
        String title;
        String subtitle;
        String description;
        List<String> actions;
        
        HeroContent(String title, String subtitle, String description, List<String> actions) {
            this.title = title;
            this.subtitle = subtitle;
            this.description = description;
            this.actions = actions;
        }
    }
    
    private static class PanelContent {
        String title;
        String body;

        PanelContent(String title, String body) {
            this.title = title;
            this.body = body;
        }
    }

    // Helper methods for container processing

    /**
     * Extracts card content based on framework
     */
    private CardContent extractCardContent(Element card, UIFramework framework) {
        String title = EMPTY_STRING;
        String imageUrl = EMPTY_STRING;
        String imageAlt = EMPTY_STRING;
        String body = EMPTY_STRING;
        String footer = EMPTY_STRING;

        switch (framework) {
            case BOOTSTRAP:
                Element bsHeader = card.selectFirst(".card-header, .card-title");
                if (bsHeader != null) {
                    title = extractTextContent(bsHeader);
                }

                Element bsImg = card.selectFirst(".card-img, .card-img-top");
                if (bsImg != null) {
                    imageUrl = getAttribute(bsImg, ATTR_SRC);
                    imageAlt = getAttribute(bsImg, ATTR_ALT);
                }

                Element bsBody = card.selectFirst(".card-body");
                if (bsBody != null) {
                    body = extractTextContent(bsBody);
                }

                Element bsFooter = card.selectFirst(".card-footer");
                if (bsFooter != null) {
                    footer = extractTextContent(bsFooter);
                }
                break;

            case ANT_DESIGN:
                Element antTitle = card.selectFirst(".ant-card-head-title");
                if (antTitle != null) {
                    title = extractTextContent(antTitle);
                }

                Element antImg = card.selectFirst(".ant-card-cover img");
                if (antImg != null) {
                    imageUrl = getAttribute(antImg, ATTR_SRC);
                    imageAlt = getAttribute(antImg, ATTR_ALT);
                }

                Element antBody = card.selectFirst(".ant-card-body");
                if (antBody != null) {
                    body = extractTextContent(antBody);
                }

                Element antActions = card.selectFirst(".ant-card-actions");
                if (antActions != null) {
                    footer = extractTextContent(antActions);
                }
                break;

            case ELEMENT_UI:
                Element elHeader = card.selectFirst(".el-card__header");
                if (elHeader != null) {
                    title = extractTextContent(elHeader);
                }

                Element elBody = card.selectFirst(".el-card__body");
                if (elBody != null) {
                    body = extractTextContent(elBody);
                }
                break;

            case VUETIFY:
                Element vTitle = card.selectFirst(".v-card-title");
                if (vTitle != null) {
                    title = extractTextContent(vTitle);
                }

                Element vImg = card.selectFirst(".v-img img");
                if (vImg != null) {
                    imageUrl = getAttribute(vImg, ATTR_SRC);
                    imageAlt = getAttribute(vImg, ATTR_ALT);
                }

                Element vText = card.selectFirst(".v-card-text");
                if (vText != null) {
                    body = extractTextContent(vText);
                }

                Element vActions = card.selectFirst(".v-card-actions");
                if (vActions != null) {
                    footer = extractTextContent(vActions);
                }
                break;

            default:
                // Generic extraction
                Element genericTitle = card.selectFirst("h1, h2, h3, h4, h5, h6, .title, .header");
                if (genericTitle != null) {
                    title = extractTextContent(genericTitle);
                }

                Element genericImg = card.selectFirst("img");
                if (genericImg != null) {
                    imageUrl = getAttribute(genericImg, ATTR_SRC);
                    imageAlt = getAttribute(genericImg, ATTR_ALT);
                }

                body = extractTextContent(card);
                break;
        }

        return new CardContent(title, imageUrl, imageAlt, body, footer);
    }

    /**
     * Extracts modal content based on framework
     */
    private ModalContent extractModalContent(Element modal, UIFramework framework) {
        String title = EMPTY_STRING;
        String body = EMPTY_STRING;
        List<String> actions = new ArrayList<>();

        switch (framework) {
            case BOOTSTRAP:
                Element bsTitle = modal.selectFirst(".modal-title");
                if (bsTitle != null) {
                    title = extractTextContent(bsTitle);
                }

                Element bsBody = modal.selectFirst(".modal-body");
                if (bsBody != null) {
                    body = extractTextContent(bsBody);
                }

                Element bsFooter = modal.selectFirst(".modal-footer");
                if (bsFooter != null) {
                    bsFooter.select("button").forEach(btn -> actions.add(extractTextContent(btn)));
                }
                break;

            case ANT_DESIGN:
                Element antTitle = modal.selectFirst(".ant-modal-title");
                if (antTitle != null) {
                    title = extractTextContent(antTitle);
                }

                Element antBody = modal.selectFirst(".ant-modal-body");
                if (antBody != null) {
                    body = extractTextContent(antBody);
                }

                Element antFooter = modal.selectFirst(".ant-modal-footer");
                if (antFooter != null) {
                    antFooter.select("button").forEach(btn -> actions.add(extractTextContent(btn)));
                }
                break;

            case ELEMENT_UI:
                Element elTitle = modal.selectFirst(".el-dialog__title");
                if (elTitle != null) {
                    title = extractTextContent(elTitle);
                }

                Element elBody = modal.selectFirst(".el-dialog__body");
                if (elBody != null) {
                    body = extractTextContent(elBody);
                }

                Element elFooter = modal.selectFirst(".el-dialog__footer");
                if (elFooter != null) {
                    elFooter.select("button").forEach(btn -> actions.add(extractTextContent(btn)));
                }
                break;

            default:
                Element genericTitle = modal.selectFirst("h1, h2, h3, h4, h5, h6, .title");
                if (genericTitle != null) {
                    title = extractTextContent(genericTitle);
                }
                body = extractTextContent(modal);
                break;
        }

        return new ModalContent(title, body, actions);
    }

    /**
     * Extracts alert content based on framework
     */
    private AlertContent extractAlertContent(Element alert, UIFramework framework) {
        String type = extractAlertType(alert, framework);
        String title = EMPTY_STRING;
        String message = EMPTY_STRING;

        switch (framework) {
            case BOOTSTRAP:
                Element bsTitle = alert.selectFirst(".alert-heading");
                if (bsTitle != null) {
                    title = extractTextContent(bsTitle);
                }
                message = extractTextContent(alert);
                break;

            case ANT_DESIGN:
                Element antTitle = alert.selectFirst(".ant-alert-message");
                if (antTitle != null) {
                    title = extractTextContent(antTitle);
                }

                Element antDesc = alert.selectFirst(".ant-alert-description");
                if (antDesc != null) {
                    message = extractTextContent(antDesc);
                } else {
                    message = extractTextContent(alert);
                }
                break;

            case ELEMENT_UI:
                Element elTitle = alert.selectFirst(".el-alert__title");
                if (elTitle != null) {
                    title = extractTextContent(elTitle);
                }

                Element elDesc = alert.selectFirst(".el-alert__description");
                if (elDesc != null) {
                    message = extractTextContent(elDesc);
                } else {
                    message = extractTextContent(alert);
                }
                break;

            default:
                message = extractTextContent(alert);
                break;
        }

        return new AlertContent(type, title, message);
    }

    /**
     * Extracts alert type from element
     */
    private String extractAlertType(Element alert, UIFramework framework) {
        String className = alert.className().toLowerCase();

        // Common alert types
        if (className.contains("success")) return "success";
        if (className.contains("error") || className.contains("danger")) return "error";
        if (className.contains("warning")) return "warning";
        if (className.contains("info")) return "info";

        // Framework-specific type detection
        switch (framework) {
            case BOOTSTRAP:
                if (className.contains("alert-primary")) return "primary";
                if (className.contains("alert-secondary")) return "secondary";
                if (className.contains("alert-success")) return "success";
                if (className.contains("alert-danger")) return "error";
                if (className.contains("alert-warning")) return "warning";
                if (className.contains("alert-info")) return "info";
                if (className.contains("alert-light")) return "light";
                if (className.contains("alert-dark")) return "dark";
                break;
            case ANT_DESIGN:
                if (className.contains("ant-alert-success")) return "success";
                if (className.contains("ant-alert-error")) return "error";
                if (className.contains("ant-alert-warning")) return "warning";
                if (className.contains("ant-alert-info")) return "info";
                break;
            case ELEMENT_UI:
                if (className.contains("el-alert--success")) return "success";
                if (className.contains("el-alert--error")) return "error";
                if (className.contains("el-alert--warning")) return "warning";
                if (className.contains("el-alert--info")) return "info";
                break;
            default:
                break;
        }

        return "info"; // default type
    }

    /**
     * Gets alert icon based on type
     */
    private String getAlertIcon(String type) {
        switch (type.toLowerCase()) {
            case "success":
                return "✅";
            case "error":
            case "danger":
                return "❌";
            case "warning":
                return "⚠️";
            case "info":
                return "ℹ️";
            default:
                return "📢";
        }
    }

    /**
     * Extracts hero content based on framework
     */
    private HeroContent extractHeroContent(Element hero, UIFramework framework) {
        String title = EMPTY_STRING;
        String subtitle = EMPTY_STRING;
        String description = EMPTY_STRING;
        List<String> actions = new ArrayList<>();

        switch (framework) {
            case BOOTSTRAP:
                Element bsTitle = hero.selectFirst(".display-1, .display-2, .display-3, .display-4, h1");
                if (bsTitle != null) {
                    title = extractTextContent(bsTitle);
                }

                Element bsSubtitle = hero.selectFirst(".lead, h2");
                if (bsSubtitle != null) {
                    subtitle = extractTextContent(bsSubtitle);
                }

                Element bsDesc = hero.selectFirst("p");
                if (bsDesc != null) {
                    description = extractTextContent(bsDesc);
                }

                hero.select(".btn").forEach(btn -> actions.add(extractTextContent(btn)));
                break;

            default:
                Element genericTitle = hero.selectFirst("h1");
                if (genericTitle != null) {
                    title = extractTextContent(genericTitle);
                }

                Element genericSubtitle = hero.selectFirst("h2");
                if (genericSubtitle != null) {
                    subtitle = extractTextContent(genericSubtitle);
                }

                Element genericDesc = hero.selectFirst("p");
                if (genericDesc != null) {
                    description = extractTextContent(genericDesc);
                }

                hero.select("button, .button").forEach(btn -> actions.add(extractTextContent(btn)));
                break;
        }

        return new HeroContent(title, subtitle, description, actions);
    }

    /**
     * Extracts panel content based on framework
     */
    private PanelContent extractPanelContent(Element panel, UIFramework framework) {
        String title = EMPTY_STRING;
        String body = EMPTY_STRING;

        switch (framework) {
            case BOOTSTRAP:
                Element bsTitle = panel.selectFirst(".panel-title, .card-title");
                if (bsTitle != null) {
                    title = extractTextContent(bsTitle);
                }

                Element bsBody = panel.selectFirst(".panel-body, .card-body");
                if (bsBody != null) {
                    body = extractTextContent(bsBody);
                } else {
                    body = extractTextContent(panel);
                }
                break;

            case SEMANTIC_UI:
                Element semTitle = panel.selectFirst(".header");
                if (semTitle != null) {
                    title = extractTextContent(semTitle);
                }

                Element semContent = panel.selectFirst(".content");
                if (semContent != null) {
                    body = extractTextContent(semContent);
                } else {
                    body = extractTextContent(panel);
                }
                break;

            default:
                Element genericTitle = panel.selectFirst("h1, h2, h3, h4, h5, h6, .title, .header");
                if (genericTitle != null) {
                    title = extractTextContent(genericTitle);
                }
                body = extractTextContent(panel);
                break;
        }

        return new PanelContent(title, body);
    }
}
