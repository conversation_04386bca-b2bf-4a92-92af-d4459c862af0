package com.talkweb.ai.converter.util.markdown.converter;

import com.talkweb.ai.converter.util.markdown.ConversionContext;
import com.talkweb.ai.converter.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.Set;

import static com.talkweb.ai.converter.util.markdown.MarkdownConstants.*;

/**
 * Converter for HTML list elements (ul, ol, li)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ListConverter extends AbstractElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(TAG_UL, TAG_OL, TAG_LI);
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String tagName = element.tagName().toLowerCase();
        
        switch (tagName) {
            case TAG_UL:
                convertUnorderedList(element, builder, context);
                break;
            case TAG_OL:
                convertOrderedList(element, builder, context);
                break;
            case TAG_LI:
                // List items are handled by their parent list
                break;
            default:
                throw new ConversionException("Unsupported list tag: " + tagName);
        }
    }
    
    @Override
    public boolean shouldProcessChildren(Element element, ConversionContext context) {
        String tagName = element.tagName().toLowerCase();
        // Only ul and ol should process children normally, li is handled specially
        return TAG_UL.equals(tagName) || TAG_OL.equals(tagName);
    }
    
    @Override
    public int getPriority() {
        return 65; // Medium-high priority for lists
    }
    
    @Override
    public void beforeConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        String tagName = element.tagName().toLowerCase();
        if (TAG_UL.equals(tagName) || TAG_OL.equals(tagName)) {
            context.setInList(true);
            if (!context.hasAncestor(TAG_UL) && !context.hasAncestor(TAG_OL)) {
                // Top-level list, add newline before if not empty
                if (!builder.isEmpty()) {
                    builder.newline();
                }
            }
        }
    }
    
    @Override
    public void afterConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        String tagName = element.tagName().toLowerCase();
        if (TAG_UL.equals(tagName) || TAG_OL.equals(tagName)) {
            context.setInList(false);
            if (!context.hasAncestor(TAG_UL) && !context.hasAncestor(TAG_OL)) {
                // Top-level list, add newline after
                builder.newline();
            }
        }
    }
    
    /**
     * Converts an unordered list
     * 
     * @param list the ul element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertUnorderedList(Element list, MarkdownBuilder builder, ConversionContext context) {
        Elements listItems = list.children().select("li");
        
        for (Element item : listItems) {
            convertListItem(item, builder, context, false, 0);
        }
    }
    
    /**
     * Converts an ordered list
     * 
     * @param list the ol element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertOrderedList(Element list, MarkdownBuilder builder, ConversionContext context) {
        Elements listItems = list.children().select("li");
        
        int index = 1;
        for (Element item : listItems) {
            convertListItem(item, builder, context, true, index++);
        }
    }
    
    /**
     * Converts a list item
     * 
     * @param item the li element
     * @param builder the markdown builder
     * @param context the conversion context
     * @param ordered whether this is an ordered list item
     * @param index the item index (for ordered lists)
     */
    private void convertListItem(Element item, MarkdownBuilder builder, ConversionContext context, 
                                boolean ordered, int index) {
        
        int depth = calculateListDepth(context);
        
        // Add indentation for nested lists
        for (int i = 0; i < depth; i++) {
            builder.append("  ");
        }
        
        // Add list marker
        if (ordered) {
            builder.append(String.valueOf(index)).append(". ");
        } else {
            builder.append(UNORDERED_LIST_MARKER);
        }
        
        // Process item content with nested formatting
        String itemContent = processListItemContent(item, context);
        
        // Handle nested lists within list items
        if (hasNestedList(item)) {
            // Split content and nested lists
            processListItemWithNestedContent(item, builder, context, itemContent);
        } else {
            // Simple list item
            builder.append(itemContent);
        }
        
        builder.newline();
    }
    
    /**
     * Calculates the current list nesting depth
     * 
     * @param context the conversion context
     * @return the nesting depth
     */
    private int calculateListDepth(ConversionContext context) {
        int depth = 0;
        
        // Count ancestor list elements
        for (int i = 0; i < context.getCurrentDepth(); i++) {
            Element ancestor = context.peekElement();
            if (ancestor != null) {
                String tagName = ancestor.tagName().toLowerCase();
                if (TAG_UL.equals(tagName) || TAG_OL.equals(tagName)) {
                    depth++;
                }
            }
        }
        
        return Math.max(0, depth - 1); // Subtract 1 because current list doesn't count
    }
    
    /**
     * Processes the content of a list item, handling nested formatting
     *
     * @param item the list item element
     * @param context the conversion context
     * @return the processed content as markdown
     */
    private String processListItemContent(Element item, ConversionContext context) {
        StringBuilder content = new StringBuilder();

        for (org.jsoup.nodes.Node child : item.childNodes()) {
            if (child instanceof org.jsoup.nodes.TextNode) {
                String text = ((org.jsoup.nodes.TextNode) child).text();
                if (!text.trim().isEmpty()) {
                    content.append(decodeHtmlEntities(text));
                }
            } else if (child instanceof Element) {
                Element childElement = (Element) child;
                String tagName = childElement.tagName().toLowerCase();

                // Skip nested lists - they'll be handled separately
                if ("ul".equals(tagName) || "ol".equals(tagName)) {
                    continue;
                }

                // Process other elements
                MarkdownBuilder childBuilder = new MarkdownBuilder();
                processChildElement(childElement, childBuilder, context);
                content.append(childBuilder.toString());
            }
        }

        return content.toString().trim();
    }

    /**
     * Checks if a list item contains nested lists
     *
     * @param item the list item
     * @return true if it contains nested lists
     */
    private boolean hasNestedList(Element item) {
        return !item.select("ul, ol").isEmpty();
    }
    
    /**
     * Processes a list item that contains nested content including lists
     * 
     * @param item the list item
     * @param builder the markdown builder
     * @param context the conversion context
     * @param itemContent the item content
     */
    private void processListItemWithNestedContent(Element item, MarkdownBuilder builder, 
                                                 ConversionContext context, String itemContent) {
        
        // For now, just append the content
        // TODO: Implement proper handling of nested lists within list items
        builder.append(itemContent);
        
        // Find and process nested lists
        Elements nestedLists = item.select("ul, ol");
        for (Element nestedList : nestedLists) {
            builder.newline();
            // Process nested list with increased depth
            context.pushElement(item);
            try {
                if (TAG_UL.equals(nestedList.tagName().toLowerCase())) {
                    convertUnorderedList(nestedList, builder, context);
                } else {
                    convertOrderedList(nestedList, builder, context);
                }
            } finally {
                context.popElement();
            }
        }
    }
}
