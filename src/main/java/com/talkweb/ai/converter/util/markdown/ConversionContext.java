package com.talkweb.ai.converter.util.markdown;

import com.talkweb.ai.converter.util.HtmlConversionMode;
import org.jsoup.nodes.Element;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Context object that maintains state during HTML to Markdown conversion
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ConversionContext {
    
    private final HtmlConversionMode mode;
    private final Map<String, Object> properties;
    private final Map<String, Object> cache;
    private final Stack<Element> elementStack;
    private final Set<String> processedElements;
    private int currentDepth;
    private int listItemIndex;
    private boolean inTable;
    private boolean inList;
    private boolean inCodeBlock;
    
    /**
     * Creates a new conversion context
     * 
     * @param mode the conversion mode
     */
    public ConversionContext(HtmlConversionMode mode) {
        this.mode = mode != null ? mode : HtmlConversionMode.LOOSE;
        this.properties = new ConcurrentHashMap<>();
        this.cache = new ConcurrentHashMap<>();
        this.elementStack = new Stack<>();
        this.processedElements = new HashSet<>();
        this.currentDepth = 0;
        this.listItemIndex = 1;
        this.inTable = false;
        this.inList = false;
        this.inCodeBlock = false;
    }
    
    /**
     * Gets the conversion mode
     * 
     * @return the conversion mode
     */
    public HtmlConversionMode getMode() {
        return mode;
    }
    
    /**
     * Checks if the context is in strict mode
     * 
     * @return true if in strict mode
     */
    public boolean isStrictMode() {
        return mode == HtmlConversionMode.STRICT;
    }
    
    /**
     * Sets a property value
     * 
     * @param key the property key
     * @param value the property value
     */
    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }
    
    /**
     * Gets a property value
     * 
     * @param key the property key
     * @return the property value, or null if not found
     */
    public Object getProperty(String key) {
        return properties.get(key);
    }
    
    /**
     * Gets a property value with a default
     * 
     * @param key the property key
     * @param defaultValue the default value
     * @param <T> the value type
     * @return the property value or default
     */
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key, T defaultValue) {
        Object value = properties.get(key);
        return value != null ? (T) value : defaultValue;
    }
    
    /**
     * Checks if a property exists
     * 
     * @param key the property key
     * @return true if the property exists
     */
    public boolean hasProperty(String key) {
        return properties.containsKey(key);
    }
    
    /**
     * Caches a value with size limit to prevent memory leaks
     *
     * @param key the cache key
     * @param value the value to cache
     */
    public void cache(String key, Object value) {
        // Prevent cache from growing too large
        if (cache.size() >= 1000) {
            // Remove oldest entries (simple LRU-like behavior)
            cache.clear();
        }
        cache.put(key, value);
    }
    
    /**
     * Gets a cached value
     * 
     * @param key the cache key
     * @return the cached value, or null if not found
     */
    public Object getCached(String key) {
        return cache.get(key);
    }
    
    /**
     * Gets a cached value with a default
     * 
     * @param key the cache key
     * @param defaultValue the default value
     * @param <T> the value type
     * @return the cached value or default
     */
    @SuppressWarnings("unchecked")
    public <T> T getCached(String key, T defaultValue) {
        Object value = cache.get(key);
        return value != null ? (T) value : defaultValue;
    }
    
    /**
     * Pushes an element onto the stack
     * 
     * @param element the element to push
     */
    public void pushElement(Element element) {
        elementStack.push(element);
        currentDepth++;
    }
    
    /**
     * Pops an element from the stack
     * 
     * @return the popped element, or null if stack is empty
     */
    public Element popElement() {
        if (!elementStack.isEmpty()) {
            currentDepth--;
            return elementStack.pop();
        }
        return null;
    }
    
    /**
     * Peeks at the top element without removing it
     * 
     * @return the top element, or null if stack is empty
     */
    public Element peekElement() {
        return elementStack.isEmpty() ? null : elementStack.peek();
    }
    
    /**
     * Gets the current nesting depth
     * 
     * @return the current depth
     */
    public int getCurrentDepth() {
        return currentDepth;
    }
    
    /**
     * Marks an element as processed
     * 
     * @param elementId the element identifier
     */
    public void markProcessed(String elementId) {
        processedElements.add(elementId);
    }
    
    /**
     * Checks if an element has been processed
     * 
     * @param elementId the element identifier
     * @return true if processed
     */
    public boolean isProcessed(String elementId) {
        return processedElements.contains(elementId);
    }
    
    /**
     * Gets the current list item index
     * 
     * @return the list item index
     */
    public int getListItemIndex() {
        return listItemIndex;
    }
    
    /**
     * Increments the list item index
     */
    public void incrementListItemIndex() {
        listItemIndex++;
    }
    
    /**
     * Resets the list item index
     */
    public void resetListItemIndex() {
        listItemIndex = 1;
    }
    
    /**
     * Checks if currently inside a table
     * 
     * @return true if in table
     */
    public boolean isInTable() {
        return inTable;
    }
    
    /**
     * Sets the table state
     * 
     * @param inTable true if entering table, false if leaving
     */
    public void setInTable(boolean inTable) {
        this.inTable = inTable;
    }
    
    /**
     * Checks if currently inside a list
     * 
     * @return true if in list
     */
    public boolean isInList() {
        return inList;
    }
    
    /**
     * Sets the list state
     * 
     * @param inList true if entering list, false if leaving
     */
    public void setInList(boolean inList) {
        this.inList = inList;
        if (!inList) {
            resetListItemIndex();
        }
    }
    
    /**
     * Checks if currently inside a code block
     * 
     * @return true if in code block
     */
    public boolean isInCodeBlock() {
        return inCodeBlock;
    }
    
    /**
     * Sets the code block state
     * 
     * @param inCodeBlock true if entering code block, false if leaving
     */
    public void setInCodeBlock(boolean inCodeBlock) {
        this.inCodeBlock = inCodeBlock;
    }
    
    /**
     * Finds the nearest ancestor element of the specified type
     * 
     * @param tagName the tag name to search for
     * @return the ancestor element, or null if not found
     */
    public Element findAncestor(String tagName) {
        for (int i = elementStack.size() - 1; i >= 0; i--) {
            Element element = elementStack.get(i);
            if (tagName.equalsIgnoreCase(element.tagName())) {
                return element;
            }
        }
        return null;
    }
    
    /**
     * Checks if an ancestor element of the specified type exists
     * 
     * @param tagName the tag name to search for
     * @return true if ancestor exists
     */
    public boolean hasAncestor(String tagName) {
        return findAncestor(tagName) != null;
    }
    
    /**
     * Creates a copy of this context for nested processing
     * 
     * @return a new context instance
     */
    public ConversionContext copy() {
        ConversionContext copy = new ConversionContext(this.mode);
        copy.properties.putAll(this.properties);
        copy.cache.putAll(this.cache);
        copy.currentDepth = this.currentDepth;
        copy.listItemIndex = this.listItemIndex;
        copy.inTable = this.inTable;
        copy.inList = this.inList;
        copy.inCodeBlock = this.inCodeBlock;
        return copy;
    }
    
    /**
     * Clears the context state
     */
    public void clear() {
        properties.clear();
        cache.clear();
        elementStack.clear();
        processedElements.clear();
        currentDepth = 0;
        listItemIndex = 1;
        inTable = false;
        inList = false;
        inCodeBlock = false;
    }
}
