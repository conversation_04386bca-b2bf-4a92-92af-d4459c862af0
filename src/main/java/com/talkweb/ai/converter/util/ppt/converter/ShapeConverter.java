package com.talkweb.ai.converter.util.ppt.converter;

import com.talkweb.ai.converter.util.ppt.PptConversionContext;
import org.apache.poi.sl.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.geom.Rectangle2D;
import java.util.List;

/**
 * Generic shape converter for PowerPoint shapes
 * 
 * Handles conversion of various shape types that don't have specialized converters,
 * focusing on extracting meaningful content and maintaining document structure.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ShapeConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(ShapeConverter.class);
    
    /**
     * Converts a generic shape to Markdown
     */
    public void convertShape(Shape<?,?> shape, StringBuilder markdown, PptConversionContext context) {
        if (shape == null) {
            return;
        }
        
        try {
            logger.debug("Converting shape: {}", shape.getClass().getSimpleName());
            
            // Handle different shape types
            if (shape instanceof AutoShape) {
                convertAutoShape((AutoShape<?,?>) shape, markdown, context);
                
            } else if (shape instanceof FreeformShape) {
                convertFreeformShape((FreeformShape<?,?>) shape, markdown, context);
                
            } else if (shape instanceof ConnectorShape) {
                convertConnectorShape((ConnectorShape<?,?>) shape, markdown, context);
                
            } else if (shape instanceof SimpleShape) {
                convertSimpleShape((SimpleShape<?,?>) shape, markdown, context);
                
            } else {
                // Generic shape handling
                convertGenericShape(shape, markdown, context);
            }
            
        } catch (Exception e) {
            String error = "Failed to convert shape: " + e.getMessage();
            context.addWarning(error);
            logger.debug("Shape conversion error", e);
            
            if (context.getConfig().isStrictMode()) {
                throw new RuntimeException(error, e);
            }
        }
    }
    
    /**
     * Converts AutoShape (rectangles, circles, arrows, etc.)
     */
    private void convertAutoShape(AutoShape<?,?> autoShape, StringBuilder markdown, 
                                 PptConversionContext context) {
        
        try {
            // Extract text content if available
            if (autoShape instanceof TextShape) {
                String text = extractTextFromTextShape((TextShape<?,?>) autoShape, context);
                if (text != null && !text.trim().isEmpty()) {
                    
                    // Format based on shape type
                    String shapeType = getShapeTypeName(autoShape);
                    if (isCalloutShape(shapeType)) {
                        // Format as callout/note
                        markdown.append("> **Note:** ").append(text.trim()).append("\n\n");
                    } else if (isArrowShape(shapeType)) {
                        // Format as emphasis
                        markdown.append("**→ ").append(text.trim()).append("**\n\n");
                    } else {
                        // Regular text box
                        markdown.append(text.trim()).append("\n\n");
                    }
                    
                    context.incrementExtractedTextBoxesCount();
                }
            }
            
            // Handle shape-specific properties
            addShapeMetadata(autoShape, markdown, context);
            
        } catch (Exception e) {
            context.addWarning("Failed to convert AutoShape: " + e.getMessage());
            logger.debug("AutoShape conversion error", e);
        }
    }
    
    /**
     * Converts FreeformShape (custom drawn shapes)
     */
    private void convertFreeformShape(FreeformShape<?,?> freeformShape, StringBuilder markdown, 
                                     PptConversionContext context) {
        
        try {
            // Freeform shapes typically don't contain text, but check anyway
            if (freeformShape instanceof TextShape) {
                String text = extractTextFromTextShape((TextShape<?,?>) freeformShape, context);
                if (text != null && !text.trim().isEmpty()) {
                    markdown.append("*[Custom Shape: ").append(text.trim()).append("]*\n\n");
                    context.incrementExtractedTextBoxesCount();
                }
            } else {
                // Add placeholder for custom shape
                markdown.append("*[Custom drawn shape]*\n\n");
            }
            
        } catch (Exception e) {
            context.addWarning("Failed to convert FreeformShape: " + e.getMessage());
            logger.debug("FreeformShape conversion error", e);
        }
    }
    
    /**
     * Converts ConnectorShape (lines, arrows connecting shapes)
     */
    private void convertConnectorShape(ConnectorShape<?,?> connectorShape, StringBuilder markdown, 
                                      PptConversionContext context) {
        
        try {
            // Connectors usually don't have text, but may have labels
            if (connectorShape instanceof TextShape) {
                String text = extractTextFromTextShape((TextShape<?,?>) connectorShape, context);
                if (text != null && !text.trim().isEmpty()) {
                    markdown.append("*→ ").append(text.trim()).append("*\n\n");
                    context.incrementExtractedTextBoxesCount();
                }
            }
            
            // Could add connector metadata if needed
            // addConnectorInfo(connectorShape, markdown, context);
            
        } catch (Exception e) {
            context.addWarning("Failed to convert ConnectorShape: " + e.getMessage());
            logger.debug("ConnectorShape conversion error", e);
        }
    }
    
    /**
     * Converts SimpleShape (basic geometric shapes)
     */
    private void convertSimpleShape(SimpleShape<?,?> simpleShape, StringBuilder markdown, 
                                   PptConversionContext context) {
        
        try {
            // Extract text if the shape contains text
            if (simpleShape instanceof TextShape) {
                String text = extractTextFromTextShape((TextShape<?,?>) simpleShape, context);
                if (text != null && !text.trim().isEmpty()) {
                    
                    // Format based on shape appearance
                    if (hasSpecialFormatting(simpleShape)) {
                        markdown.append("**").append(text.trim()).append("**\n\n");
                    } else {
                        markdown.append(text.trim()).append("\n\n");
                    }
                    
                    context.incrementExtractedTextBoxesCount();
                }
            }
            
        } catch (Exception e) {
            context.addWarning("Failed to convert SimpleShape: " + e.getMessage());
            logger.debug("SimpleShape conversion error", e);
        }
    }
    
    /**
     * Generic shape conversion fallback
     */
    private void convertGenericShape(Shape<?,?> shape, StringBuilder markdown, 
                                    PptConversionContext context) {
        
        try {
            // Try to extract any text content
            if (shape instanceof TextShape) {
                String text = extractTextFromTextShape((TextShape<?,?>) shape, context);
                if (text != null && !text.trim().isEmpty()) {
                    markdown.append(text.trim()).append("\n\n");
                    context.incrementExtractedTextBoxesCount();
                }
            } else {
                // Add placeholder for unknown shape type
                String shapeType = shape.getClass().getSimpleName();
                markdown.append("*[").append(shapeType).append("]*\n\n");
            }
            
        } catch (Exception e) {
            context.addWarning("Failed to convert generic shape: " + e.getMessage());
            logger.debug("Generic shape conversion error", e);
        }
    }
    
    /**
     * Extracts text from a TextShape
     */
    private String extractTextFromTextShape(TextShape<?,?> textShape, PptConversionContext context) {
        try {
            StringBuilder text = new StringBuilder();
            
            List<? extends TextParagraph<?,?,?>> paragraphs = textShape.getTextParagraphs();
            if (paragraphs != null) {
                for (TextParagraph<?,?,?> paragraph : paragraphs) {
                    String paragraphText = extractParagraphText(paragraph);
                    if (paragraphText != null && !paragraphText.trim().isEmpty()) {
                        text.append(paragraphText).append("\n");
                    }
                }
            }
            
            String result = text.toString().trim();
            
            // Clean and normalize text
            if (context.getConfig().isNormalizeWhitespace()) {
                result = result.replaceAll("\\s+", " ");
            }
            
            return result;
            
        } catch (Exception e) {
            logger.debug("Failed to extract text from TextShape", e);
            return null;
        }
    }
    
    /**
     * Extracts text from a paragraph
     */
    private String extractParagraphText(TextParagraph<?,?,?> paragraph) {
        try {
            StringBuilder text = new StringBuilder();
            
            List<? extends TextRun> runs = paragraph.getTextRuns();
            if (runs != null) {
                for (TextRun run : runs) {
                    String runText = run.getRawText();
                    if (runText != null) {
                        // Apply basic formatting if needed
                        if (run.isBold() && run.isItalic()) {
                            text.append("***").append(runText).append("***");
                        } else if (run.isBold()) {
                            text.append("**").append(runText).append("**");
                        } else if (run.isItalic()) {
                            text.append("*").append(runText).append("*");
                        } else {
                            text.append(runText);
                        }
                    }
                }
            }
            
            return text.toString();
            
        } catch (Exception e) {
            logger.debug("Failed to extract paragraph text", e);
            return "";
        }
    }
    
    /**
     * Gets the shape type name for classification
     */
    private String getShapeTypeName(Shape<?,?> shape) {
        try {
            if (shape instanceof AutoShape) {
                // In a real implementation, you'd get the actual shape type
                // For now, return the class name
                return shape.getClass().getSimpleName();
            }
            return shape.getClass().getSimpleName();
        } catch (Exception e) {
            return "Unknown";
        }
    }
    
    /**
     * Checks if the shape is a callout type
     */
    private boolean isCalloutShape(String shapeType) {
        return shapeType != null && 
               (shapeType.toLowerCase().contains("callout") || 
                shapeType.toLowerCase().contains("balloon"));
    }
    
    /**
     * Checks if the shape is an arrow type
     */
    private boolean isArrowShape(String shapeType) {
        return shapeType != null && 
               (shapeType.toLowerCase().contains("arrow") || 
                shapeType.toLowerCase().contains("pointer"));
    }
    
    /**
     * Checks if the shape has special formatting that should be preserved
     */
    private boolean hasSpecialFormatting(SimpleShape<?,?> shape) {
        try {
            // Check for special fill, border, or other visual properties
            // This is a simplified implementation
            return false; // Placeholder
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Adds shape metadata if relevant
     */
    private void addShapeMetadata(Shape<?,?> shape, StringBuilder markdown, 
                                 PptConversionContext context) {
        
        if (!context.getConfig().isIncludeMetadata()) {
            return;
        }
        
        try {
            // Add shape position and size information if relevant
            Rectangle2D anchor = shape.getAnchor();
            if (anchor != null) {
                // Only add metadata for significantly positioned shapes
                if (anchor.getWidth() > 100 || anchor.getHeight() > 100) {
                    markdown.append("<!-- Shape: ")
                           .append("width=").append((int)anchor.getWidth())
                           .append(", height=").append((int)anchor.getHeight())
                           .append(" -->\n");
                }
            }
            
        } catch (Exception e) {
            logger.debug("Failed to add shape metadata", e);
        }
    }
    
    /**
     * Utility method to check if a shape contains meaningful content
     */
    public boolean hasContent(Shape<?,?> shape) {
        if (shape == null) {
            return false;
        }
        
        try {
            if (shape instanceof TextShape) {
                String text = extractTextFromTextShape((TextShape<?,?>) shape, 
                    new PptConversionContext(null, null));
                return text != null && !text.trim().isEmpty();
            }
            
            // For non-text shapes, assume they have content
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Gets a description of the shape for debugging/logging
     */
    public String getShapeDescription(Shape<?,?> shape) {
        if (shape == null) {
            return "null";
        }
        
        try {
            StringBuilder desc = new StringBuilder();
            desc.append(shape.getClass().getSimpleName());
            
            if (shape instanceof TextShape) {
                String text = extractTextFromTextShape((TextShape<?,?>) shape, 
                    new PptConversionContext(null, null));
                if (text != null && !text.trim().isEmpty()) {
                    String preview = text.length() > 50 ? 
                        text.substring(0, 47) + "..." : text;
                    desc.append(" (\"").append(preview.replace("\n", " ")).append("\")");
                }
            }
            
            Rectangle2D anchor = shape.getAnchor();
            if (anchor != null) {
                desc.append(" [").append((int)anchor.getWidth())
                   .append("x").append((int)anchor.getHeight()).append("]");
            }
            
            return desc.toString();
            
        } catch (Exception e) {
            return shape.getClass().getSimpleName() + " (error)";
        }
    }
}
