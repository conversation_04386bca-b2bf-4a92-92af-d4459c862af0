package com.talkweb.ai.converter.util.ppt.converter;

import com.talkweb.ai.converter.util.ppt.PptConversionContext;
import org.apache.poi.sl.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.regex.Pattern;

/**
 * Converter for individual PowerPoint slides
 * 
 * Handles the conversion of slide content including text, shapes, images, and tables
 * while preserving the logical structure and hierarchy of the presentation.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class SlideConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(SlideConverter.class);
    
    // Text processing patterns
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s+");
    private static final Pattern BULLET_PATTERN = Pattern.compile("^[•·▪▫◦‣⁃]\\s*");
    
    // Shape converters
    private final ShapeConverter shapeConverter;
    private final PptTableConverter tableConverter;
    private final PptImageConverter imageConverter;
    
    public SlideConverter() {
        this.shapeConverter = new ShapeConverter();
        this.tableConverter = new PptTableConverter();
        this.imageConverter = new PptImageConverter();
    }
    
    /**
     * Converts a slide to Markdown format
     */
    public void convertSlide(Slide<?,?> slide, StringBuilder markdown, PptConversionContext context) {
        if (slide == null) {
            context.addWarning("Encountered null slide");
            return;
        }
        
        try {
            context.setCurrentSlideIndex(slide.getSlideNumber() - 1);
            
            // Check if slide should be skipped
            if (shouldSkipSlide(slide, context)) {
                context.incrementSkippedSlides();
                return;
            }
            
            logger.debug("Converting slide {}: {}", slide.getSlideNumber(), getSlideTitle(slide));
            
            // Extract slide title
            String slideTitle = extractSlideTitle(slide, context);
            if (slideTitle != null && !slideTitle.trim().isEmpty()) {
                markdown.append("### ").append(slideTitle.trim()).append("\n\n");
            }
            
            // Process slide shapes
            List<? extends Shape<?,?>> shapes = slide.getShapes();
            if (shapes != null && !shapes.isEmpty()) {
                processSlideShapes(shapes, markdown, context);
            }
            
            // Add speaker notes if enabled
            if (context.getConfig().isIncludeSpeakerNotes()) {
                addSpeakerNotes(slide, markdown, context);
            }
            
            context.incrementProcessedSlides();
            
        } catch (Exception e) {
            String error = "Failed to convert slide " + slide.getSlideNumber() + ": " + e.getMessage();
            context.addError(error);
            
            if (context.getConfig().isStrictMode()) {
                throw new RuntimeException(error, e);
            }
            
            // Add error placeholder in loose mode
            markdown.append("*[Slide conversion failed: ").append(e.getMessage()).append("]*\n\n");
        }
    }
    
    /**
     * Determines if a slide should be skipped
     */
    private boolean shouldSkipSlide(Slide<?,?> slide, PptConversionContext context) {
        // Skip hidden slides unless configured to include them
        if (slide.isHidden() && !context.getConfig().isIncludeHiddenSlides()) {
            logger.debug("Skipping hidden slide {}", slide.getSlideNumber());
            return true;
        }
        
        return false;
    }
    
    /**
     * Extracts the slide title
     */
    private String extractSlideTitle(Slide<?,?> slide, PptConversionContext context) {
        try {
            // Try to get title from slide layout
            String title = slide.getTitle();
            if (title != null && !title.trim().isEmpty()) {
                return cleanText(title, context);
            }
            
            // Fallback: look for title placeholder or first text shape
            List<? extends Shape<?,?>> shapes = slide.getShapes();
            if (shapes != null) {
                for (Shape<?,?> shape : shapes) {
                    if (shape instanceof TextShape) {
                        TextShape<?,?> textShape = (TextShape<?,?>) shape;
                        
                        // Check if this is a title placeholder
                        if (isTitlePlaceholder(textShape)) {
                            String text = extractTextFromShape(textShape, context);
                            if (text != null && !text.trim().isEmpty()) {
                                return cleanText(text, context);
                            }
                        }
                    }
                }
            }
            
            // If no title found, generate one
            return "Slide " + slide.getSlideNumber();
            
        } catch (Exception e) {
            logger.debug("Failed to extract slide title", e);
            return "Slide " + slide.getSlideNumber();
        }
    }
    
    /**
     * Gets slide title for logging purposes
     */
    private String getSlideTitle(Slide<?,?> slide) {
        try {
            String title = slide.getTitle();
            return title != null && !title.trim().isEmpty() ? title : "Untitled";
        } catch (Exception e) {
            return "Untitled";
        }
    }
    
    /**
     * Processes all shapes in a slide
     */
    private void processSlideShapes(List<? extends Shape<?,?>> shapes, StringBuilder markdown, 
                                   PptConversionContext context) {
        
        int shapeIndex = 0;
        for (Shape<?,?> shape : shapes) {
            try {
                String shapeId = context.generateShapeId(context.getCurrentSlideIndex(), shapeIndex);
                
                // Skip if already processed (to avoid duplicates)
                if (context.isShapeProcessed(shapeId)) {
                    continue;
                }
                
                processShape(shape, markdown, context, shapeIndex);
                context.markShapeAsProcessed(shapeId);
                
            } catch (Exception e) {
                String error = "Failed to process shape " + shapeIndex + " in slide " + 
                              (context.getCurrentSlideIndex() + 1) + ": " + e.getMessage();
                context.addError(error);
                
                if (context.getConfig().isStrictMode()) {
                    throw new RuntimeException(error, e);
                }
                
                logger.debug("Shape processing error", e);
            }
            
            shapeIndex++;
        }
    }
    
    /**
     * Processes a single shape
     */
    private void processShape(Shape<?,?> shape, StringBuilder markdown, PptConversionContext context, 
                             int shapeIndex) {
        
        if (shape == null) return;
        
        logger.debug("Processing shape type: {}", shape.getClass().getSimpleName());
        
        if (shape instanceof TextShape) {
            processTextShape((TextShape<?,?>) shape, markdown, context);
            
        } else if (shape instanceof TableShape) {
            tableConverter.convertTable((TableShape<?,?>) shape, markdown, context);
            
        } else if (shape instanceof PictureShape) {
            imageConverter.convertImage((PictureShape<?,?>) shape, markdown, context, shapeIndex);
            
        } else if (shape instanceof GroupShape) {
            processGroupShape((GroupShape<?,?>) shape, markdown, context);
            
        } else {
            // Handle other shape types with generic shape converter
            shapeConverter.convertShape(shape, markdown, context);
        }
    }
    
    /**
     * Processes text shapes (text boxes, placeholders)
     */
    private void processTextShape(TextShape<?,?> textShape, StringBuilder markdown, 
                                 PptConversionContext context) {
        
        if (!context.getConfig().isExtractTextFromShapes()) {
            return;
        }
        
        try {
            String text = extractTextFromShape(textShape, context);
            if (text != null && !text.trim().isEmpty()) {
                
                // Skip title placeholders as they're handled separately
                if (isTitlePlaceholder(textShape)) {
                    return;
                }
                
                // Format text based on shape type and content
                String formattedText = formatTextContent(text, textShape, context);
                if (!formattedText.trim().isEmpty()) {
                    markdown.append(formattedText).append("\n\n");
                    context.incrementExtractedTextBoxesCount();
                }
            }
            
        } catch (Exception e) {
            context.addWarning("Failed to extract text from shape: " + e.getMessage());
            logger.debug("Text shape processing error", e);
        }
    }
    
    /**
     * Processes group shapes (shapes containing other shapes)
     */
    private void processGroupShape(GroupShape<?,?> groupShape, StringBuilder markdown, 
                                  PptConversionContext context) {
        
        try {
            List<? extends Shape<?,?>> groupedShapes = groupShape.getShapes();
            if (groupedShapes != null && !groupedShapes.isEmpty()) {
                
                // Add group separator if needed
                if (groupedShapes.size() > 1) {
                    markdown.append("<!-- Group Start -->\n");
                }
                
                // Process each shape in the group
                for (Shape<?,?> shape : groupedShapes) {
                    processShape(shape, markdown, context, 0);
                }
                
                if (groupedShapes.size() > 1) {
                    markdown.append("<!-- Group End -->\n\n");
                }
            }
            
        } catch (Exception e) {
            context.addWarning("Failed to process group shape: " + e.getMessage());
            logger.debug("Group shape processing error", e);
        }
    }
    
    /**
     * Extracts text content from a text shape
     */
    private String extractTextFromShape(TextShape<?,?> textShape, PptConversionContext context) {
        try {
            StringBuilder text = new StringBuilder();
            
            List<? extends TextParagraph<?,?,?>> paragraphs = textShape.getTextParagraphs();
            if (paragraphs != null) {
                for (TextParagraph<?,?,?> paragraph : paragraphs) {
                    String paragraphText = extractParagraphText(paragraph, context);
                    if (paragraphText != null && !paragraphText.trim().isEmpty()) {
                        text.append(paragraphText).append("\n");
                    }
                }
            }
            
            return text.toString().trim();
            
        } catch (Exception e) {
            logger.debug("Failed to extract text from shape", e);
            return null;
        }
    }
    
    /**
     * Extracts text from a paragraph
     */
    private String extractParagraphText(TextParagraph<?,?,?> paragraph, PptConversionContext context) {
        try {
            StringBuilder text = new StringBuilder();
            
            List<? extends TextRun> runs = paragraph.getTextRuns();
            if (runs != null) {
                for (TextRun run : runs) {
                    String runText = run.getRawText();
                    if (runText != null) {
                        text.append(runText);
                    }
                }
            }
            
            return text.toString();
            
        } catch (Exception e) {
            logger.debug("Failed to extract paragraph text", e);
            return "";
        }
    }
    
    /**
     * Formats text content based on context and shape properties
     */
    private String formatTextContent(String text, TextShape<?,?> textShape, PptConversionContext context) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        String formatted = text;
        
        // Normalize whitespace if enabled
        if (context.getConfig().isNormalizeWhitespace()) {
            formatted = WHITESPACE_PATTERN.matcher(formatted).replaceAll(" ");
        }
        
        // Convert bullets to Markdown if enabled
        if (context.getConfig().isConvertBulletsToMarkdown()) {
            formatted = convertBulletsToMarkdown(formatted);
        }
        
        // Clean up the text
        formatted = cleanText(formatted, context);
        
        return formatted;
    }
    
    /**
     * Converts bullet points to Markdown format
     */
    private String convertBulletsToMarkdown(String text) {
        String[] lines = text.split("\n");
        StringBuilder result = new StringBuilder();
        
        for (String line : lines) {
            String trimmed = line.trim();
            if (!trimmed.isEmpty()) {
                // Replace bullet characters with Markdown bullets
                if (BULLET_PATTERN.matcher(trimmed).find()) {
                    trimmed = BULLET_PATTERN.matcher(trimmed).replaceFirst("- ");
                }
                result.append(trimmed).append("\n");
            }
        }
        
        return result.toString().trim();
    }
    
    /**
     * Cleans text content
     */
    private String cleanText(String text, PptConversionContext context) {
        if (text == null) return "";
        
        String cleaned = text.trim();
        
        // Remove excessive whitespace
        if (context.getConfig().isNormalizeWhitespace()) {
            cleaned = WHITESPACE_PATTERN.matcher(cleaned).replaceAll(" ");
        }
        
        // Escape Markdown special characters if needed
        cleaned = escapeMarkdownCharacters(cleaned);
        
        return cleaned;
    }
    
    /**
     * Escapes Markdown special characters
     */
    private String escapeMarkdownCharacters(String text) {
        if (text == null) return "";
        
        return text.replace("\\", "\\\\")
                  .replace("`", "\\`")
                  .replace("*", "\\*")
                  .replace("_", "\\_")
                  .replace("{", "\\{")
                  .replace("}", "\\}")
                  .replace("[", "\\[")
                  .replace("]", "\\]")
                  .replace("(", "\\(")
                  .replace(")", "\\)")
                  .replace("#", "\\#")
                  .replace("+", "\\+")
                  .replace("-", "\\-")
                  .replace(".", "\\.")
                  .replace("!", "\\!");
    }
    
    /**
     * Checks if a text shape is a title placeholder
     */
    private boolean isTitlePlaceholder(TextShape<?,?> textShape) {
        try {
            // This is a simplified check - in practice, you'd check the placeholder type
            // For now, we'll use a heuristic based on position and content
            return false; // Placeholder implementation
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Adds speaker notes to the markdown
     */
    private void addSpeakerNotes(Slide<?,?> slide, StringBuilder markdown, PptConversionContext context) {
        try {
            Notes<?,?> notes = slide.getNotes();
            if (notes != null) {
                List<? extends Shape<?,?>> noteShapes = notes.getShapes();
                if (noteShapes != null && !noteShapes.isEmpty()) {
                    
                    StringBuilder notesText = new StringBuilder();
                    for (Shape<?,?> shape : noteShapes) {
                        if (shape instanceof TextShape) {
                            String text = extractTextFromShape((TextShape<?,?>) shape, context);
                            if (text != null && !text.trim().isEmpty()) {
                                notesText.append(text).append("\n");
                            }
                        }
                    }
                    
                    if (notesText.length() > 0) {
                        markdown.append("**Speaker Notes:**\n");
                        markdown.append("> ").append(notesText.toString().trim().replace("\n", "\n> "));
                        markdown.append("\n\n");
                    }
                }
            }
            
        } catch (Exception e) {
            context.addWarning("Failed to extract speaker notes: " + e.getMessage());
            logger.debug("Speaker notes extraction error", e);
        }
    }
}
