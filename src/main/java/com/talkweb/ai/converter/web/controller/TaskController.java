package com.talkweb.ai.converter.web.controller;

import com.talkweb.ai.converter.web.dto.ConversionRequest;
import com.talkweb.ai.converter.web.dto.TaskProgressResponse;
import com.talkweb.ai.converter.web.dto.TaskResponse;
import com.talkweb.ai.converter.web.model.TaskStatus;
import com.talkweb.ai.converter.web.service.ConversionTaskService;
import com.talkweb.ai.converter.web.service.DocumentConversionService;
import com.talkweb.ai.converter.web.service.TaskProgressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for task management operations
 */
@RestController
@RequestMapping("/api/v1/tasks")
@Profile("server")
@Tag(name = "Tasks", description = "Document conversion task management")
public class TaskController {

    private static final Logger logger = LoggerFactory.getLogger(TaskController.class);

    private final DocumentConversionService conversionService;
    private final ConversionTaskService taskService;
    private final TaskProgressService progressService;

    @Autowired
    public TaskController(
            DocumentConversionService conversionService,
            ConversionTaskService taskService,
            TaskProgressService progressService) {
        this.conversionService = conversionService;
        this.taskService = taskService;
        this.progressService = progressService;
    }

    @Operation(summary = "Create a new conversion task", 
               description = "Upload a document and create a conversion task")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Task created successfully",
                    content = @Content(schema = @Schema(implementation = TaskResponse.class))),
        @ApiResponse(responseCode = "400", description = "Invalid request or file validation failed"),
        @ApiResponse(responseCode = "413", description = "File too large"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<TaskResponse> createTask(
            @Valid @ModelAttribute ConversionRequest request) {
        
        logger.info("Creating new conversion task for file: {}", 
            request.getFile().getOriginalFilename());
        
        TaskResponse response = conversionService.submitConversion(request);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "Get all tasks", 
               description = "Retrieve a paginated list of all conversion tasks")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Tasks retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid pagination parameters")
    })
    @GetMapping
    public ResponseEntity<Page<TaskResponse>> getAllTasks(
            @Parameter(description = "Page number (0-based)") 
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Page size") 
            @RequestParam(defaultValue = "20") int size,
            
            @Parameter(description = "Sort field") 
            @RequestParam(defaultValue = "createdAt") String sortBy,
            
            @Parameter(description = "Sort direction (asc/desc)") 
            @RequestParam(defaultValue = "desc") String sortDir,
            
            @Parameter(description = "Filter by status") 
            @RequestParam(required = false) TaskStatus status) {
        
        logger.debug("Getting tasks - page: {}, size: {}, sortBy: {}, sortDir: {}, status: {}", 
            page, size, sortBy, sortDir, status);
        
        Page<TaskResponse> tasks;
        
        if (status != null) {
            tasks = taskService.getTasksByStatus(status, page, size);
        } else {
            tasks = taskService.getAllTasks(page, size, sortBy, sortDir);
        }
        
        return ResponseEntity.ok(tasks);
    }

    @Operation(summary = "Get task by ID", 
               description = "Retrieve detailed information about a specific task")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Task found",
                    content = @Content(schema = @Schema(implementation = TaskResponse.class))),
        @ApiResponse(responseCode = "404", description = "Task not found")
    })
    @GetMapping("/{taskId}")
    public ResponseEntity<TaskResponse> getTask(
            @Parameter(description = "Task ID") 
            @PathVariable String taskId) {
        
        logger.debug("Getting task: {}", taskId);
        
        TaskResponse task = taskService.getTaskResponse(taskId);
        return ResponseEntity.ok(task);
    }

    @Operation(summary = "Get task progress", 
               description = "Get real-time progress information for a task")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Progress retrieved successfully",
                    content = @Content(schema = @Schema(implementation = TaskProgressResponse.class))),
        @ApiResponse(responseCode = "404", description = "Task not found")
    })
    @GetMapping("/{taskId}/progress")
    public ResponseEntity<TaskProgressResponse> getTaskProgress(
            @Parameter(description = "Task ID") 
            @PathVariable String taskId) {
        
        logger.debug("Getting progress for task: {}", taskId);
        
        TaskProgressResponse progress = progressService.getProgress(taskId);
        return ResponseEntity.ok(progress);
    }

    @Operation(summary = "Cancel task", 
               description = "Cancel a running or pending conversion task")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Task cancelled successfully",
                    content = @Content(schema = @Schema(implementation = TaskResponse.class))),
        @ApiResponse(responseCode = "404", description = "Task not found"),
        @ApiResponse(responseCode = "400", description = "Task cannot be cancelled")
    })
    @DeleteMapping("/{taskId}")
    public ResponseEntity<TaskResponse> cancelTask(
            @Parameter(description = "Task ID") 
            @PathVariable String taskId) {
        
        logger.info("Cancelling task: {}", taskId);
        
        TaskResponse task = conversionService.cancelConversion(taskId);
        return ResponseEntity.ok(task);
    }

    @Operation(summary = "Delete task", 
               description = "Permanently delete a task and its associated files")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Task deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Task not found"),
        @ApiResponse(responseCode = "400", description = "Task cannot be deleted")
    })
    @DeleteMapping("/{taskId}/delete")
    public ResponseEntity<Void> deleteTask(
            @Parameter(description = "Task ID") 
            @PathVariable String taskId) {
        
        logger.info("Deleting task: {}", taskId);
        
        conversionService.deleteTask(taskId);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Get tasks by status", 
               description = "Retrieve tasks filtered by their status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Tasks retrieved successfully")
    })
    @GetMapping("/status/{status}")
    public ResponseEntity<List<TaskResponse>> getTasksByStatus(
            @Parameter(description = "Task status") 
            @PathVariable TaskStatus status) {
        
        logger.debug("Getting tasks with status: {}", status);
        
        List<TaskResponse> tasks = taskService.getTasksByStatus(status);
        return ResponseEntity.ok(tasks);
    }

    @Operation(summary = "Retry failed task", 
               description = "Retry a failed conversion task")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Task retry initiated",
                    content = @Content(schema = @Schema(implementation = TaskResponse.class))),
        @ApiResponse(responseCode = "404", description = "Task not found"),
        @ApiResponse(responseCode = "400", description = "Task cannot be retried")
    })
    @PostMapping("/{taskId}/retry")
    public ResponseEntity<TaskResponse> retryTask(
            @Parameter(description = "Task ID") 
            @PathVariable String taskId) {
        
        logger.info("Retrying task: {}", taskId);
        
        // Get the current task
        TaskResponse currentTask = taskService.getTaskResponse(taskId);
        
        // Check if task can be retried
        if (currentTask.getStatus() != TaskStatus.FAILED) {
            throw new IllegalStateException("Only failed tasks can be retried");
        }
        
        // Reset task status to pending and restart conversion
        taskService.updateTaskStatus(taskId, TaskStatus.PENDING);
        
        // This would need to be implemented to restart the conversion
        // For now, we'll just return the updated task
        TaskResponse updatedTask = taskService.getTaskResponse(taskId);
        
        return ResponseEntity.ok(updatedTask);
    }

    @Operation(summary = "Get task statistics", 
               description = "Get statistics about conversion tasks")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully")
    })
    @GetMapping("/statistics")
    public ResponseEntity<ConversionTaskService.TaskStatistics> getTaskStatistics() {
        
        logger.debug("Getting task statistics");
        
        ConversionTaskService.TaskStatistics stats = taskService.getTaskStatistics();
        return ResponseEntity.ok(stats);
    }
}
