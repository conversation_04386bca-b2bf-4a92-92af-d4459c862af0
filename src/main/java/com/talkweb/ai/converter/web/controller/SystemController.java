package com.talkweb.ai.converter.web.controller;

import com.talkweb.ai.converter.core.PluginManager;
import com.talkweb.ai.converter.web.service.DocumentConversionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.*;

/**
 * REST Controller for system management and information
 */
@RestController
@RequestMapping("/api/v1/system")
@Profile("server")
@Tag(name = "System", description = "System information and monitoring")
public class SystemController {

    private static final Logger logger = LoggerFactory.getLogger(SystemController.class);

    private final DocumentConversionService conversionService;
    private final PluginManager pluginManager;

    @Autowired
    public SystemController(
            DocumentConversionService conversionService,
            PluginManager pluginManager) {
        this.conversionService = conversionService;
        this.pluginManager = pluginManager;
    }

    @Operation(summary = "Get system status", 
               description = "Get overall system health and status information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "System status retrieved successfully",
                    content = @Content(schema = @Schema(implementation = SystemStatus.class)))
    })
    @GetMapping("/status")
    public ResponseEntity<SystemStatus> getSystemStatus() {
        logger.debug("Getting system status");
        
        try {
            // Get system statistics
            DocumentConversionService.SystemStatistics stats = conversionService.getSystemStatistics();
            
            // Get memory information
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            // Calculate memory usage percentage
            double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
            
            // Get available processors
            int availableProcessors = runtime.availableProcessors();
            
            // Determine system health
            SystemHealth health = determineSystemHealth(memoryUsagePercent, stats.getRunningTasks());
            
            SystemStatus status = new SystemStatus(
                health,
                LocalDateTime.now(),
                "Document Converter Web Service",
                "1.0.0",
                stats.getTotalTasks(),
                stats.getTasksToday(),
                stats.getCompletedToday(),
                stats.getRunningTasks(),
                new MemoryInfo(totalMemory, freeMemory, usedMemory, maxMemory, memoryUsagePercent),
                availableProcessors,
                System.getProperty("java.version"),
                System.getProperty("os.name") + " " + System.getProperty("os.version")
            );
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            logger.error("Error getting system status: {}", e.getMessage(), e);
            
            SystemStatus errorStatus = new SystemStatus(
                SystemHealth.ERROR,
                LocalDateTime.now(),
                "Document Converter Web Service",
                "1.0.0",
                0, 0, 0, 0,
                null, 0, null, null
            );
            
            return ResponseEntity.ok(errorStatus);
        }
    }

    @Operation(summary = "Get supported formats", 
               description = "Get list of supported document formats for conversion")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Supported formats retrieved successfully")
    })
    @GetMapping("/formats")
    public ResponseEntity<SupportedFormats> getSupportedFormats() {
        logger.debug("Getting supported formats");
        
        // Define supported input formats
        List<FormatInfo> inputFormats = Arrays.asList(
            new FormatInfo("pdf", "Portable Document Format", "application/pdf", true),
            new FormatInfo("docx", "Microsoft Word Document", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", true),
            new FormatInfo("doc", "Microsoft Word 97-2003 Document", "application/msword", true),
            new FormatInfo("xlsx", "Microsoft Excel Spreadsheet", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", true),
            new FormatInfo("xls", "Microsoft Excel 97-2003 Spreadsheet", "application/vnd.ms-excel", true),
            new FormatInfo("pptx", "Microsoft PowerPoint Presentation", "application/vnd.openxmlformats-officedocument.presentationml.presentation", true),
            new FormatInfo("ppt", "Microsoft PowerPoint 97-2003 Presentation", "application/vnd.ms-powerpoint", true),
            new FormatInfo("html", "HyperText Markup Language", "text/html", true),
            new FormatInfo("htm", "HyperText Markup Language", "text/html", true),
            new FormatInfo("txt", "Plain Text", "text/plain", true),
            new FormatInfo("rtf", "Rich Text Format", "application/rtf", true),
            new FormatInfo("odt", "OpenDocument Text", "application/vnd.oasis.opendocument.text", true),
            new FormatInfo("png", "Portable Network Graphics", "image/png", true),
            new FormatInfo("jpg", "JPEG Image", "image/jpeg", true),
            new FormatInfo("jpeg", "JPEG Image", "image/jpeg", true),
            new FormatInfo("tiff", "Tagged Image File Format", "image/tiff", true),
            new FormatInfo("tif", "Tagged Image File Format", "image/tiff", true),
            new FormatInfo("bmp", "Bitmap Image", "image/bmp", true),
            new FormatInfo("gif", "Graphics Interchange Format", "image/gif", true)
        );
        
        // Define output formats
        List<FormatInfo> outputFormats = Arrays.asList(
            new FormatInfo("md", "Markdown", "text/markdown", true)
        );
        
        SupportedFormats formats = new SupportedFormats(inputFormats, outputFormats);
        
        return ResponseEntity.ok(formats);
    }

    @Operation(summary = "Get plugin information", 
               description = "Get information about loaded plugins and processors")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Plugin information retrieved successfully")
    })
    @GetMapping("/plugins")
    public ResponseEntity<PluginInfo> getPluginInfo() {
        logger.debug("Getting plugin information");
        
        try {
            // Get plugin information from plugin manager
            // This is a simplified implementation
            List<PluginDetails> plugins = new ArrayList<>();
            
            // Add built-in processors
            plugins.add(new PluginDetails("pdf-processor", "PDF Document Processor", "1.0.0", true, "Built-in processor for PDF documents"));
            plugins.add(new PluginDetails("word-processor", "Microsoft Word Processor", "1.0.0", true, "Built-in processor for Word documents"));
            plugins.add(new PluginDetails("excel-processor", "Microsoft Excel Processor", "1.0.0", true, "Built-in processor for Excel spreadsheets"));
            plugins.add(new PluginDetails("powerpoint-processor", "Microsoft PowerPoint Processor", "1.0.0", true, "Built-in processor for PowerPoint presentations"));
            plugins.add(new PluginDetails("html-processor", "HTML Document Processor", "1.0.0", true, "Built-in processor for HTML documents"));
            plugins.add(new PluginDetails("text-processor", "Plain Text Processor", "1.0.0", true, "Built-in processor for plain text files"));
            plugins.add(new PluginDetails("rtf-processor", "Rich Text Format Processor", "1.0.0", true, "Built-in processor for RTF documents"));
            plugins.add(new PluginDetails("odt-processor", "OpenDocument Text Processor", "1.0.0", true, "Built-in processor for ODT documents"));
            plugins.add(new PluginDetails("image-processor", "Image OCR Processor", "1.0.0", true, "Built-in processor for image files with OCR"));
            
            PluginInfo pluginInfo = new PluginInfo(plugins.size(), plugins);
            
            return ResponseEntity.ok(pluginInfo);
            
        } catch (Exception e) {
            logger.error("Error getting plugin information: {}", e.getMessage(), e);
            
            PluginInfo errorInfo = new PluginInfo(0, Collections.emptyList());
            return ResponseEntity.ok(errorInfo);
        }
    }

    @Operation(summary = "Get system metrics", 
               description = "Get detailed system performance metrics")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "System metrics retrieved successfully")
    })
    @GetMapping("/metrics")
    public ResponseEntity<SystemMetrics> getSystemMetrics() {
        logger.debug("Getting system metrics");
        
        try {
            DocumentConversionService.SystemStatistics stats = conversionService.getSystemStatistics();
            
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            SystemMetrics metrics = new SystemMetrics(
                stats.getTotalTasks(),
                stats.getTasksToday(),
                stats.getCompletedToday(),
                stats.getRunningTasks(),
                stats.getAverageProcessingTimeMs(),
                usedMemory,
                totalMemory,
                maxMemory,
                runtime.availableProcessors(),
                System.currentTimeMillis()
            );
            
            return ResponseEntity.ok(metrics);
            
        } catch (Exception e) {
            logger.error("Error getting system metrics: {}", e.getMessage(), e);
            throw e;
        }
    }

    // Helper methods
    private SystemHealth determineSystemHealth(double memoryUsagePercent, int runningTasks) {
        if (memoryUsagePercent > 90) {
            return SystemHealth.CRITICAL;
        } else if (memoryUsagePercent > 80 || runningTasks > 50) {
            return SystemHealth.WARNING;
        } else {
            return SystemHealth.HEALTHY;
        }
    }

    // Inner classes for response DTOs
    public enum SystemHealth {
        HEALTHY, WARNING, CRITICAL, ERROR
    }

    public static class SystemStatus {
        private final SystemHealth health;
        private final LocalDateTime timestamp;
        private final String serviceName;
        private final String version;
        private final long totalTasks;
        private final long tasksToday;
        private final long completedToday;
        private final int runningTasks;
        private final MemoryInfo memory;
        private final int availableProcessors;
        private final String javaVersion;
        private final String osInfo;

        public SystemStatus(SystemHealth health, LocalDateTime timestamp, String serviceName, String version,
                          long totalTasks, long tasksToday, long completedToday, int runningTasks,
                          MemoryInfo memory, int availableProcessors, String javaVersion, String osInfo) {
            this.health = health;
            this.timestamp = timestamp;
            this.serviceName = serviceName;
            this.version = version;
            this.totalTasks = totalTasks;
            this.tasksToday = tasksToday;
            this.completedToday = completedToday;
            this.runningTasks = runningTasks;
            this.memory = memory;
            this.availableProcessors = availableProcessors;
            this.javaVersion = javaVersion;
            this.osInfo = osInfo;
        }

        // Getters
        public SystemHealth getHealth() { return health; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public String getServiceName() { return serviceName; }
        public String getVersion() { return version; }
        public long getTotalTasks() { return totalTasks; }
        public long getTasksToday() { return tasksToday; }
        public long getCompletedToday() { return completedToday; }
        public int getRunningTasks() { return runningTasks; }
        public MemoryInfo getMemory() { return memory; }
        public int getAvailableProcessors() { return availableProcessors; }
        public String getJavaVersion() { return javaVersion; }
        public String getOsInfo() { return osInfo; }
    }

    public static class MemoryInfo {
        private final long total;
        private final long free;
        private final long used;
        private final long max;
        private final double usagePercent;

        public MemoryInfo(long total, long free, long used, long max, double usagePercent) {
            this.total = total;
            this.free = free;
            this.used = used;
            this.max = max;
            this.usagePercent = usagePercent;
        }

        // Getters
        public long getTotal() { return total; }
        public long getFree() { return free; }
        public long getUsed() { return used; }
        public long getMax() { return max; }
        public double getUsagePercent() { return usagePercent; }
    }

    public static class FormatInfo {
        private final String extension;
        private final String description;
        private final String mimeType;
        private final boolean supported;

        public FormatInfo(String extension, String description, String mimeType, boolean supported) {
            this.extension = extension;
            this.description = description;
            this.mimeType = mimeType;
            this.supported = supported;
        }

        // Getters
        public String getExtension() { return extension; }
        public String getDescription() { return description; }
        public String getMimeType() { return mimeType; }
        public boolean isSupported() { return supported; }
    }

    public static class SupportedFormats {
        private final List<FormatInfo> inputFormats;
        private final List<FormatInfo> outputFormats;

        public SupportedFormats(List<FormatInfo> inputFormats, List<FormatInfo> outputFormats) {
            this.inputFormats = inputFormats;
            this.outputFormats = outputFormats;
        }

        // Getters
        public List<FormatInfo> getInputFormats() { return inputFormats; }
        public List<FormatInfo> getOutputFormats() { return outputFormats; }
    }

    public static class PluginDetails {
        private final String id;
        private final String name;
        private final String version;
        private final boolean enabled;
        private final String description;

        public PluginDetails(String id, String name, String version, boolean enabled, String description) {
            this.id = id;
            this.name = name;
            this.version = version;
            this.enabled = enabled;
            this.description = description;
        }

        // Getters
        public String getId() { return id; }
        public String getName() { return name; }
        public String getVersion() { return version; }
        public boolean isEnabled() { return enabled; }
        public String getDescription() { return description; }
    }

    public static class PluginInfo {
        private final int totalPlugins;
        private final List<PluginDetails> plugins;

        public PluginInfo(int totalPlugins, List<PluginDetails> plugins) {
            this.totalPlugins = totalPlugins;
            this.plugins = plugins;
        }

        // Getters
        public int getTotalPlugins() { return totalPlugins; }
        public List<PluginDetails> getPlugins() { return plugins; }
    }

    public static class SystemMetrics {
        private final long totalTasks;
        private final long tasksToday;
        private final long completedToday;
        private final int runningTasks;
        private final double averageProcessingTimeMs;
        private final long memoryUsed;
        private final long memoryTotal;
        private final long memoryMax;
        private final int cpuCores;
        private final long timestamp;

        public SystemMetrics(long totalTasks, long tasksToday, long completedToday, int runningTasks,
                           double averageProcessingTimeMs, long memoryUsed, long memoryTotal, long memoryMax,
                           int cpuCores, long timestamp) {
            this.totalTasks = totalTasks;
            this.tasksToday = tasksToday;
            this.completedToday = completedToday;
            this.runningTasks = runningTasks;
            this.averageProcessingTimeMs = averageProcessingTimeMs;
            this.memoryUsed = memoryUsed;
            this.memoryTotal = memoryTotal;
            this.memoryMax = memoryMax;
            this.cpuCores = cpuCores;
            this.timestamp = timestamp;
        }

        // Getters
        public long getTotalTasks() { return totalTasks; }
        public long getTasksToday() { return tasksToday; }
        public long getCompletedToday() { return completedToday; }
        public int getRunningTasks() { return runningTasks; }
        public double getAverageProcessingTimeMs() { return averageProcessingTimeMs; }
        public long getMemoryUsed() { return memoryUsed; }
        public long getMemoryTotal() { return memoryTotal; }
        public long getMemoryMax() { return memoryMax; }
        public int getCpuCores() { return cpuCores; }
        public long getTimestamp() { return timestamp; }
    }
}
