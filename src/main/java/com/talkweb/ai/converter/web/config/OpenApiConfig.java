package com.talkweb.ai.converter.web.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.List;

/**
 * OpenAPI/Swagger configuration for the Document Converter API
 */
@Configuration
@Profile("server")
public class OpenApiConfig {

    @Value("${server.port:8080}")
    private int serverPort;

    @Value("${server.servlet.context-path:/doc-converter}")
    private String contextPath;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Document Converter API")
                        .description("REST API for converting documents to Markdown format. " +
                                   "Supports multiple document formats including PDF, Word, Excel, PowerPoint, HTML, and images with OCR.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Document Converter Team")
                                .email("<EMAIL>")
                                .url("https://github.com/talkweb/doc-converter"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Development Server"),
                        new Server()
                                .url("https://api.example.com" + contextPath)
                                .description("Production Server")))
                .tags(List.of(
                        new Tag()
                                .name("Tasks")
                                .description("Document conversion task management"),
                        new Tag()
                                .name("Files")
                                .description("File upload and download operations"),
                        new Tag()
                                .name("System")
                                .description("System information and monitoring"),
                        new Tag()
                                .name("WebSocket")
                                .description("Real-time task status updates")));
    }
}
