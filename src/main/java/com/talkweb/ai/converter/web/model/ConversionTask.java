package com.talkweb.ai.converter.web.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Entity representing a document conversion task
 */
@Entity
@Table(name = "conversion_tasks", indexes = {
    @Index(name = "idx_task_status", columnList = "status"),
    @Index(name = "idx_task_created_at", columnList = "createdAt"),
    @Index(name = "idx_task_status_created", columnList = "status,createdAt")
})
public class ConversionTask {

    @Id
    @Column(name = "task_id", length = 36)
    private String taskId;

    @NotBlank
    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;

    @NotBlank
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;

    @Column(name = "target_file_name", length = 255)
    private String targetFileName;

    @Column(name = "target_path", length = 500)
    private String targetPath;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private TaskStatus status;

    @Column(name = "progress")
    private Integer progress = 0;

    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    @Column(name = "file_size")
    private Long fileSize;

    @Column(name = "file_type", length = 50)
    private String fileType;

    @Column(name = "conversion_options", columnDefinition = "TEXT")
    private String conversionOptions;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "started_at")
    private LocalDateTime startedAt;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "processing_time_ms")
    private Long processingTimeMs;

    @Column(name = "result_file_size")
    private Long resultFileSize;

    // Default constructor
    public ConversionTask() {}

    // Constructor with required fields
    public ConversionTask(String taskId, String fileName, String filePath) {
        this.taskId = taskId;
        this.fileName = fileName;
        this.filePath = filePath;
        this.status = TaskStatus.PENDING;
        this.progress = 0;
    }

    // Getters and setters
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getTargetFileName() {
        return targetFileName;
    }

    public void setTargetFileName(String targetFileName) {
        this.targetFileName = targetFileName;
    }

    public String getTargetPath() {
        return targetPath;
    }

    public void setTargetPath(String targetPath) {
        this.targetPath = targetPath;
    }

    public TaskStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getConversionOptions() {
        return conversionOptions;
    }

    public void setConversionOptions(String conversionOptions) {
        this.conversionOptions = conversionOptions;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public Long getResultFileSize() {
        return resultFileSize;
    }

    public void setResultFileSize(Long resultFileSize) {
        this.resultFileSize = resultFileSize;
    }

    // Utility methods
    public void markAsStarted() {
        this.status = TaskStatus.PROCESSING;
        this.startedAt = LocalDateTime.now();
        this.progress = 0;
    }

    public void markAsCompleted(String resultPath, Long resultSize) {
        this.status = TaskStatus.COMPLETED;
        this.completedAt = LocalDateTime.now();
        this.progress = 100;
        this.targetPath = resultPath;
        this.resultFileSize = resultSize;
        
        if (this.startedAt != null) {
            this.processingTimeMs = java.time.Duration.between(this.startedAt, this.completedAt).toMillis();
        }
    }

    public void markAsFailed(String errorMessage) {
        this.status = TaskStatus.FAILED;
        this.completedAt = LocalDateTime.now();
        this.errorMessage = errorMessage;
        
        if (this.startedAt != null) {
            this.processingTimeMs = java.time.Duration.between(this.startedAt, this.completedAt).toMillis();
        }
    }

    public void markAsCancelled() {
        this.status = TaskStatus.CANCELLED;
        this.completedAt = LocalDateTime.now();
        
        if (this.startedAt != null) {
            this.processingTimeMs = java.time.Duration.between(this.startedAt, this.completedAt).toMillis();
        }
    }

    public void updateProgress(int progress) {
        this.progress = Math.max(0, Math.min(100, progress));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConversionTask that = (ConversionTask) o;
        return Objects.equals(taskId, that.taskId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(taskId);
    }

    @Override
    public String toString() {
        return "ConversionTask{" +
                "taskId='" + taskId + '\'' +
                ", fileName='" + fileName + '\'' +
                ", status=" + status +
                ", progress=" + progress +
                ", createdAt=" + createdAt +
                '}';
    }
}
