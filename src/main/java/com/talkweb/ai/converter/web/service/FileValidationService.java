package com.talkweb.ai.converter.web.service;

import com.talkweb.ai.converter.web.exception.ConversionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Set;

/**
 * Service for validating uploaded files
 */
@Service
@Profile("server")
public class FileValidationService {

    private static final Logger logger = LoggerFactory.getLogger(FileValidationService.class);

    // File signature validation (magic numbers)
    private static final byte[] PDF_SIGNATURE = {0x25, 0x50, 0x44, 0x46}; // %PDF
    private static final byte[] PNG_SIGNATURE = {(byte) 0x89, 0x50, 0x4E, 0x47}; // PNG
    private static final byte[] JPEG_SIGNATURE = {(byte) 0xFF, (byte) 0xD8, (byte) 0xFF}; // JPEG
    private static final byte[] ZIP_SIGNATURE = {0x50, 0x4B, 0x03, 0x04}; // ZIP (used by DOCX, XLSX, PPTX)
    private static final byte[] GIF_SIGNATURE = {0x47, 0x49, 0x46, 0x38}; // GIF8
    private static final byte[] TIFF_SIGNATURE_LE = {0x49, 0x49, 0x2A, 0x00}; // TIFF little-endian
    private static final byte[] TIFF_SIGNATURE_BE = {0x4D, 0x4D, 0x00, 0x2A}; // TIFF big-endian
    private static final byte[] BMP_SIGNATURE = {0x42, 0x4D}; // BM

    @Value("${doc-converter.web.security.max-upload-size:104857600}")
    private long maxUploadSize;

    @Value("${doc-converter.web.security.allowed-file-types}")
    private Set<String> allowedFileTypes;

    @Value("${doc-converter.web.security.blocked-file-types}")
    private Set<String> blockedFileTypes;

    /**
     * Validate uploaded file
     */
    public ValidationResult validateFile(MultipartFile file) {
        ValidationResult result = new ValidationResult();

        try {
            // Basic validations
            validateBasicProperties(file, result);
            
            if (!result.isValid()) {
                return result;
            }

            // File extension validation
            validateFileExtension(file, result);
            
            if (!result.isValid()) {
                return result;
            }

            // File signature validation
            validateFileSignature(file, result);
            
            if (!result.isValid()) {
                return result;
            }

            // Content validation
            validateFileContent(file, result);

            logger.debug("File validation completed for {}: {}", 
                file.getOriginalFilename(), result.isValid() ? "PASSED" : "FAILED");

        } catch (Exception e) {
            logger.error("Error during file validation: {}", e.getMessage(), e);
            result.addError("File validation failed: " + e.getMessage());
        }

        return result;
    }

    /**
     * Validate basic file properties
     */
    private void validateBasicProperties(MultipartFile file, ValidationResult result) {
        // Check if file is empty
        if (file.isEmpty()) {
            result.addError("File is empty");
            return;
        }

        // Check file size
        if (file.getSize() > maxUploadSize) {
            result.addError(String.format("File size (%d bytes) exceeds maximum allowed size (%d bytes)", 
                file.getSize(), maxUploadSize));
            return;
        }

        // Check filename
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            result.addError("File name is required");
            return;
        }

        // Check for suspicious filenames
        if (containsSuspiciousCharacters(originalFilename)) {
            result.addError("File name contains suspicious characters");
            return;
        }

        result.setFileSize(file.getSize());
        result.setOriginalFilename(originalFilename);
        result.setContentType(file.getContentType());
    }

    /**
     * Validate file extension
     */
    private void validateFileExtension(MultipartFile file, ValidationResult result) {
        String filename = file.getOriginalFilename();
        String extension = getFileExtension(filename).toLowerCase();

        if (extension.isEmpty()) {
            result.addError("File must have an extension");
            return;
        }

        // Check blocked extensions
        if (blockedFileTypes.contains(extension)) {
            result.addError("File type not allowed: " + extension);
            return;
        }

        // Check allowed extensions
        if (!allowedFileTypes.contains(extension)) {
            result.addError("Unsupported file type: " + extension);
            return;
        }

        result.setFileExtension(extension);
    }

    /**
     * Validate file signature (magic numbers)
     */
    private void validateFileSignature(MultipartFile file, ValidationResult result) {
        try (InputStream inputStream = file.getInputStream()) {
            byte[] header = new byte[8];
            int bytesRead = inputStream.read(header);
            
            if (bytesRead < 4) {
                result.addError("File is too small to validate");
                return;
            }

            String extension = result.getFileExtension();
            boolean signatureValid = false;

            switch (extension) {
                case "pdf":
                    signatureValid = Arrays.equals(Arrays.copyOf(header, 4), PDF_SIGNATURE);
                    break;
                case "png":
                    signatureValid = Arrays.equals(Arrays.copyOf(header, 4), PNG_SIGNATURE);
                    break;
                case "jpg", "jpeg":
                    signatureValid = Arrays.equals(Arrays.copyOf(header, 3), JPEG_SIGNATURE);
                    break;
                case "gif":
                    signatureValid = Arrays.equals(Arrays.copyOf(header, 4), GIF_SIGNATURE);
                    break;
                case "tiff", "tif":
                    signatureValid = Arrays.equals(Arrays.copyOf(header, 4), TIFF_SIGNATURE_LE) ||
                                   Arrays.equals(Arrays.copyOf(header, 4), TIFF_SIGNATURE_BE);
                    break;
                case "bmp":
                    signatureValid = Arrays.equals(Arrays.copyOf(header, 2), BMP_SIGNATURE);
                    break;
                case "docx", "xlsx", "pptx":
                    // These are ZIP files
                    signatureValid = Arrays.equals(Arrays.copyOf(header, 4), ZIP_SIGNATURE);
                    break;
                case "txt", "html", "htm", "rtf":
                    // Text files - no specific signature validation
                    signatureValid = true;
                    break;
                default:
                    // For other types, skip signature validation
                    signatureValid = true;
                    break;
            }

            if (!signatureValid) {
                result.addError("File signature does not match the file extension");
            }

        } catch (IOException e) {
            result.addError("Failed to read file for signature validation: " + e.getMessage());
        }
    }

    /**
     * Validate file content
     */
    private void validateFileContent(MultipartFile file, ValidationResult result) {
        String extension = result.getFileExtension();
        
        try (InputStream inputStream = file.getInputStream()) {
            // Read first few KB for content analysis
            byte[] content = new byte[Math.min(8192, (int) file.getSize())];
            int bytesRead = inputStream.read(content);
            
            // Check for embedded executables or scripts
            if (containsSuspiciousContent(content, bytesRead)) {
                result.addError("File contains suspicious content");
                return;
            }

            // Specific validations based on file type
            switch (extension) {
                case "txt", "html", "htm":
                    validateTextContent(content, bytesRead, result);
                    break;
                case "pdf":
                    validatePdfContent(content, bytesRead, result);
                    break;
                default:
                    // No specific content validation for other types
                    break;
            }

        } catch (IOException e) {
            result.addError("Failed to read file content for validation: " + e.getMessage());
        }
    }

    /**
     * Validate text file content
     */
    private void validateTextContent(byte[] content, int length, ValidationResult result) {
        String text = new String(content, 0, length);
        
        // Check for suspicious scripts
        String lowerText = text.toLowerCase();
        if (lowerText.contains("<script") || lowerText.contains("javascript:") || 
            lowerText.contains("vbscript:") || lowerText.contains("data:")) {
            result.addWarning("File contains script content");
        }
    }

    /**
     * Validate PDF content
     */
    private void validatePdfContent(byte[] content, int length, ValidationResult result) {
        String text = new String(content, 0, length);
        
        // Check for PDF version
        if (text.startsWith("%PDF-")) {
            String version = text.substring(5, Math.min(8, text.length()));
            result.addInfo("PDF version: " + version);
        }
        
        // Check for suspicious PDF features
        if (text.contains("/JavaScript") || text.contains("/JS")) {
            result.addWarning("PDF contains JavaScript");
        }
        
        if (text.contains("/Launch") || text.contains("/URI")) {
            result.addWarning("PDF contains external links or launch actions");
        }
    }

    // Helper methods
    private String getFileExtension(String filename) {
        if (filename == null) return "";
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    private boolean containsSuspiciousCharacters(String filename) {
        // Check for path traversal attempts
        if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
            return true;
        }
        
        // Check for null bytes
        if (filename.contains("\0")) {
            return true;
        }
        
        // Check for control characters
        for (char c : filename.toCharArray()) {
            if (Character.isISOControl(c)) {
                return true;
            }
        }
        
        return false;
    }

    private boolean containsSuspiciousContent(byte[] content, int length) {
        // Check for executable signatures
        if (length >= 2) {
            // Check for MZ header (Windows executable)
            if (content[0] == 0x4D && content[1] == 0x5A) {
                return true;
            }
        }
        
        if (length >= 4) {
            // Check for ELF header (Linux executable)
            if (content[0] == 0x7F && content[1] == 0x45 && 
                content[2] == 0x4C && content[3] == 0x46) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Validation result class
     */
    public static class ValidationResult {
        private boolean valid = true;
        private String originalFilename;
        private String fileExtension;
        private String contentType;
        private long fileSize;
        private java.util.List<String> errors = new java.util.ArrayList<>();
        private java.util.List<String> warnings = new java.util.ArrayList<>();
        private java.util.List<String> info = new java.util.ArrayList<>();

        public boolean isValid() { return valid && errors.isEmpty(); }
        
        public void addError(String error) {
            this.errors.add(error);
            this.valid = false;
        }
        
        public void addWarning(String warning) {
            this.warnings.add(warning);
        }
        
        public void addInfo(String info) {
            this.info.add(info);
        }

        // Getters and setters
        public String getOriginalFilename() { return originalFilename; }
        public void setOriginalFilename(String originalFilename) { this.originalFilename = originalFilename; }
        
        public String getFileExtension() { return fileExtension; }
        public void setFileExtension(String fileExtension) { this.fileExtension = fileExtension; }
        
        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public java.util.List<String> getErrors() { return errors; }
        public java.util.List<String> getWarnings() { return warnings; }
        public java.util.List<String> getInfo() { return info; }
    }
}
