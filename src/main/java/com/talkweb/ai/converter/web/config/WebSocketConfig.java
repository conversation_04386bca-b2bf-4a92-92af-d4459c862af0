package com.talkweb.ai.converter.web.config;

import com.talkweb.ai.converter.web.websocket.TaskProgressWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket configuration for real-time task updates
 */
@Configuration
@EnableWebSocket
@Profile("server")
public class WebSocketConfig implements WebSocketConfigurer {

    private final TaskProgressWebSocketHandler taskProgressHandler;

    @Autowired
    public WebSocketConfig(TaskProgressWebSocketHandler taskProgressHandler) {
        this.taskProgressHandler = taskProgressHandler;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // Register task progress WebSocket handler
        registry.addHandler(taskProgressHandler, "/ws/tasks")
                .setAllowedOrigins("*") // In production, specify allowed origins
                .withSockJS(); // Enable SockJS fallback for browsers that don't support WebSocket
        
        // Register task-specific progress handler
        registry.addHandler(taskProgressHandler, "/ws/tasks/{taskId}")
                .setAllowedOrigins("*")
                .withSockJS();
        
        // Register system-wide notifications handler
        registry.addHandler(taskProgressHandler, "/ws/system")
                .setAllowedOrigins("*")
                .withSockJS();
    }
}
