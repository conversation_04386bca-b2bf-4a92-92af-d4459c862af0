package com.talkweb.ai.converter.web.model;

/**
 * Enumeration representing the status of a conversion task
 */
public enum TaskStatus {
    /**
     * Task has been created and is waiting to be processed
     */
    PENDING("Pending", "Task is waiting to be processed"),
    
    /**
     * Task is currently being processed
     */
    PROCESSING("Processing", "Task is currently being processed"),
    
    /**
     * Task has been completed successfully
     */
    COMPLETED("Completed", "Task has been completed successfully"),
    
    /**
     * Task has failed due to an error
     */
    FAILED("Failed", "Task has failed due to an error"),
    
    /**
     * Task has been cancelled by user or system
     */
    CANCELLED("Cancelled", "Task has been cancelled");

    private final String displayName;
    private final String description;

    TaskStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Check if the task is in a terminal state (completed, failed, or cancelled)
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == CANCELLED;
    }

    /**
     * Check if the task is in progress
     */
    public boolean isInProgress() {
        return this == PROCESSING;
    }

    /**
     * Check if the task can be cancelled
     */
    public boolean isCancellable() {
        return this == PENDING || this == PROCESSING;
    }

    /**
     * Get the next possible statuses from current status
     */
    public TaskStatus[] getNextPossibleStatuses() {
        return switch (this) {
            case PENDING -> new TaskStatus[]{PROCESSING, CANCELLED};
            case PROCESSING -> new TaskStatus[]{COMPLETED, FAILED, CANCELLED};
            case COMPLETED, FAILED, CANCELLED -> new TaskStatus[]{};
        };
    }
}
