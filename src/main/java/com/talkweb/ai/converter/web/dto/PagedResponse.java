package com.talkweb.ai.converter.web.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * Generic paged response wrapper for API responses
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PagedResponse<T> {
    
    private List<T> content;
    private PageInfo page;
    private SortInfo sort;

    // Default constructor
    public PagedResponse() {}

    // Constructor from Spring Data Page
    public PagedResponse(Page<T> page) {
        this.content = page.getContent();
        this.page = new PageInfo(
            page.getNumber(),
            page.getSize(),
            page.getTotalElements(),
            page.getTotalPages(),
            page.isFirst(),
            page.isLast(),
            page.hasNext(),
            page.hasPrevious()
        );
        
        if (page.getSort().isSorted()) {
            this.sort = new SortInfo(page.getSort());
        }
    }

    // Constructor with content and page info
    public PagedResponse(List<T> content, int pageNumber, int pageSize, long totalElements) {
        this.content = content;
        int totalPages = (int) Math.ceil((double) totalElements / pageSize);
        this.page = new PageInfo(
            pageNumber,
            pageSize,
            totalElements,
            totalPages,
            pageNumber == 0,
            pageNumber >= totalPages - 1,
            pageNumber < totalPages - 1,
            pageNumber > 0
        );
    }

    // Static factory method
    public static <T> PagedResponse<T> of(Page<T> page) {
        return new PagedResponse<>(page);
    }

    // Getters and setters
    public List<T> getContent() {
        return content;
    }

    public void setContent(List<T> content) {
        this.content = content;
    }

    public PageInfo getPage() {
        return page;
    }

    public void setPage(PageInfo page) {
        this.page = page;
    }

    public SortInfo getSort() {
        return sort;
    }

    public void setSort(SortInfo sort) {
        this.sort = sort;
    }

    /**
     * Page information
     */
    public static class PageInfo {
        private final int number;
        private final int size;
        private final long totalElements;
        private final int totalPages;
        private final boolean first;
        private final boolean last;
        private final boolean hasNext;
        private final boolean hasPrevious;

        public PageInfo(int number, int size, long totalElements, int totalPages,
                       boolean first, boolean last, boolean hasNext, boolean hasPrevious) {
            this.number = number;
            this.size = size;
            this.totalElements = totalElements;
            this.totalPages = totalPages;
            this.first = first;
            this.last = last;
            this.hasNext = hasNext;
            this.hasPrevious = hasPrevious;
        }

        // Getters
        public int getNumber() { return number; }
        public int getSize() { return size; }
        public long getTotalElements() { return totalElements; }
        public int getTotalPages() { return totalPages; }
        public boolean isFirst() { return first; }
        public boolean isLast() { return last; }
        public boolean isHasNext() { return hasNext; }
        public boolean isHasPrevious() { return hasPrevious; }
    }

    /**
     * Sort information
     */
    public static class SortInfo {
        private final boolean sorted;
        private final List<SortOrder> orders;

        public SortInfo(org.springframework.data.domain.Sort sort) {
            this.sorted = sort.isSorted();
            this.orders = sort.stream()
                .map(order -> new SortOrder(
                    order.getProperty(),
                    order.getDirection().name().toLowerCase(),
                    order.isIgnoreCase()
                ))
                .toList();
        }

        // Getters
        public boolean isSorted() { return sorted; }
        public List<SortOrder> getOrders() { return orders; }

        public static class SortOrder {
            private final String property;
            private final String direction;
            private final boolean ignoreCase;

            public SortOrder(String property, String direction, boolean ignoreCase) {
                this.property = property;
                this.direction = direction;
                this.ignoreCase = ignoreCase;
            }

            // Getters
            public String getProperty() { return property; }
            public String getDirection() { return direction; }
            public boolean isIgnoreCase() { return ignoreCase; }
        }
    }
}
