package com.talkweb.ai.converter.web.service;

import com.talkweb.ai.converter.web.dto.TaskProgressResponse;
import com.talkweb.ai.converter.web.dto.TaskResponse;
import com.talkweb.ai.converter.web.exception.TaskNotFoundException;
import com.talkweb.ai.converter.web.model.ConversionTask;
import com.talkweb.ai.converter.web.model.TaskStatus;
import com.talkweb.ai.converter.web.repository.ConversionTaskRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for managing conversion tasks
 */
@Service
@Profile("server")
@Transactional
public class ConversionTaskService {

    private static final Logger logger = LoggerFactory.getLogger(ConversionTaskService.class);

    private final ConversionTaskRepository taskRepository;

    @Autowired
    public ConversionTaskService(ConversionTaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }

    /**
     * Create a new conversion task
     */
    public ConversionTask createTask(String fileName, String filePath, Long fileSize, String fileType) {
        String taskId = UUID.randomUUID().toString();
        
        ConversionTask task = new ConversionTask(taskId, fileName, filePath);
        task.setFileSize(fileSize);
        task.setFileType(fileType);
        
        // Generate target file name if not provided
        if (task.getTargetFileName() == null) {
            task.setTargetFileName(generateTargetFileName(fileName));
        }
        
        ConversionTask savedTask = taskRepository.save(task);
        logger.info("Created new conversion task: {}", taskId);
        
        return savedTask;
    }

    /**
     * Create a new conversion task with options
     */
    public ConversionTask createTask(String fileName, String filePath, Long fileSize, String fileType, 
                                   String targetFileName, String conversionOptions) {
        ConversionTask task = createTask(fileName, filePath, fileSize, fileType);
        
        if (targetFileName != null && !targetFileName.trim().isEmpty()) {
            task.setTargetFileName(targetFileName);
        }
        
        task.setConversionOptions(conversionOptions);
        
        return taskRepository.save(task);
    }

    /**
     * Get task by ID
     */
    @Transactional(readOnly = true)
    public ConversionTask getTask(String taskId) {
        return taskRepository.findById(taskId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));
    }

    /**
     * Get task response DTO by ID
     */
    @Transactional(readOnly = true)
    public TaskResponse getTaskResponse(String taskId) {
        ConversionTask task = getTask(taskId);
        return TaskResponse.from(task);
    }

    /**
     * Get all tasks with pagination
     */
    @Transactional(readOnly = true)
    public Page<TaskResponse> getAllTasks(int page, int size, String sortBy, String sortDir) {
        Sort.Direction direction = "desc".equalsIgnoreCase(sortDir) ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        
        return taskRepository.findAll(pageable)
                .map(TaskResponse::from);
    }

    /**
     * Get tasks by status
     */
    @Transactional(readOnly = true)
    public List<TaskResponse> getTasksByStatus(TaskStatus status) {
        return taskRepository.findByStatus(status)
                .stream()
                .map(TaskResponse::from)
                .collect(Collectors.toList());
    }

    /**
     * Get tasks by status with pagination
     */
    @Transactional(readOnly = true)
    public Page<TaskResponse> getTasksByStatus(TaskStatus status, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return taskRepository.findByStatus(status, pageable)
                .map(TaskResponse::from);
    }

    /**
     * Update task status
     */
    public ConversionTask updateTaskStatus(String taskId, TaskStatus newStatus) {
        ConversionTask task = getTask(taskId);
        
        // Validate status transition
        if (!isValidStatusTransition(task.getStatus(), newStatus)) {
            throw new IllegalArgumentException(
                String.format("Invalid status transition from %s to %s", task.getStatus(), newStatus));
        }
        
        TaskStatus oldStatus = task.getStatus();
        task.setStatus(newStatus);
        
        // Update timestamps based on status
        switch (newStatus) {
            case PROCESSING:
                if (task.getStartedAt() == null) {
                    task.setStartedAt(LocalDateTime.now());
                }
                break;
            case COMPLETED:
            case FAILED:
            case CANCELLED:
                if (task.getCompletedAt() == null) {
                    task.setCompletedAt(LocalDateTime.now());
                    if (task.getStartedAt() != null) {
                        long processingTime = java.time.Duration.between(task.getStartedAt(), task.getCompletedAt()).toMillis();
                        task.setProcessingTimeMs(processingTime);
                    }
                }
                break;
        }
        
        ConversionTask savedTask = taskRepository.save(task);
        logger.info("Updated task {} status from {} to {}", taskId, oldStatus, newStatus);
        
        return savedTask;
    }

    /**
     * Update task progress
     */
    public ConversionTask updateTaskProgress(String taskId, int progress) {
        ConversionTask task = getTask(taskId);
        task.updateProgress(progress);
        
        ConversionTask savedTask = taskRepository.save(task);
        logger.debug("Updated task {} progress to {}%", taskId, progress);
        
        return savedTask;
    }

    /**
     * Update task progress with message
     */
    public ConversionTask updateTaskProgress(String taskId, int progress, String message) {
        ConversionTask task = updateTaskProgress(taskId, progress);
        // Note: We don't have a message field in the entity, but we could add it
        // For now, we'll just log the message
        logger.info("Task {} progress: {}% - {}", taskId, progress, message);
        return task;
    }

    /**
     * Mark task as completed
     */
    public ConversionTask markTaskCompleted(String taskId, String resultPath, Long resultSize) {
        ConversionTask task = getTask(taskId);
        task.markAsCompleted(resultPath, resultSize);
        
        ConversionTask savedTask = taskRepository.save(task);
        logger.info("Marked task {} as completed", taskId);
        
        return savedTask;
    }

    /**
     * Mark task as failed
     */
    public ConversionTask markTaskFailed(String taskId, String errorMessage) {
        ConversionTask task = getTask(taskId);
        task.markAsFailed(errorMessage);
        
        ConversionTask savedTask = taskRepository.save(task);
        logger.warn("Marked task {} as failed: {}", taskId, errorMessage);
        
        return savedTask;
    }

    /**
     * Cancel task
     */
    public ConversionTask cancelTask(String taskId) {
        ConversionTask task = getTask(taskId);
        
        if (!task.getStatus().isCancellable()) {
            throw new IllegalStateException(
                String.format("Task %s cannot be cancelled in status %s", taskId, task.getStatus()));
        }
        
        task.markAsCancelled();
        
        ConversionTask savedTask = taskRepository.save(task);
        logger.info("Cancelled task {}", taskId);
        
        return savedTask;
    }

    /**
     * Delete task
     */
    public void deleteTask(String taskId) {
        if (!taskRepository.existsByTaskId(taskId)) {
            throw new TaskNotFoundException(taskId);
        }
        
        taskRepository.deleteById(taskId);
        logger.info("Deleted task {}", taskId);
    }

    /**
     * Get task progress
     */
    @Transactional(readOnly = true)
    public TaskProgressResponse getTaskProgress(String taskId) {
        ConversionTask task = getTask(taskId);
        
        TaskProgressResponse response = new TaskProgressResponse(
            task.getTaskId(),
            task.getStatus(),
            task.getProgress()
        );
        
        response.setLastUpdated(task.getUpdatedAt());
        response.setErrorMessage(task.getErrorMessage());
        
        return response;
    }

    /**
     * Get processing tasks (for monitoring)
     */
    @Transactional(readOnly = true)
    public List<ConversionTask> getProcessingTasks() {
        return taskRepository.findProcessingTasks();
    }

    /**
     * Get stuck tasks (processing for too long)
     */
    @Transactional(readOnly = true)
    public List<ConversionTask> getStuckTasks(int timeoutMinutes) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        return taskRepository.findStuckTasks(cutoffTime);
    }

    /**
     * Clean up old completed tasks
     */
    public int cleanupOldTasks(int daysOld) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysOld);
        return taskRepository.deleteOldTasks(cutoffTime);
    }

    /**
     * Get task statistics
     */
    @Transactional(readOnly = true)
    public TaskStatistics getTaskStatistics() {
        List<Object[]> stats = taskRepository.getTaskStatusStatistics();
        long totalTasks = taskRepository.count();
        long tasksToday = taskRepository.countTasksCreatedToday();
        long completedToday = taskRepository.countTasksCompletedToday();
        Optional<Double> avgProcessingTime = taskRepository.getAverageProcessingTime();
        
        return new TaskStatistics(stats, totalTasks, tasksToday, completedToday, avgProcessingTime.orElse(0.0));
    }

    // Helper methods
    private String generateTargetFileName(String originalFileName) {
        String nameWithoutExtension = originalFileName.replaceFirst("[.][^.]+$", "");
        return nameWithoutExtension + ".md";
    }

    private boolean isValidStatusTransition(TaskStatus from, TaskStatus to) {
        if (from == to) return true;
        
        return switch (from) {
            case PENDING -> to == TaskStatus.PROCESSING || to == TaskStatus.CANCELLED;
            case PROCESSING -> to == TaskStatus.COMPLETED || to == TaskStatus.FAILED || to == TaskStatus.CANCELLED;
            case COMPLETED, FAILED, CANCELLED -> false; // Terminal states
        };
    }

    /**
     * Inner class for task statistics
     */
    public static class TaskStatistics {
        private final List<Object[]> statusCounts;
        private final long totalTasks;
        private final long tasksToday;
        private final long completedToday;
        private final double averageProcessingTimeMs;

        public TaskStatistics(List<Object[]> statusCounts, long totalTasks, long tasksToday,
                            long completedToday, double averageProcessingTimeMs) {
            this.statusCounts = statusCounts;
            this.totalTasks = totalTasks;
            this.tasksToday = tasksToday;
            this.completedToday = completedToday;
            this.averageProcessingTimeMs = averageProcessingTimeMs;
        }

        // Getters
        public List<Object[]> getStatusCounts() { return statusCounts; }
        public long getTotalTasks() { return totalTasks; }
        public long getTasksToday() { return tasksToday; }
        public long getCompletedToday() { return completedToday; }
        public double getAverageProcessingTimeMs() { return averageProcessingTimeMs; }
    }
}
