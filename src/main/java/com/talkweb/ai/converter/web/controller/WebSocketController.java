package com.talkweb.ai.converter.web.controller;

import com.talkweb.ai.converter.web.service.TaskNotificationService;
import com.talkweb.ai.converter.web.websocket.TaskProgressWebSocketHandler;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for WebSocket management and information
 */
@RestController
@RequestMapping("/api/v1/websocket")
@Profile("server")
@Tag(name = "WebSocket", description = "Real-time task status updates")
public class WebSocketController {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketController.class);

    private final TaskNotificationService notificationService;

    @Autowired
    public WebSocketController(TaskNotificationService notificationService) {
        this.notificationService = notificationService;
    }

    @Operation(summary = "Get WebSocket connection information", 
               description = "Get information about WebSocket endpoints and connection statistics")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "WebSocket information retrieved successfully")
    })
    @GetMapping("/info")
    public ResponseEntity<WebSocketInfo> getWebSocketInfo() {
        logger.debug("Getting WebSocket information");
        
        TaskProgressWebSocketHandler.ConnectionStats stats = notificationService.getConnectionStats();
        
        WebSocketInfo info = new WebSocketInfo(
            "/ws/tasks",
            "/ws/tasks/{taskId}",
            "/ws/system",
            stats.getTotalConnections(),
            stats.getSystemSubscribers(),
            stats.getTaskSubscriptions()
        );
        
        return ResponseEntity.ok(info);
    }

    @Operation(summary = "Get WebSocket connection statistics", 
               description = "Get detailed statistics about active WebSocket connections")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Connection statistics retrieved successfully")
    })
    @GetMapping("/stats")
    public ResponseEntity<TaskProgressWebSocketHandler.ConnectionStats> getConnectionStats() {
        logger.debug("Getting WebSocket connection statistics");
        
        TaskProgressWebSocketHandler.ConnectionStats stats = notificationService.getConnectionStats();
        return ResponseEntity.ok(stats);
    }

    @Operation(summary = "Send test notification", 
               description = "Send a test notification to all connected clients (for testing purposes)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Test notification sent successfully")
    })
    @PostMapping("/test")
    public ResponseEntity<String> sendTestNotification(
            @RequestParam(defaultValue = "TEST") String type,
            @RequestParam(defaultValue = "This is a test notification") String message) {
        
        logger.info("Sending test notification: {} - {}", type, message);
        
        notificationService.broadcastMessage(type, message);
        
        return ResponseEntity.ok("Test notification sent successfully");
    }

    @Operation(summary = "Send system notification", 
               description = "Send a system-wide notification to all connected clients")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "System notification sent successfully")
    })
    @PostMapping("/notify")
    public ResponseEntity<String> sendSystemNotification(
            @RequestParam String type,
            @RequestParam String message) {
        
        logger.info("Sending system notification: {} - {}", type, message);
        
        notificationService.notifySystemEvent(type, message);
        
        return ResponseEntity.ok("System notification sent successfully");
    }

    @Operation(summary = "Send maintenance notification", 
               description = "Send a maintenance notification to all connected clients")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Maintenance notification sent successfully")
    })
    @PostMapping("/maintenance")
    public ResponseEntity<String> sendMaintenanceNotification(
            @RequestParam String message,
            @RequestParam(required = false) String scheduledTime) {
        
        logger.info("Sending maintenance notification: {}", message);
        
        java.time.LocalDateTime scheduled = null;
        if (scheduledTime != null) {
            try {
                scheduled = java.time.LocalDateTime.parse(scheduledTime);
            } catch (Exception e) {
                logger.warn("Invalid scheduled time format: {}", scheduledTime);
            }
        }
        
        notificationService.notifyMaintenance(message, scheduled);
        
        return ResponseEntity.ok("Maintenance notification sent successfully");
    }

    /**
     * WebSocket information response
     */
    public static class WebSocketInfo {
        private final String allTasksEndpoint;
        private final String taskSpecificEndpoint;
        private final String systemEndpoint;
        private final int totalConnections;
        private final int systemSubscribers;
        private final int taskSubscriptions;

        public WebSocketInfo(String allTasksEndpoint, String taskSpecificEndpoint, String systemEndpoint,
                           int totalConnections, int systemSubscribers, int taskSubscriptions) {
            this.allTasksEndpoint = allTasksEndpoint;
            this.taskSpecificEndpoint = taskSpecificEndpoint;
            this.systemEndpoint = systemEndpoint;
            this.totalConnections = totalConnections;
            this.systemSubscribers = systemSubscribers;
            this.taskSubscriptions = taskSubscriptions;
        }

        // Getters
        public String getAllTasksEndpoint() { return allTasksEndpoint; }
        public String getTaskSpecificEndpoint() { return taskSpecificEndpoint; }
        public String getSystemEndpoint() { return systemEndpoint; }
        public int getTotalConnections() { return totalConnections; }
        public int getSystemSubscribers() { return systemSubscribers; }
        public int getTaskSubscriptions() { return taskSubscriptions; }
    }
}
