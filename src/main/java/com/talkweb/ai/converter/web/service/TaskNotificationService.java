package com.talkweb.ai.converter.web.service;

import com.talkweb.ai.converter.web.dto.TaskProgressResponse;
import com.talkweb.ai.converter.web.model.TaskStatus;
import com.talkweb.ai.converter.web.websocket.TaskProgressWebSocketHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * Service for sending real-time notifications about task progress and system events
 */
@Service
@Profile("server")
public class TaskNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(TaskNotificationService.class);

    private final TaskProgressWebSocketHandler webSocketHandler;

    @Autowired
    public TaskNotificationService(TaskProgressWebSocketHandler webSocketHandler) {
        this.webSocketHandler = webSocketHandler;
    }

    /**
     * Notify about task progress update
     */
    public void notifyTaskProgress(String taskId, TaskStatus status, Integer progress, String message) {
        try {
            TaskProgressResponse progressResponse = new TaskProgressResponse(taskId, status, progress, message);
            progressResponse.setLastUpdated(LocalDateTime.now());
            
            // Send via WebSocket asynchronously
            CompletableFuture.runAsync(() -> {
                webSocketHandler.sendTaskProgressUpdate(taskId, progressResponse);
            });
            
            logger.debug("Sent task progress notification for task {}: {}% - {}", taskId, progress, message);
            
        } catch (Exception e) {
            logger.error("Error sending task progress notification for task {}: {}", taskId, e.getMessage());
        }
    }

    /**
     * Notify about task progress with current step
     */
    public void notifyTaskProgress(String taskId, TaskStatus status, Integer progress, String message, String currentStep) {
        try {
            TaskProgressResponse progressResponse = new TaskProgressResponse(taskId, status, progress, message);
            progressResponse.setCurrentStep(currentStep);
            progressResponse.setLastUpdated(LocalDateTime.now());
            
            // Send via WebSocket asynchronously
            CompletableFuture.runAsync(() -> {
                webSocketHandler.sendTaskProgressUpdate(taskId, progressResponse);
            });
            
            logger.debug("Sent task progress notification for task {}: {}% - {} ({})", 
                taskId, progress, message, currentStep);
            
        } catch (Exception e) {
            logger.error("Error sending task progress notification for task {}: {}", taskId, e.getMessage());
        }
    }

    /**
     * Notify about task started
     */
    public void notifyTaskStarted(String taskId) {
        notifyTaskProgress(taskId, TaskStatus.PROCESSING, 0, "Task started", "Initializing");
        notifySystemEvent("TASK_STARTED", new TaskEvent(taskId, "Task started"));
    }

    /**
     * Notify about task completed
     */
    public void notifyTaskCompleted(String taskId) {
        notifyTaskProgress(taskId, TaskStatus.COMPLETED, 100, "Task completed successfully", "Completed");
        notifySystemEvent("TASK_COMPLETED", new TaskEvent(taskId, "Task completed successfully"));
    }

    /**
     * Notify about task failed
     */
    public void notifyTaskFailed(String taskId, String errorMessage) {
        TaskProgressResponse progressResponse = TaskProgressResponse.failed(taskId, errorMessage);
        progressResponse.setLastUpdated(LocalDateTime.now());
        
        CompletableFuture.runAsync(() -> {
            webSocketHandler.sendTaskProgressUpdate(taskId, progressResponse);
        });
        
        notifySystemEvent("TASK_FAILED", new TaskEvent(taskId, "Task failed: " + errorMessage));
    }

    /**
     * Notify about task cancelled
     */
    public void notifyTaskCancelled(String taskId) {
        TaskProgressResponse progressResponse = TaskProgressResponse.cancelled(taskId);
        progressResponse.setLastUpdated(LocalDateTime.now());
        
        CompletableFuture.runAsync(() -> {
            webSocketHandler.sendTaskProgressUpdate(taskId, progressResponse);
        });
        
        notifySystemEvent("TASK_CANCELLED", new TaskEvent(taskId, "Task was cancelled"));
    }

    /**
     * Notify about system events
     */
    public void notifySystemEvent(String eventType, Object eventData) {
        try {
            SystemNotification notification = new SystemNotification(eventType, eventData);
            
            CompletableFuture.runAsync(() -> {
                webSocketHandler.sendSystemNotification(notification);
            });
            
            logger.debug("Sent system notification: {}", eventType);
            
        } catch (Exception e) {
            logger.error("Error sending system notification {}: {}", eventType, e.getMessage());
        }
    }

    /**
     * Notify about server status change
     */
    public void notifyServerStatus(String status, String message) {
        ServerStatusEvent statusEvent = new ServerStatusEvent(status, message, LocalDateTime.now());
        notifySystemEvent("SERVER_STATUS", statusEvent);
    }

    /**
     * Notify about system statistics update
     */
    public void notifySystemStats(Object stats) {
        notifySystemEvent("SYSTEM_STATS", stats);
    }

    /**
     * Broadcast message to all connected clients
     */
    public void broadcastMessage(String type, String message) {
        try {
            BroadcastMessage broadcastMsg = new BroadcastMessage(message, LocalDateTime.now());
            
            CompletableFuture.runAsync(() -> {
                webSocketHandler.broadcastMessage(type, broadcastMsg);
            });
            
            logger.info("Broadcasted message: {} - {}", type, message);
            
        } catch (Exception e) {
            logger.error("Error broadcasting message {}: {}", type, e.getMessage());
        }
    }

    /**
     * Send maintenance notification
     */
    public void notifyMaintenance(String message, LocalDateTime scheduledTime) {
        MaintenanceNotification maintenance = new MaintenanceNotification(message, scheduledTime);
        notifySystemEvent("MAINTENANCE", maintenance);
    }

    /**
     * Get WebSocket connection statistics
     */
    public TaskProgressWebSocketHandler.ConnectionStats getConnectionStats() {
        return webSocketHandler.getConnectionStats();
    }

    // Inner classes for different notification types
    
    /**
     * Task event notification
     */
    public static class TaskEvent {
        private final String taskId;
        private final String message;
        private final LocalDateTime timestamp;

        public TaskEvent(String taskId, String message) {
            this.taskId = taskId;
            this.message = message;
            this.timestamp = LocalDateTime.now();
        }

        // Getters
        public String getTaskId() { return taskId; }
        public String getMessage() { return message; }
        public LocalDateTime getTimestamp() { return timestamp; }
    }

    /**
     * System notification
     */
    public static class SystemNotification {
        private final String type;
        private final Object data;
        private final LocalDateTime timestamp;

        public SystemNotification(String type, Object data) {
            this.type = type;
            this.data = data;
            this.timestamp = LocalDateTime.now();
        }

        // Getters
        public String getType() { return type; }
        public Object getData() { return data; }
        public LocalDateTime getTimestamp() { return timestamp; }
    }

    /**
     * Server status event
     */
    public static class ServerStatusEvent {
        private final String status;
        private final String message;
        private final LocalDateTime timestamp;

        public ServerStatusEvent(String status, String message, LocalDateTime timestamp) {
            this.status = status;
            this.message = message;
            this.timestamp = timestamp;
        }

        // Getters
        public String getStatus() { return status; }
        public String getMessage() { return message; }
        public LocalDateTime getTimestamp() { return timestamp; }
    }

    /**
     * Broadcast message
     */
    public static class BroadcastMessage {
        private final String message;
        private final LocalDateTime timestamp;

        public BroadcastMessage(String message, LocalDateTime timestamp) {
            this.message = message;
            this.timestamp = timestamp;
        }

        // Getters
        public String getMessage() { return message; }
        public LocalDateTime getTimestamp() { return timestamp; }
    }

    /**
     * Maintenance notification
     */
    public static class MaintenanceNotification {
        private final String message;
        private final LocalDateTime scheduledTime;
        private final LocalDateTime notificationTime;

        public MaintenanceNotification(String message, LocalDateTime scheduledTime) {
            this.message = message;
            this.scheduledTime = scheduledTime;
            this.notificationTime = LocalDateTime.now();
        }

        // Getters
        public String getMessage() { return message; }
        public LocalDateTime getScheduledTime() { return scheduledTime; }
        public LocalDateTime getNotificationTime() { return notificationTime; }
    }
}
