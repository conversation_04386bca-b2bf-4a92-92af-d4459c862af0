package com.talkweb.ai.converter.web.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * Custom validation annotation for file type validation
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = FileTypeValidator.class)
@Documented
public @interface ValidFileType {
    
    String message() default "Invalid file type";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * Allowed file extensions
     */
    String[] allowed() default {
        "pdf", "docx", "doc", "xlsx", "xls", "pptx", "ppt",
        "html", "htm", "txt", "rtf", "odt",
        "png", "jpg", "jpeg", "tiff", "tif", "bmp", "gif"
    };
    
    /**
     * Blocked file extensions
     */
    String[] blocked() default {
        "exe", "bat", "sh", "cmd", "scr", "com", "pif", "vbs", "js"
    };
    
    /**
     * Maximum file size in bytes
     */
    long maxSize() default 104857600L; // 100MB
}
