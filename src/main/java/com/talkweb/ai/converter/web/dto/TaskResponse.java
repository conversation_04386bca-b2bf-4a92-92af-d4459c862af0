package com.talkweb.ai.converter.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.talkweb.ai.converter.web.model.ConversionTask;
import com.talkweb.ai.converter.web.model.TaskStatus;

import java.time.LocalDateTime;

/**
 * DTO for conversion task response
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskResponse {

    private String taskId;
    private String fileName;
    private String targetFileName;
    private TaskStatus status;
    private Integer progress;
    private String errorMessage;
    private Long fileSize;
    private String fileType;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime createdAt;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime startedAt;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime completedAt;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime updatedAt;
    
    private Long processingTimeMs;
    private Long resultFileSize;
    private String downloadUrl;

    // Default constructor
    public TaskResponse() {}

    // Constructor from ConversionTask entity
    public TaskResponse(ConversionTask task) {
        this.taskId = task.getTaskId();
        this.fileName = task.getFileName();
        this.targetFileName = task.getTargetFileName();
        this.status = task.getStatus();
        this.progress = task.getProgress();
        this.errorMessage = task.getErrorMessage();
        this.fileSize = task.getFileSize();
        this.fileType = task.getFileType();
        this.createdAt = task.getCreatedAt();
        this.startedAt = task.getStartedAt();
        this.completedAt = task.getCompletedAt();
        this.updatedAt = task.getUpdatedAt();
        this.processingTimeMs = task.getProcessingTimeMs();
        this.resultFileSize = task.getResultFileSize();
    }

    // Static factory method
    public static TaskResponse from(ConversionTask task) {
        return new TaskResponse(task);
    }

    // Static factory method with download URL
    public static TaskResponse from(ConversionTask task, String downloadUrl) {
        TaskResponse response = new TaskResponse(task);
        response.setDownloadUrl(downloadUrl);
        return response;
    }

    // Getters and setters
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getTargetFileName() {
        return targetFileName;
    }

    public void setTargetFileName(String targetFileName) {
        this.targetFileName = targetFileName;
    }

    public TaskStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public Long getResultFileSize() {
        return resultFileSize;
    }

    public void setResultFileSize(Long resultFileSize) {
        this.resultFileSize = resultFileSize;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    @Override
    public String toString() {
        return "TaskResponse{" +
                "taskId='" + taskId + '\'' +
                ", fileName='" + fileName + '\'' +
                ", status=" + status +
                ", progress=" + progress +
                ", createdAt=" + createdAt +
                '}';
    }
}
