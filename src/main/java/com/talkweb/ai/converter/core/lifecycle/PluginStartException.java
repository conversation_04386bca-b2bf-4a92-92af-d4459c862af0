package com.talkweb.ai.converter.core.lifecycle;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginState;

/**
 * 插件启动异常
 */
public class PluginStartException extends PluginException {
    
    private final String pluginId;

    public PluginStartException(String message, String pluginId) {
        super(message, PluginState.FAILED);
        this.pluginId = pluginId;
    }

    public PluginStartException(String message, String pluginId, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginId = pluginId;
    }

    public String getPluginId() {
        return pluginId;
    }

    public static PluginStartException invalidState(String pluginId, PluginState currentState) {
        return new PluginStartException(
            "Cannot start plugin in state: " + currentState + " (Plugin: " + pluginId + ")",
            pluginId
        );
    }

    public static PluginStartException resourceError(String pluginId, String resource) {
        return new PluginStartException(
            "Plugin start failed due to resource error: " + resource + " (Plugin: " + pluginId + ")",
            pluginId
        );
    }
}
