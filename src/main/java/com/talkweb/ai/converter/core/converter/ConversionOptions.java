package com.talkweb.ai.converter.core.converter;

import java.util.Map;
import java.util.Optional;

/**
 * 转换选项接口，定义了转换过程中的配置选项
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public interface ConversionOptions {
    
    /**
     * 获取选项值
     * 
     * @param key 选项键
     * @return 选项值的Optional包装
     */
    Optional<Object> getOption(String key);
    
    /**
     * 获取选项值，如果不存在则返回默认值
     * 
     * @param key 选项键
     * @param defaultValue 默认值
     * @param <T> 选项类型
     * @return 选项值，如果不存在则返回默认值
     */
    <T> T getOption(String key, T defaultValue);
    
    /**
     * 获取布尔选项值
     * 
     * @param key 选项键
     * @param defaultValue 默认值
     * @return 布尔选项值
     */
    boolean getBooleanOption(String key, boolean defaultValue);
    
    /**
     * 获取整数选项值
     * 
     * @param key 选项键
     * @param defaultValue 默认值
     * @return 整数选项值
     */
    int getIntOption(String key, int defaultValue);
    
    /**
     * 获取字符串选项值
     * 
     * @param key 选项键
     * @param defaultValue 默认值
     * @return 字符串选项值
     */
    String getStringOption(String key, String defaultValue);
    
    /**
     * 获取所有选项
     * 
     * @return 不可变的选项映射
     */
    Map<String, Object> getAllOptions();
    
    /**
     * 创建选项构建器
     * 
     * @return 选项构建器
     */
    static Builder builder() {
        return new DefaultConversionOptions.Builder();
    }
    
    /**
     * 选项构建器接口
     */
    interface Builder {
        /**
         * 添加选项
         * 
         * @param key 选项键
         * @param value 选项值
         * @return 当前构建器
         */
        Builder option(String key, Object value);
        
        /**
         * 构建选项对象
         * 
         * @return 选项对象
         */
        ConversionOptions build();
    }
}