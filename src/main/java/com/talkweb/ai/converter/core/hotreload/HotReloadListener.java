package com.talkweb.ai.converter.core.hotreload;

import java.nio.file.Path;

/**
 * 热重载监听器接口
 * 用于监听热重载事件
 */
public interface HotReloadListener {

    /**
     * 文件变化检测到时调用
     * 
     * @param filePath 变化的文件路径
     * @param changeType 变化类型
     */
    default void onFileChanged(Path filePath, FileChangeType changeType) {
        // 默认空实现
    }

    /**
     * 重载开始前调用
     * 
     * @param pluginPath 插件文件路径
     * @return 如果允许重载返回true，否则返回false
     */
    default boolean beforeReload(Path pluginPath) {
        return true;
    }

    /**
     * 重载成功后调用
     * 
     * @param pluginPath 插件文件路径
     * @param reloadTime 重载耗时（毫秒）
     */
    default void onReloadSuccess(Path pluginPath, long reloadTime) {
        // 默认空实现
    }

    /**
     * 重载失败后调用
     * 
     * @param pluginPath 插件文件路径
     * @param error 错误信息
     */
    default void onReloadFailure(Path pluginPath, Throwable error) {
        // 默认空实现
    }

    /**
     * 监控启动时调用
     * 
     * @param watchDirectories 监控的目录列表
     */
    default void onWatchStarted(java.util.List<Path> watchDirectories) {
        // 默认空实现
    }

    /**
     * 监控停止时调用
     */
    default void onWatchStopped() {
        // 默认空实现
    }

    /**
     * 监控目录添加时调用
     * 
     * @param directory 添加的目录
     */
    default void onWatchDirectoryAdded(Path directory) {
        // 默认空实现
    }

    /**
     * 监控目录移除时调用
     * 
     * @param directory 移除的目录
     */
    default void onWatchDirectoryRemoved(Path directory) {
        // 默认空实现
    }

    /**
     * 文件变化类型
     */
    enum FileChangeType {
        /**
         * 文件创建
         */
        CREATED,
        
        /**
         * 文件修改
         */
        MODIFIED,
        
        /**
         * 文件删除
         */
        DELETED
    }
}
