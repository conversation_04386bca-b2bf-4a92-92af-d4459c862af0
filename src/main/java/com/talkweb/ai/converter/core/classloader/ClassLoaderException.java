package com.talkweb.ai.converter.core.classloader;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginState;

import java.nio.file.Path;

/**
 * 类加载器异常
 */
public class ClassLoaderException extends PluginException {
    
    private final String pluginId;
    private final Path pluginPath;
    private final ClassLoaderErrorType errorType;

    public ClassLoaderException(String message, String pluginId) {
        this(message, pluginId, null, ClassLoaderErrorType.UNKNOWN, null);
    }

    public ClassLoaderException(String message, String pluginId, Throwable cause) {
        this(message, pluginId, null, ClassLoaderErrorType.UNKNOWN, cause);
    }

    public ClassLoaderException(String message, String pluginId, Path pluginPath, 
                               ClassLoaderErrorType errorType, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginId = pluginId;
        this.pluginPath = pluginPath;
        this.errorType = errorType;
    }

    public String getPluginId() {
        return pluginId;
    }

    public Path getPluginPath() {
        return pluginPath;
    }

    public ClassLoaderErrorType getErrorType() {
        return errorType;
    }

    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(errorType.getDescription()).append(": ").append(getMessage());
        
        if (pluginId != null) {
            sb.append(" (Plugin: ").append(pluginId).append(")");
        }
        
        if (pluginPath != null) {
            sb.append(" (Path: ").append(pluginPath.getFileName()).append(")");
        }
        
        return sb.toString();
    }

    /**
     * 类加载器错误类型
     */
    public enum ClassLoaderErrorType {
        CREATION_FAILED("Failed to create class loader"),
        INVALID_JAR_FILE("Invalid JAR file"),
        SECURITY_VIOLATION("Security violation"),
        MEMORY_ERROR("Insufficient memory"),
        IO_ERROR("I/O error"),
        CLEANUP_FAILED("Failed to cleanup class loader"),
        ALREADY_EXISTS("Class loader already exists"),
        NOT_FOUND("Class loader not found"),
        UNKNOWN("Unknown error");

        private final String description;

        ClassLoaderErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 工厂方法
    public static ClassLoaderException creationFailed(String pluginId, Path pluginPath, Throwable cause) {
        return new ClassLoaderException(
            "Failed to create class loader for plugin: " + pluginId,
            pluginId,
            pluginPath,
            ClassLoaderErrorType.CREATION_FAILED,
            cause
        );
    }

    public static ClassLoaderException invalidJarFile(String pluginId, Path pluginPath) {
        return new ClassLoaderException(
            "Invalid JAR file: " + pluginPath,
            pluginId,
            pluginPath,
            ClassLoaderErrorType.INVALID_JAR_FILE,
            null
        );
    }

    public static ClassLoaderException cleanupFailed(String pluginId, Throwable cause) {
        return new ClassLoaderException(
            "Failed to cleanup class loader for plugin: " + pluginId,
            pluginId,
            null,
            ClassLoaderErrorType.CLEANUP_FAILED,
            cause
        );
    }

    public static ClassLoaderException alreadyExists(String pluginId) {
        return new ClassLoaderException(
            "Class loader already exists for plugin: " + pluginId,
            pluginId,
            null,
            ClassLoaderErrorType.ALREADY_EXISTS,
            null
        );
    }

    public static ClassLoaderException notFound(String pluginId) {
        return new ClassLoaderException(
            "Class loader not found for plugin: " + pluginId,
            pluginId,
            null,
            ClassLoaderErrorType.NOT_FOUND,
            null
        );
    }
}
