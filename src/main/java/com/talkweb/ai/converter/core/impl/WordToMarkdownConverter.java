package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.converter.AbstractDocumentConverter;
import com.talkweb.ai.converter.core.converter.ConversionCapabilities;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.ConversionMetadata;
// Using fully qualified name to avoid conflict
import com.talkweb.ai.converter.util.word.WordConversionMode;
import com.talkweb.ai.converter.util.word.WordConversionOptions;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Set;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * Word to Markdown converter implementation using the new architecture
 * 
 * This converter wraps the existing utility converter and provides
 * the new interface while maintaining backward compatibility.
 * 
 * Features:
 * - Support for both .doc and .docx formats
 * - High-performance conversion with optimized processing
 * - Comprehensive Word element support (paragraphs, tables, images, lists)
 * - Configurable conversion options
 * - Detailed error handling and logging
 * - Image extraction and processing
 * 
 * <AUTHOR> Assistant
 * @version 3.0 (Refactored to use new architecture)
 */
public class WordToMarkdownConverter extends AbstractDocumentConverter {
    
    private static final Logger logger = Logger.getLogger(WordToMarkdownConverter.class.getName());
    
    /**
     * Default constructor
     */
    public WordToMarkdownConverter() {
        super();
    }
    
    @Override
    protected ConversionMetadata createMetadata() {
        return ConversionMetadata.builder("Word to Markdown Converter")
                .description("Converts Word files (.doc, .docx) to Markdown format with comprehensive element support")
                .version("3.0")
                .attribute("author", "AI Assistant")
                .attribute("supportedInputFormats", Set.of("doc", "docx"))
                .attribute("supportedOutputFormats", Set.of("md"))
                .build();
    }
    
    @Override
    public Set<String> getSupportedExtensions() {
        return Set.of("doc", "docx");
    }
    
    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.HEADINGS)
                .feature(ConversionCapabilities.Features.TABLES)
                .feature(ConversionCapabilities.Features.LISTS)
                .feature(ConversionCapabilities.Features.IMAGES)
                .feature(ConversionCapabilities.Features.METADATA)
                .capability("strictMode", true)
                .capability("looseMode", true)
                .capability("footnotes", true)
                .capability("imageExtraction", true)
                .capability("maxFileSize", 200 * 1024 * 1024) // 200MB
                .build();
    }
    
    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) throws ConversionException {
        logger.info("Starting Word conversion for: " + inputFile.getName());
        
        String parentDir = inputFile.getParent();
        if (parentDir == null) {
            parentDir = ".";
        }
        Path outputPath = Path.of(parentDir, inputFile.getName() + ".md");
        
        try {
            // Create conversion options from context
            WordConversionOptions options = createOptionsFromContext(context);
            WordConversionMode mode = getConversionMode(context);
            
            // Use the existing utility converter
            String markdownContent = com.talkweb.ai.converter.util.WordToMarkdownConverter.convert(inputFile, mode, options);
            
            return new ConversionResult(
                ConversionResult.Status.SUCCESS,
                inputFile.getPath(),
                outputPath.toString(),
                markdownContent
            );
            
        } catch (IOException e) {
            logger.log(Level.SEVERE, "Word conversion failed: " + inputFile.getName(), e);
            throw new ConversionException("Word conversion failed: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Unexpected error during Word conversion: " + inputFile.getName(), e);
            throw new ConversionException("Unexpected error during Word conversion: " + e.getMessage(), e);
        }
    }
    
    /**
     * Creates WordConversionOptions from ConversionContext
     */
    private WordConversionOptions createOptionsFromContext(ConversionContext context) {
        WordConversionOptions options = new WordConversionOptions();
        
        // Apply boolean options
        options.setConvertTables(context.getOptions().getBooleanOption("convertTables", true));
        options.setConvertImages(context.getOptions().getBooleanOption("convertImages", true));
        options.setConvertFootnotes(context.getOptions().getBooleanOption("convertFootnotes", true));
        options.setPreserveFormatting(context.getOptions().getBooleanOption("preserveFormatting", true));
        options.setExtractImages(context.getOptions().getBooleanOption("extractImages", false));
        
        // Apply string options
        String imageDir = context.getOptions().getStringOption("imageDirectory", "images");
        options.setImageDirectory(imageDir);
        
        // Apply integer options
        int maxImageSize = context.getOptions().getIntOption("maxImageSize", 1024 * 1024); // 1MB default
        options.setMaxImageSize(maxImageSize);
        
        return options;
    }
    
    /**
     * Gets conversion mode from context
     */
    private WordConversionMode getConversionMode(ConversionContext context) {
        String modeStr = context.getOptions().getStringOption("conversionMode", "LOOSE");
        
        try {
            return WordConversionMode.valueOf(modeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warning("Invalid conversion mode: " + modeStr + ", using LOOSE mode");
            return WordConversionMode.LOOSE;
        }
    }
    
    /**
     * Validates Word file format
     */
    @Override
    protected void validateInput(File inputFile, ConversionContext context) throws ConversionException {
        super.validateInput(inputFile, context);
        
        String fileName = inputFile.getName().toLowerCase();
        if (!fileName.endsWith(".doc") && !fileName.endsWith(".docx")) {
            throw new ConversionException("Unsupported file format. Only .doc and .docx files are supported.");
        }
        
        // Additional validation for file size
        long fileSize = inputFile.length();
        long maxSize = getCapabilities().getCapability("maxFileSize", 200L * 1024 * 1024);
        
        if (fileSize > maxSize) {
            throw new ConversionException("File size (" + fileSize + " bytes) exceeds maximum allowed size (" + maxSize + " bytes)");
        }
        
        // Check if file is readable
        if (!inputFile.canRead()) {
            throw new ConversionException("Cannot read input file: " + inputFile.getPath());
        }
    }
    
    /**
     * Gets conversion statistics
     */
    public ConversionStatistics getLastConversionStatistics() {
        // This would be implemented to return statistics from the last conversion
        // For now, return a placeholder
        return new ConversionStatistics();
    }
    
    /**
     * Conversion statistics holder
     */
    public static class ConversionStatistics {
        private int paragraphCount = 0;
        private int tableCount = 0;
        private int imageCount = 0;
        private int footnoteCount = 0;
        private long processingTimeMs = 0;
        
        // Getters and setters
        public int getParagraphCount() { return paragraphCount; }
        public void setParagraphCount(int paragraphCount) { this.paragraphCount = paragraphCount; }
        
        public int getTableCount() { return tableCount; }
        public void setTableCount(int tableCount) { this.tableCount = tableCount; }
        
        public int getImageCount() { return imageCount; }
        public void setImageCount(int imageCount) { this.imageCount = imageCount; }
        
        public int getFootnoteCount() { return footnoteCount; }
        public void setFootnoteCount(int footnoteCount) { this.footnoteCount = footnoteCount; }
        
        public long getProcessingTimeMs() { return processingTimeMs; }
        public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
        
        @Override
        public String toString() {
            return String.format("ConversionStatistics{paragraphs=%d, tables=%d, images=%d, footnotes=%d, time=%dms}",
                    paragraphCount, tableCount, imageCount, footnoteCount, processingTimeMs);
        }
    }
}
