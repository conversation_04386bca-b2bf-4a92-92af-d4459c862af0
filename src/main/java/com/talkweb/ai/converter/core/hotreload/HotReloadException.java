package com.talkweb.ai.converter.core.hotreload;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginState;

import java.nio.file.Path;

/**
 * 热重载异常
 */
public class HotReloadException extends PluginException {
    
    private final Path filePath;
    private final ReloadErrorType errorType;

    public HotReloadException(String message, Path filePath) {
        this(message, filePath, ReloadErrorType.UNKNOWN, null);
    }

    public HotReloadException(String message, Path filePath, Throwable cause) {
        this(message, filePath, ReloadErrorType.UNKNOWN, cause);
    }

    public HotReloadException(String message, Path filePath, ReloadErrorType errorType, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.filePath = filePath;
        this.errorType = errorType;
    }

    public Path getFilePath() {
        return filePath;
    }

    public ReloadErrorType getErrorType() {
        return errorType;
    }

    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(errorType.getDescription()).append(": ").append(getMessage());
        
        if (filePath != null) {
            sb.append(" (File: ").append(filePath.getFileName()).append(")");
        }
        
        return sb.toString();
    }

    /**
     * 重载错误类型
     */
    public enum ReloadErrorType {
        WATCH_SERVICE_ERROR("Watch service error"),
        FILE_ACCESS_ERROR("File access error"),
        PLUGIN_LOAD_ERROR("Plugin load error"),
        PLUGIN_UNLOAD_ERROR("Plugin unload error"),
        INVALID_PLUGIN_FILE("Invalid plugin file"),
        RELOAD_TIMEOUT("Reload timeout"),
        CONCURRENT_RELOAD("Concurrent reload detected"),
        UNKNOWN("Unknown error");

        private final String description;

        ReloadErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 工厂方法
    public static HotReloadException watchServiceError(String message, Throwable cause) {
        return new HotReloadException(
            "Watch service error: " + message,
            null,
            ReloadErrorType.WATCH_SERVICE_ERROR,
            cause
        );
    }

    public static HotReloadException fileAccessError(Path filePath, String message, Throwable cause) {
        return new HotReloadException(
            "File access error: " + message,
            filePath,
            ReloadErrorType.FILE_ACCESS_ERROR,
            cause
        );
    }

    public static HotReloadException pluginLoadError(Path filePath, String message, Throwable cause) {
        return new HotReloadException(
            "Plugin load error: " + message,
            filePath,
            ReloadErrorType.PLUGIN_LOAD_ERROR,
            cause
        );
    }

    public static HotReloadException pluginUnloadError(Path filePath, String message, Throwable cause) {
        return new HotReloadException(
            "Plugin unload error: " + message,
            filePath,
            ReloadErrorType.PLUGIN_UNLOAD_ERROR,
            cause
        );
    }

    public static HotReloadException invalidPluginFile(Path filePath, String reason) {
        return new HotReloadException(
            "Invalid plugin file: " + reason,
            filePath,
            ReloadErrorType.INVALID_PLUGIN_FILE,
            null
        );
    }

    public static HotReloadException reloadTimeout(Path filePath, long timeoutMs) {
        return new HotReloadException(
            "Reload timeout after " + timeoutMs + "ms",
            filePath,
            ReloadErrorType.RELOAD_TIMEOUT,
            null
        );
    }

    public static HotReloadException concurrentReload(Path filePath) {
        return new HotReloadException(
            "Concurrent reload detected for the same file",
            filePath,
            ReloadErrorType.CONCURRENT_RELOAD,
            null
        );
    }
}
