package com.talkweb.ai.converter.core.loader;

import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Properties;
import java.util.jar.JarFile;
import java.util.zip.ZipEntry;

/**
 * JAR文件插件加载器实现
 */
public class JarPluginLoader implements PluginLoader {
    
    private static final Logger log = LoggerFactory.getLogger(JarPluginLoader.class);
    private static final String PLUGIN_DESCRIPTOR = "plugin.properties";
    private static final String[] SUPPORTED_EXTENSIONS = {".jar"};

    @Override
    public Plugin loadPlugin(Path pluginPath) throws PluginLoadException {
        log.debug("Loading plugin from: {}", pluginPath);
        
        // 验证文件存在性
        if (!Files.exists(pluginPath)) {
            throw PluginLoadException.fileNotFound(pluginPath);
        }

        // 验证文件格式
        if (!isValidPlugin(pluginPath)) {
            throw PluginLoadException.invalidFormat(pluginPath, "JAR");
        }

        // 提取元数据
        PluginMetadata metadata = extractMetadata(pluginPath);
        
        // 创建类加载器并加载插件类
        URLClassLoader classLoader = null;
        try {
            classLoader = createClassLoader(pluginPath);
            Class<?> pluginClass = loadPluginClass(classLoader, metadata.getClassName());
            Plugin plugin = instantiatePlugin(pluginClass, pluginPath);
            
            log.info("Successfully loaded plugin: {} v{}", metadata.getId(), metadata.getVersion());
            return plugin;
            
        } catch (Exception e) {
            // 清理资源
            if (classLoader != null) {
                try {
                    classLoader.close();
                } catch (IOException ioException) {
                    log.warn("Failed to close class loader for plugin: {}", pluginPath, ioException);
                }
            }
            
            if (e instanceof PluginLoadException) {
                throw e;
            }
            throw new PluginLoadException("Failed to load plugin: " + e.getMessage(), pluginPath, e);
        }
    }

    @Override
    public PluginMetadata extractMetadata(Path pluginPath) throws PluginLoadException {
        log.debug("Extracting metadata from: {}", pluginPath);
        
        try (JarFile jarFile = new JarFile(pluginPath.toFile())) {
            ZipEntry entry = jarFile.getEntry(PLUGIN_DESCRIPTOR);
            if (entry == null) {
                throw PluginLoadException.missingDescriptor(pluginPath);
            }

            Properties props = new Properties();
            try (InputStream is = jarFile.getInputStream(entry)) {
                props.load(is);
            }

            return parseMetadata(props, pluginPath);
            
        } catch (IOException e) {
            throw PluginLoadException.corruptedFile(pluginPath, e);
        }
    }

    @Override
    public boolean isValidPlugin(Path pluginPath) {
        if (pluginPath == null || !Files.exists(pluginPath)) {
            return false;
        }

        String fileName = pluginPath.getFileName().toString().toLowerCase();
        for (String ext : SUPPORTED_EXTENSIONS) {
            if (fileName.endsWith(ext)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String[] getSupportedExtensions() {
        return SUPPORTED_EXTENSIONS.clone();
    }

    @Override
    public PluginValidationResult validatePlugin(Path pluginPath) {
        try {
            // 基本文件检查
            if (!Files.exists(pluginPath)) {
                return new PluginValidationResult(false, "Plugin file not found", 
                    PluginValidationResult.ValidationError.CORRUPTED_FILE);
            }

            if (!isValidPlugin(pluginPath)) {
                return new PluginValidationResult(false, "Invalid plugin file format", 
                    PluginValidationResult.ValidationError.CORRUPTED_FILE);
            }

            // 检查插件描述符
            try (JarFile jarFile = new JarFile(pluginPath.toFile())) {
                ZipEntry entry = jarFile.getEntry(PLUGIN_DESCRIPTOR);
                if (entry == null) {
                    return new PluginValidationResult(false, "Missing plugin descriptor", 
                        PluginValidationResult.ValidationError.MISSING_DESCRIPTOR);
                }

                // 验证描述符内容
                Properties props = new Properties();
                try (InputStream is = jarFile.getInputStream(entry)) {
                    props.load(is);
                }

                String pluginId = props.getProperty("plugin.id");
                String pluginClass = props.getProperty("plugin.class");
                String version = props.getProperty("plugin.version");

                if (pluginId == null || pluginId.trim().isEmpty()) {
                    return new PluginValidationResult(false, "Missing plugin.id", 
                        PluginValidationResult.ValidationError.INVALID_DESCRIPTOR);
                }

                if (pluginClass == null || pluginClass.trim().isEmpty()) {
                    return new PluginValidationResult(false, "Missing plugin.class", 
                        PluginValidationResult.ValidationError.INVALID_DESCRIPTOR);
                }

                if (version == null || version.trim().isEmpty()) {
                    return new PluginValidationResult(false, "Missing plugin.version", 
                        PluginValidationResult.ValidationError.INVALID_DESCRIPTOR);
                }

                // 检查主类是否存在
                URLClassLoader tempLoader = null;
                try {
                    tempLoader = createClassLoader(pluginPath);
                    tempLoader.loadClass(pluginClass);
                } catch (ClassNotFoundException e) {
                    return new PluginValidationResult(false, "Plugin main class not found: " + pluginClass, 
                        PluginValidationResult.ValidationError.MISSING_MAIN_CLASS);
                } finally {
                    if (tempLoader != null) {
                        try {
                            tempLoader.close();
                        } catch (IOException e) {
                            log.warn("Failed to close temporary class loader", e);
                        }
                    }
                }
            }

            return new PluginValidationResult(true);
            
        } catch (Exception e) {
            log.warn("Plugin validation failed for: {}", pluginPath, e);
            return new PluginValidationResult(false, "Validation error: " + e.getMessage(), 
                PluginValidationResult.ValidationError.CORRUPTED_FILE);
        }
    }

    /**
     * 创建插件类加载器
     */
    private URLClassLoader createClassLoader(Path pluginPath) throws PluginLoadException {
        try {
            URL pluginUrl = pluginPath.toUri().toURL();
            return new URLClassLoader(
                new URL[]{pluginUrl},
                getClass().getClassLoader()
            );
        } catch (Exception e) {
            throw new PluginLoadException("Failed to create class loader", pluginPath, e);
        }
    }

    /**
     * 加载插件类
     */
    private Class<?> loadPluginClass(URLClassLoader classLoader, String className) throws PluginLoadException {
        try {
            return classLoader.loadClass(className);
        } catch (ClassNotFoundException e) {
            throw new PluginLoadException("Plugin class not found: " + className, null, e);
        }
    }

    /**
     * 实例化插件
     */
    private Plugin instantiatePlugin(Class<?> pluginClass, Path pluginPath) throws PluginLoadException {
        try {
            Object instance = pluginClass.getDeclaredConstructor().newInstance();
            if (!(instance instanceof Plugin)) {
                throw new PluginLoadException(
                    "Plugin class does not implement Plugin interface: " + pluginClass.getName(),
                    pluginPath
                );
            }
            return (Plugin) instance;
        } catch (Exception e) {
            throw PluginLoadException.instantiationError(pluginPath, pluginClass.getName(), e);
        }
    }

    /**
     * 解析插件元数据
     */
    private PluginMetadata parseMetadata(Properties props, Path pluginPath) throws PluginLoadException {
        String pluginId = props.getProperty("plugin.id");
        String name = props.getProperty("plugin.name", pluginId);
        String version = props.getProperty("plugin.version");
        String description = props.getProperty("plugin.description", "");
        String provider = props.getProperty("plugin.provider", "");
        String className = props.getProperty("plugin.class");

        // 验证必需字段
        if (pluginId == null || pluginId.trim().isEmpty()) {
            throw PluginLoadException.invalidDescriptor(pluginPath, "Missing plugin.id");
        }
        if (version == null || version.trim().isEmpty()) {
            throw PluginLoadException.invalidDescriptor(pluginPath, "Missing plugin.version");
        }
        if (className == null || className.trim().isEmpty()) {
            throw PluginLoadException.invalidDescriptor(pluginPath, "Missing plugin.class");
        }

        return PluginMetadata.builder()
            .id(pluginId.trim())
            .name(name.trim())
            .version(version.trim())
            .description(description.trim())
            .provider(provider.trim())
            .className(className.trim())
            .build();
    }
}
