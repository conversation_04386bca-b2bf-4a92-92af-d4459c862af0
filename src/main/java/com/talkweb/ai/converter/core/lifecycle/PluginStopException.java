package com.talkweb.ai.converter.core.lifecycle;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginState;

/**
 * 插件停止异常
 */
public class PluginStopException extends PluginException {
    
    private final String pluginId;

    public PluginStopException(String message, String pluginId) {
        super(message, PluginState.FAILED);
        this.pluginId = pluginId;
    }

    public PluginStopException(String message, String pluginId, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginId = pluginId;
    }

    public String getPluginId() {
        return pluginId;
    }

    public static PluginStopException invalidState(String pluginId, PluginState currentState) {
        return new PluginStopException(
            "Cannot stop plugin in state: " + currentState + " (Plugin: " + pluginId + ")",
            pluginId
        );
    }

    public static PluginStopException timeoutError(String pluginId, long timeoutMs) {
        return new PluginStopException(
            "Plugin stop timeout after " + timeoutMs + "ms (Plugin: " + pluginId + ")",
            pluginId
        );
    }
}
