package com.talkweb.ai.converter.core.service;

import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.DocumentConverter;
import com.talkweb.ai.converter.core.registry.ConverterRegistry;
import com.talkweb.ai.converter.core.util.FileFormatDetector;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 转换服务默认实现
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class DefaultConversionService implements ConversionService {
    
    private final ConverterRegistry converterRegistry;
    private final Executor executor;
    
    // 统计数据
    private final LongAdder totalConversions = new LongAdder();
    private final LongAdder successfulConversions = new LongAdder();
    private final LongAdder failedConversions = new LongAdder();
    private final LongAdder cacheHits = new LongAdder();
    private final LongAdder cacheMisses = new LongAdder();
    private final AtomicLong totalConversionTime = new AtomicLong(0);
    
    /**
     * 创建默认转换服务
     * 
     * @param converterRegistry 转换器注册表
     * @param executor 执行器
     */
    public DefaultConversionService(ConverterRegistry converterRegistry, Executor executor) {
        this.converterRegistry = converterRegistry;
        this.executor = executor;
    }
    
    @Override
    public ConversionResult convert(File file) throws ConversionException {
        return convert(file, ConversionContext.builder().build());
    }
    
    @Override
    public ConversionResult convert(File file, ConversionContext context) throws ConversionException {
        long startTime = System.currentTimeMillis();
        totalConversions.increment();
        
        try {
            // 查找合适的转换器
            DocumentConverter converter = findConverter(file, context);
            if (converter == null) {
                failedConversions.increment();
                throw new ConversionException("找不到适合的转换器: " + file.getName());
            }
            
            // 执行转换
            ConversionResult result = converter.convert(file, context);
            
            // 更新统计信息
            if (result.isSuccess()) {
                successfulConversions.increment();
            } else {
                failedConversions.increment();
            }
            
            return result;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            totalConversionTime.addAndGet(duration);
        }
    }
    
    @Override
    public Map<File, ConversionResult> convertAll(List<File> files) throws ConversionException {
        return convertAll(files, ConversionContext.builder().build());
    }
    
    @Override
    public Map<File, ConversionResult> convertAll(List<File> files, ConversionContext context) throws ConversionException {
        Map<File, ConversionResult> results = new HashMap<>();
        
        for (File file : files) {
            try {
                ConversionResult result = convert(file, context);
                results.put(file, result);
            } catch (ConversionException e) {
                results.put(file, new ConversionResult(
                        ConversionResult.Status.FAILED,
                        "",
                        "转换失败: " + e.getMessage()
                ));
            }
        }
        
        return results;
    }
    
    @Override
    public CompletableFuture<ConversionResult> convertAsync(File file) {
        return convertAsync(file, ConversionContext.builder().build());
    }
    
    @Override
    public CompletableFuture<ConversionResult> convertAsync(File file, ConversionContext context) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return convert(file, context);
            } catch (ConversionException e) {
                return new ConversionResult(
                        ConversionResult.Status.FAILED,
                        "",
                        "转换失败: " + e.getMessage()
                );
            }
        }, executor);
    }
    
    @Override
    public CompletableFuture<Map<File, ConversionResult>> convertAllAsync(List<File> files) {
        return convertAllAsync(files, ConversionContext.builder().build());
    }
    
    @Override
    public CompletableFuture<Map<File, ConversionResult>> convertAllAsync(List<File> files, ConversionContext context) {
        Map<File, CompletableFuture<ConversionResult>> futures = new ConcurrentHashMap<>();
        
        for (File file : files) {
            futures.put(file, convertAsync(file, context));
        }
        
        return CompletableFuture.allOf(futures.values().toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    Map<File, ConversionResult> results = new HashMap<>();
                    futures.forEach((file, future) -> results.put(file, future.join()));
                    return results;
                });
    }
    
    @Override
    public ServiceStatistics getStatistics() {
        return new DefaultServiceStatistics(
                totalConversions.sum(),
                successfulConversions.sum(),
                failedConversions.sum(),
                totalConversionTime.get() / Math.max(1, totalConversions.sum()),
                cacheHits.sum() / Math.max(1.0, cacheHits.sum() + cacheMisses.sum())
        );
    }
    
    /**
     * 查找合适的转换器
     * 
     * @param file 输入文件
     * @param context 转换上下文
     * @return 转换器，如果找不到则返回null
     */
    protected DocumentConverter findConverter(File file, ConversionContext context) {
        String extension = FileFormatDetector.getFileExtension(file);
        if (extension == null) {
            return null;
        }
        
        return converterRegistry.findConverter(file, context);
    }
    
    /**
     * 默认服务统计信息实现
     */
    private static class DefaultServiceStatistics implements ServiceStatistics {
        private final long totalConversions;
        private final long successfulConversions;
        private final long failedConversions;
        private final double averageConversionTime;
        private final double cacheHitRate;
        
        public DefaultServiceStatistics(
                long totalConversions,
                long successfulConversions,
                long failedConversions,
                double averageConversionTime,
                double cacheHitRate) {
            this.totalConversions = totalConversions;
            this.successfulConversions = successfulConversions;
            this.failedConversions = failedConversions;
            this.averageConversionTime = averageConversionTime;
            this.cacheHitRate = cacheHitRate;
        }
        
        @Override
        public long getTotalConversions() {
            return totalConversions;
        }
        
        @Override
        public long getSuccessfulConversions() {
            return successfulConversions;
        }
        
        @Override
        public long getFailedConversions() {
            return failedConversions;
        }
        
        @Override
        public double getAverageConversionTime() {
            return averageConversionTime;
        }
        
        @Override
        public double getCacheHitRate() {
            return cacheHitRate;
        }
    }
}