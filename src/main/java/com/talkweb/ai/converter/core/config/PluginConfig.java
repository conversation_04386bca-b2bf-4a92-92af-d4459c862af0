
package com.talkweb.ai.converter.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@Component("hotReloadPluginConfig")
@ConfigurationProperties(prefix = "plugin.hot-reload")
public class PluginConfig {
    private static final Logger log = LoggerFactory.getLogger(PluginConfig.class);
    private long pollInterval = 5000;
    private String mode = "BLACKLIST";
    private List<String> blacklist = new ArrayList<>();
    private List<String> whitelist = new ArrayList<>();
    private long debounceTime = 1000;

    // Getters and Setters
    public long getPollInterval() { return pollInterval; }
    public long getDebounceTimeMs() { return debounceTime; }
    public void setPollInterval(long pollInterval) { this.pollInterval = pollInterval; }
    
    public String getMode() { return mode; }
    public void setMode(String mode) { this.mode = mode; }
    
    public List<String> getBlacklist() { return blacklist; }
    public void setBlacklist(List<String> blacklist) { this.blacklist = blacklist; }
    
    public List<String> getWhitelist() { return whitelist; }
    public void setWhitelist(List<String> whitelist) { this.whitelist = whitelist; }
    
    public long getDebounceTime() { return debounceTime; }
    public void setDebounceTime(long debounceTime) {        if (debounceTime < 100) {
            throw new IllegalArgumentException("Debounce time must be ≥100ms");
        }
        log.info("Updating debounce time from {}ms to {}ms", 
            this.debounceTime, debounceTime);
        this.debounceTime = debounceTime;
    }
}
