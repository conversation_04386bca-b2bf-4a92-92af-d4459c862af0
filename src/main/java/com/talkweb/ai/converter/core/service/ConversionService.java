package com.talkweb.ai.converter.core.service;

import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.converter.ConversionContext;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 转换服务接口，提供文档转换的高级服务
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public interface ConversionService {
    
    /**
     * 转换单个文件
     * 
     * @param file 输入文件
     * @return 转换结果
     * @throws ConversionException 转换异常
     */
    ConversionResult convert(File file) throws ConversionException;
    
    /**
     * 使用指定上下文转换单个文件
     * 
     * @param file 输入文件
     * @param context 转换上下文
     * @return 转换结果
     * @throws ConversionException 转换异常
     */
    ConversionResult convert(File file, ConversionContext context) throws ConversionException;
    
    /**
     * 批量转换文件
     * 
     * @param files 输入文件列表
     * @return 转换结果映射
     * @throws ConversionException 转换异常
     */
    Map<File, ConversionResult> convertAll(List<File> files) throws ConversionException;
    
    /**
     * 使用指定上下文批量转换文件
     * 
     * @param files 输入文件列表
     * @param context 转换上下文
     * @return 转换结果映射
     * @throws ConversionException 转换异常
     */
    Map<File, ConversionResult> convertAll(List<File> files, ConversionContext context) throws ConversionException;
    
    /**
     * 异步转换单个文件
     * 
     * @param file 输入文件
     * @return 转换结果的CompletableFuture
     */
    CompletableFuture<ConversionResult> convertAsync(File file);
    
    /**
     * 使用指定上下文异步转换单个文件
     * 
     * @param file 输入文件
     * @param context 转换上下文
     * @return 转换结果的CompletableFuture
     */
    CompletableFuture<ConversionResult> convertAsync(File file, ConversionContext context);
    
    /**
     * 异步批量转换文件
     * 
     * @param files 输入文件列表
     * @return 转换结果映射的CompletableFuture
     */
    CompletableFuture<Map<File, ConversionResult>> convertAllAsync(List<File> files);
    
    /**
     * 使用指定上下文异步批量转换文件
     * 
     * @param files 输入文件列表
     * @param context 转换上下文
     * @return 转换结果映射的CompletableFuture
     */
    CompletableFuture<Map<File, ConversionResult>> convertAllAsync(List<File> files, ConversionContext context);
    
    /**
     * 获取服务统计信息
     * 
     * @return 服务统计信息
     */
    ServiceStatistics getStatistics();
    
    /**
     * 服务统计信息
     */
    interface ServiceStatistics {
        /**
         * 获取总转换次数
         * 
         * @return 总转换次数
         */
        long getTotalConversions();
        
        /**
         * 获取成功转换次数
         * 
         * @return 成功转换次数
         */
        long getSuccessfulConversions();
        
        /**
         * 获取失败转换次数
         * 
         * @return 失败转换次数
         */
        long getFailedConversions();
        
        /**
         * 获取平均转换时间（毫秒）
         * 
         * @return 平均转换时间
         */
        double getAverageConversionTime();
        
        /**
         * 获取缓存命中率
         * 
         * @return 缓存命中率
         */
        double getCacheHitRate();
    }
}