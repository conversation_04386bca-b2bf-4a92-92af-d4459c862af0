package com.talkweb.ai.converter.core.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileDescriptor;
import java.net.InetAddress;
import java.security.Permission;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 插件安全管理器实现
 * 扩展Java SecurityManager以提供插件级别的安全控制
 */
public class PluginSecurityManagerImpl extends SecurityManager {
    
    private static final Logger log = LoggerFactory.getLogger(PluginSecurityManagerImpl.class);
    
    private final Map<String, PluginSandbox> pluginSandboxes = new ConcurrentHashMap<>();
    private final SecurityManager originalSecurityManager;
    
    public PluginSecurityManagerImpl() {
        this.originalSecurityManager = System.getSecurityManager();
    }

    /**
     * 注册插件沙箱
     */
    public void registerPluginSandbox(String pluginId, PluginSandbox sandbox) {
        pluginSandboxes.put(pluginId, sandbox);
        log.debug("Registered sandbox for plugin: {}", pluginId);
    }

    /**
     * 注销插件沙箱
     */
    public void unregisterPluginSandbox(String pluginId) {
        pluginSandboxes.remove(pluginId);
        log.debug("Unregistered sandbox for plugin: {}", pluginId);
    }

    @Override
    public void checkPermission(Permission perm) {
        String pluginId = getCurrentPluginId();
        if (pluginId != null) {
            PluginSandbox sandbox = pluginSandboxes.get(pluginId);
            if (sandbox != null && !sandbox.getAllowedPermissions().contains(perm)) {
                log.warn("Permission denied for plugin {}: {}", pluginId, perm);
                throw new SecurityException("Permission denied: " + perm + " for plugin: " + pluginId);
            }
        }
        
        // 如果有原始的SecurityManager，也要检查
        if (originalSecurityManager != null) {
            originalSecurityManager.checkPermission(perm);
        }
    }

    @Override
    public void checkPermission(Permission perm, Object context) {
        checkPermission(perm);
    }

    @Override
    public void checkRead(String file) {
        String pluginId = getCurrentPluginId();
        if (pluginId != null) {
            PluginSandbox sandbox = pluginSandboxes.get(pluginId);
            if (sandbox != null) {
                java.io.File fileObj = new java.io.File(file);
                if (!sandbox.checkFileAccess(fileObj, PluginSandbox.FileAccess.READ)) {
                    log.warn("File read access denied for plugin {}: {}", pluginId, file);
                    throw new SecurityException("File read access denied: " + file + " for plugin: " + pluginId);
                }
            }
        }
        
        if (originalSecurityManager != null) {
            originalSecurityManager.checkRead(file);
        }
    }

    @Override
    public void checkRead(String file, Object context) {
        checkRead(file);
    }

    @Override
    public void checkRead(FileDescriptor fd) {
        // 对于文件描述符，我们采用更宽松的策略
        if (originalSecurityManager != null) {
            originalSecurityManager.checkRead(fd);
        }
    }

    @Override
    public void checkWrite(String file) {
        String pluginId = getCurrentPluginId();
        if (pluginId != null) {
            PluginSandbox sandbox = pluginSandboxes.get(pluginId);
            if (sandbox != null) {
                java.io.File fileObj = new java.io.File(file);
                if (!sandbox.checkFileAccess(fileObj, PluginSandbox.FileAccess.WRITE)) {
                    log.warn("File write access denied for plugin {}: {}", pluginId, file);
                    throw new SecurityException("File write access denied: " + file + " for plugin: " + pluginId);
                }
            }
        }
        
        if (originalSecurityManager != null) {
            originalSecurityManager.checkWrite(file);
        }
    }

    @Override
    public void checkWrite(FileDescriptor fd) {
        // 对于文件描述符，我们采用更宽松的策略
        if (originalSecurityManager != null) {
            originalSecurityManager.checkWrite(fd);
        }
    }

    @Override
    public void checkDelete(String file) {
        String pluginId = getCurrentPluginId();
        if (pluginId != null) {
            PluginSandbox sandbox = pluginSandboxes.get(pluginId);
            if (sandbox != null) {
                java.io.File fileObj = new java.io.File(file);
                if (!sandbox.checkFileAccess(fileObj, PluginSandbox.FileAccess.DELETE)) {
                    log.warn("File delete access denied for plugin {}: {}", pluginId, file);
                    throw new SecurityException("File delete access denied: " + file + " for plugin: " + pluginId);
                }
            }
        }
        
        if (originalSecurityManager != null) {
            originalSecurityManager.checkDelete(file);
        }
    }

    @Override
    public void checkConnect(String host, int port) {
        String pluginId = getCurrentPluginId();
        if (pluginId != null) {
            PluginSandbox sandbox = pluginSandboxes.get(pluginId);
            if (sandbox != null) {
                if (!sandbox.checkNetworkAccess(host, port, "tcp")) {
                    log.warn("Network access denied for plugin {}: {}:{}", pluginId, host, port);
                    throw new SecurityException("Network access denied: " + host + ":" + port + " for plugin: " + pluginId);
                }
            }
        }
        
        if (originalSecurityManager != null) {
            originalSecurityManager.checkConnect(host, port);
        }
    }

    @Override
    public void checkConnect(String host, int port, Object context) {
        checkConnect(host, port);
    }

    @Override
    public void checkListen(int port) {
        String pluginId = getCurrentPluginId();
        if (pluginId != null) {
            PluginSandbox sandbox = pluginSandboxes.get(pluginId);
            if (sandbox != null) {
                if (!sandbox.checkNetworkAccess("localhost", port, "tcp")) {
                    log.warn("Network listen access denied for plugin {}: port {}", pluginId, port);
                    throw new SecurityException("Network listen access denied: port " + port + " for plugin: " + pluginId);
                }
            }
        }
        
        if (originalSecurityManager != null) {
            originalSecurityManager.checkListen(port);
        }
    }

    @Override
    public void checkPropertyAccess(String key) {
        String pluginId = getCurrentPluginId();
        if (pluginId != null) {
            PluginSandbox sandbox = pluginSandboxes.get(pluginId);
            if (sandbox != null) {
                if (!sandbox.checkSystemPropertyAccess(key, PluginSandbox.PropertyAccess.READ)) {
                    log.warn("System property read access denied for plugin {}: {}", pluginId, key);
                    throw new SecurityException("System property read access denied: " + key + " for plugin: " + pluginId);
                }
            }
        }
        
        if (originalSecurityManager != null) {
            originalSecurityManager.checkPropertyAccess(key);
        }
    }

    @Override
    public void checkExit(int status) {
        String pluginId = getCurrentPluginId();
        if (pluginId != null) {
            log.warn("System exit attempt blocked for plugin: {}", pluginId);
            throw new SecurityException("System exit not allowed for plugin: " + pluginId);
        }
        
        if (originalSecurityManager != null) {
            originalSecurityManager.checkExit(status);
        }
    }

    @Override
    public void checkCreateClassLoader() {
        String pluginId = getCurrentPluginId();
        if (pluginId != null) {
            log.warn("Class loader creation attempt blocked for plugin: {}", pluginId);
            throw new SecurityException("Class loader creation not allowed for plugin: " + pluginId);
        }
        
        if (originalSecurityManager != null) {
            originalSecurityManager.checkCreateClassLoader();
        }
    }

    /**
     * 获取当前执行线程对应的插件ID
     */
    private String getCurrentPluginId() {
        // 通过分析调用栈来确定当前执行的插件
        Class<?>[] classContext = getClassContext();
        
        for (Class<?> clazz : classContext) {
            ClassLoader classLoader = clazz.getClassLoader();
            if (classLoader != null) {
                // 检查是否是插件类加载器
                String pluginId = getPluginIdFromClassLoader(classLoader);
                if (pluginId != null) {
                    return pluginId;
                }
            }
        }
        
        return null;
    }

    /**
     * 从类加载器获取插件ID
     */
    private String getPluginIdFromClassLoader(ClassLoader classLoader) {
        // 这里需要与PluginClassLoaderManager配合，
        // 可以通过类加载器的特定属性或者维护一个映射表来实现
        // 简化实现：检查类加载器的字符串表示
        String classLoaderStr = classLoader.toString();
        if (classLoaderStr.contains("plugin-")) {
            // 从类加载器字符串中提取插件ID
            // 这是一个简化的实现，实际应该有更可靠的方法
            int start = classLoaderStr.indexOf("plugin-") + 7;
            int end = classLoaderStr.indexOf(")", start);
            if (end > start) {
                return classLoaderStr.substring(start, end);
            }
        }
        
        return null;
    }

    /**
     * 恢复原始的SecurityManager
     */
    public void restore() {
        try {
            System.setSecurityManager(originalSecurityManager);
            log.info("Restored original security manager");
        } catch (UnsupportedOperationException e) {
            log.warn("Cannot restore security manager in Java 17+: {}", e.getMessage());
        }
    }
}
