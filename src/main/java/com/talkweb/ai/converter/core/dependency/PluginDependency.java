package com.talkweb.ai.converter.core.dependency;

import java.util.Objects;

/**
 * 插件依赖信息
 */
public class PluginDependency {
    
    private final String pluginId;
    private final String version;
    private final boolean optional;
    private final DependencyType type;

    public PluginDependency(String pluginId, String version, boolean optional, DependencyType type) {
        this.pluginId = Objects.requireNonNull(pluginId, "Plugin ID cannot be null");
        this.version = Objects.requireNonNull(version, "Version cannot be null");
        this.optional = optional;
        this.type = Objects.requireNonNull(type, "Dependency type cannot be null");
    }

    public String getPluginId() {
        return pluginId;
    }

    public String getVersion() {
        return version;
    }

    public boolean isOptional() {
        return optional;
    }

    public DependencyType getType() {
        return type;
    }

    /**
     * 检查版本是否兼容
     * 
     * @param availableVersion 可用版本
     * @return 如果兼容返回true
     */
    public boolean isVersionCompatible(String availableVersion) {
        if (availableVersion == null) {
            return false;
        }
        
        // 简单的版本比较逻辑，可以根据需要扩展
        return VersionComparator.isCompatible(version, availableVersion);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PluginDependency that = (PluginDependency) o;
        return optional == that.optional &&
               Objects.equals(pluginId, that.pluginId) &&
               Objects.equals(version, that.version) &&
               type == that.type;
    }

    @Override
    public int hashCode() {
        return Objects.hash(pluginId, version, optional, type);
    }

    @Override
    public String toString() {
        return String.format("PluginDependency{pluginId='%s', version='%s', optional=%s, type=%s}",
                           pluginId, version, optional, type);
    }

    /**
     * 依赖类型
     */
    public enum DependencyType {
        /**
         * 运行时依赖
         */
        RUNTIME,
        
        /**
         * 编译时依赖
         */
        COMPILE,
        
        /**
         * 测试依赖
         */
        TEST,
        
        /**
         * 提供的依赖（由容器提供）
         */
        PROVIDED
    }

    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 依赖构建器
     */
    public static class Builder {
        private String pluginId;
        private String version;
        private boolean optional = false;
        private DependencyType type = DependencyType.RUNTIME;

        public Builder pluginId(String pluginId) {
            this.pluginId = pluginId;
            return this;
        }

        public Builder version(String version) {
            this.version = version;
            return this;
        }

        public Builder optional(boolean optional) {
            this.optional = optional;
            return this;
        }

        public Builder type(DependencyType type) {
            this.type = type;
            return this;
        }

        public PluginDependency build() {
            return new PluginDependency(pluginId, version, optional, type);
        }
    }
}
