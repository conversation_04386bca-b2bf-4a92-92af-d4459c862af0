package com.talkweb.ai.converter.core.security;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginState;

/**
 * 插件安全异常
 */
public class PluginSecurityException extends PluginException {
    
    private final String pluginId;
    private final SecurityErrorType errorType;
    private final String securityContext;

    public PluginSecurityException(String message, String pluginId) {
        this(message, pluginId, SecurityErrorType.UNKNOWN, null, null);
    }

    public PluginSecurityException(String message, String pluginId, SecurityErrorType errorType) {
        this(message, pluginId, errorType, null, null);
    }

    public PluginSecurityException(String message, String pluginId, SecurityErrorType errorType, 
                                 String securityContext, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginId = pluginId;
        this.errorType = errorType;
        this.securityContext = securityContext;
    }

    public String getPluginId() {
        return pluginId;
    }

    public SecurityErrorType getErrorType() {
        return errorType;
    }

    public String getSecurityContext() {
        return securityContext;
    }

    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(errorType.getDescription()).append(": ").append(getMessage());
        
        if (pluginId != null) {
            sb.append(" (Plugin: ").append(pluginId).append(")");
        }
        
        if (securityContext != null) {
            sb.append(" (Context: ").append(securityContext).append(")");
        }
        
        return sb.toString();
    }

    /**
     * 安全错误类型
     */
    public enum SecurityErrorType {
        SIGNATURE_VERIFICATION_FAILED("Signature verification failed"),
        PERMISSION_DENIED("Permission denied"),
        SANDBOX_CREATION_FAILED("Sandbox creation failed"),
        SANDBOX_VIOLATION("Sandbox violation"),
        UNTRUSTED_PLUGIN("Untrusted plugin"),
        SECURITY_POLICY_VIOLATION("Security policy violation"),
        CERTIFICATE_ERROR("Certificate error"),
        UNKNOWN("Unknown security error");

        private final String description;

        SecurityErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 工厂方法
    public static PluginSecurityException signatureVerificationFailed(String pluginId, String reason) {
        return new PluginSecurityException(
            "Signature verification failed: " + reason,
            pluginId,
            SecurityErrorType.SIGNATURE_VERIFICATION_FAILED,
            "signature_verification",
            null
        );
    }

    public static PluginSecurityException permissionDenied(String pluginId, String permission) {
        return new PluginSecurityException(
            "Permission denied: " + permission,
            pluginId,
            SecurityErrorType.PERMISSION_DENIED,
            "permission_check",
            null
        );
    }

    public static PluginSecurityException sandboxCreationFailed(String pluginId, Throwable cause) {
        return new PluginSecurityException(
            "Failed to create sandbox for plugin: " + pluginId,
            pluginId,
            SecurityErrorType.SANDBOX_CREATION_FAILED,
            "sandbox_creation",
            cause
        );
    }

    public static PluginSecurityException sandboxViolation(String pluginId, String violation) {
        return new PluginSecurityException(
            "Sandbox violation: " + violation,
            pluginId,
            SecurityErrorType.SANDBOX_VIOLATION,
            "sandbox_runtime",
            null
        );
    }

    public static PluginSecurityException untrustedPlugin(String pluginId, String reason) {
        return new PluginSecurityException(
            "Untrusted plugin: " + reason,
            pluginId,
            SecurityErrorType.UNTRUSTED_PLUGIN,
            "trust_verification",
            null
        );
    }

    public static PluginSecurityException policyViolation(String pluginId, String policyId, String violation) {
        return new PluginSecurityException(
            "Security policy violation: " + violation + " (Policy: " + policyId + ")",
            pluginId,
            SecurityErrorType.SECURITY_POLICY_VIOLATION,
            "policy_enforcement",
            null
        );
    }
}
