package com.talkweb.ai.converter.core;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 文档处理结果
 */
public class ProcessingResult {
    private final boolean success;
    private final File outputFile;
    private final String errorMessage;
    private final Throwable error;
    private final Map<String, Object> metadata;

    private ProcessingResult(Builder builder) {
        this.success = builder.success;
        this.outputFile = builder.outputFile;
        this.errorMessage = builder.errorMessage;
        this.error = builder.error;
        this.metadata = new HashMap<>(builder.metadata);
    }

    /**
     * 处理是否成功
     * @return 是否成功
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 获取输出文件
     * @return 输出文件
     */
    public File getOutputFile() {
        return outputFile;
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }
    
    /**
     * 获取异常
     * @return 异常
     */
    public Throwable getError() {
        return error;
    }
    
    /**
     * 获取元数据
     * @return 元数据
     */
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    /**
     * 获取元数据值
     * @param key 键
     * @return 值
     */
    public Object getMetadata(String key) {
        return metadata.get(key);
    }
    
    /**
     * 获取元数据值，如果不存在则返回默认值
     * @param key 键
     * @param defaultValue 默认值
     * @return 值
     */
    public Object getMetadata(String key, Object defaultValue) {
        return metadata.getOrDefault(key, defaultValue);
    }
    
    /**
     * 创建成功结果
     * @param outputFile 输出文件
     * @return 处理结果
     */
    public static ProcessingResult success(File outputFile) {
        return new Builder()
                .success(true)
                .outputFile(outputFile)
                .build();
    }
    
    /**
     * 创建失败结果
     * @param errorMessage 错误信息
     * @return 处理结果
     */
    public static ProcessingResult failure(String errorMessage) {
        return new Builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
    
    /**
     * 创建失败结果
     * @param error 异常
     * @return 处理结果
     */
    public static ProcessingResult failure(Throwable error) {
        return new Builder()
                .success(false)
                .error(error)
                .errorMessage(error.getMessage())
                .build();
    }

    /**
     * 处理结果构建器
     */
    public static class Builder {
        private boolean success;
        private File outputFile;
        private String errorMessage;
        private Throwable error;
        private final Map<String, Object> metadata = new HashMap<>();

        /**
         * 设置是否成功
         * @param success 是否成功
         * @return 构建器
         */
        public Builder success(boolean success) {
            this.success = success;
            return this;
        }

        /**
         * 设置输出文件
         * @param outputFile 输出文件
         * @return 构建器
         */
        public Builder outputFile(File outputFile) {
            this.outputFile = outputFile;
            return this;
        }

        /**
         * 设置错误信息
         * @param errorMessage 错误信息
         * @return 构建器
         */
        public Builder errorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
            return this;
        }
        
        /**
         * 设置异常
         * @param error 异常
         * @return 构建器
         */
        public Builder error(Throwable error) {
            this.error = error;
            return this;
        }
        
        /**
         * 添加元数据
         * @param key 键
         * @param value 值
         * @return 构建器
         */
        public Builder metadata(String key, Object value) {
            this.metadata.put(key, value);
            return this;
        }

        /**
         * 构建处理结果
         * @return 处理结果
         */
        public ProcessingResult build() {
            return new ProcessingResult(this);
        }
    }
}