
package com.talkweb.ai.converter.core;

public class PluginEvent {
    public enum Type {
        CREATED,
        DELETED,
        MODIFIED,
        ERROR
    }

    private final Type type;
    private final String pluginId;
    private final Exception exception;

    public PluginEvent(Type type, String pluginId) {
        this(type, pluginId, null);
    }

    public PluginEvent(Type type, String pluginId, Exception exception) {
        this.type = type;
        this.pluginId = pluginId;
        this.exception = exception;
    }

    // Getters
    public Type getType() { return type; }
    public String getPluginId() { return pluginId; }
    public Exception getException() { return exception; }
}
