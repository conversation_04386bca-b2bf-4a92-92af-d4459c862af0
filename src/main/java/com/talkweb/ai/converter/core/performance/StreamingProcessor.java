package com.talkweb.ai.converter.core.performance;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.function.Function;

/**
 * Memory-efficient streaming processor for large files
 * 
 * This class provides streaming capabilities for processing large documents
 * without loading the entire file into memory at once.
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class StreamingProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(StreamingProcessor.class);
    
    private static final int DEFAULT_BUFFER_SIZE = 64 * 1024; // 64KB
    private static final int MAX_BUFFER_SIZE = 1024 * 1024; // 1MB
    
    private final int bufferSize;
    private final boolean useDirectBuffers;
    
    public StreamingProcessor() {
        this(DEFAULT_BUFFER_SIZE, false);
    }
    
    public StreamingProcessor(int bufferSize, boolean useDirectBuffers) {
        this.bufferSize = Math.min(bufferSize, MAX_BUFFER_SIZE);
        this.useDirectBuffers = useDirectBuffers;
    }
    
    /**
     * Process a file in streaming fashion with a chunk processor
     * 
     * @param inputFile the file to process
     * @param chunkProcessor function to process each chunk
     * @param outputFile the output file
     * @throws IOException if an I/O error occurs
     */
    public void processFileStreaming(File inputFile, Function<byte[], String> chunkProcessor, File outputFile) 
            throws IOException {
        
        logger.info("Starting streaming processing of file: {} (size: {} bytes)", 
                inputFile.getName(), inputFile.length());
        
        long startTime = System.currentTimeMillis();
        long totalBytesProcessed = 0;
        int chunkCount = 0;
        
        try (FileChannel inputChannel = FileChannel.open(inputFile.toPath(), StandardOpenOption.READ);
             FileWriter outputWriter = new FileWriter(outputFile)) {
            
            ByteBuffer buffer = useDirectBuffers ? 
                ByteBuffer.allocateDirect(bufferSize) : 
                ByteBuffer.allocate(bufferSize);
            
            while (inputChannel.read(buffer) > 0) {
                buffer.flip();
                
                byte[] chunk = new byte[buffer.remaining()];
                buffer.get(chunk);
                
                // Process the chunk
                String processedChunk = chunkProcessor.apply(chunk);
                if (processedChunk != null) {
                    outputWriter.write(processedChunk);
                }
                
                totalBytesProcessed += chunk.length;
                chunkCount++;
                
                buffer.clear();
                
                // Log progress every 100 chunks
                if (chunkCount % 100 == 0) {
                    logger.debug("Processed {} chunks, {} bytes", chunkCount, totalBytesProcessed);
                }
            }
        }
        
        long processingTime = System.currentTimeMillis() - startTime;
        logger.info("Streaming processing completed: {} chunks, {} bytes in {} ms", 
                chunkCount, totalBytesProcessed, processingTime);
    }
    
    /**
     * Process a text file line by line in streaming fashion
     * 
     * @param inputFile the file to process
     * @param lineProcessor function to process each line
     * @param outputFile the output file
     * @throws IOException if an I/O error occurs
     */
    public void processTextFileStreaming(File inputFile, Function<String, String> lineProcessor, File outputFile) 
            throws IOException {
        
        logger.info("Starting line-by-line streaming processing of file: {}", inputFile.getName());
        
        long startTime = System.currentTimeMillis();
        long lineCount = 0;
        
        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile), bufferSize);
             BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile), bufferSize)) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                String processedLine = lineProcessor.apply(line);
                if (processedLine != null) {
                    writer.write(processedLine);
                    writer.newLine();
                }
                
                lineCount++;
                
                // Log progress every 10000 lines
                if (lineCount % 10000 == 0) {
                    logger.debug("Processed {} lines", lineCount);
                }
            }
        }
        
        long processingTime = System.currentTimeMillis() - startTime;
        logger.info("Line streaming processing completed: {} lines in {} ms", lineCount, processingTime);
    }
    
    /**
     * Process a file with memory-mapped I/O for very large files
     * 
     * @param inputFile the file to process
     * @param chunkProcessor function to process each chunk
     * @param outputFile the output file
     * @throws IOException if an I/O error occurs
     */
    public void processFileMemoryMapped(File inputFile, Function<byte[], String> chunkProcessor, File outputFile) 
            throws IOException {
        
        logger.info("Starting memory-mapped processing of file: {} (size: {} bytes)", 
                inputFile.getName(), inputFile.length());
        
        long startTime = System.currentTimeMillis();
        long fileSize = inputFile.length();
        long totalBytesProcessed = 0;
        int chunkCount = 0;
        
        try (FileChannel inputChannel = FileChannel.open(inputFile.toPath(), StandardOpenOption.READ);
             FileWriter outputWriter = new FileWriter(outputFile)) {
            
            long position = 0;
            while (position < fileSize) {
                long remainingBytes = fileSize - position;
                int mapSize = (int) Math.min(bufferSize, remainingBytes);
                
                var mappedBuffer = inputChannel.map(FileChannel.MapMode.READ_ONLY, position, mapSize);
                
                byte[] chunk = new byte[mapSize];
                mappedBuffer.get(chunk);
                
                // Process the chunk
                String processedChunk = chunkProcessor.apply(chunk);
                if (processedChunk != null) {
                    outputWriter.write(processedChunk);
                }
                
                position += mapSize;
                totalBytesProcessed += mapSize;
                chunkCount++;
                
                // Log progress every 100 chunks
                if (chunkCount % 100 == 0) {
                    logger.debug("Memory-mapped processed {} chunks, {} bytes", chunkCount, totalBytesProcessed);
                }
            }
        }
        
        long processingTime = System.currentTimeMillis() - startTime;
        logger.info("Memory-mapped processing completed: {} chunks, {} bytes in {} ms", 
                chunkCount, totalBytesProcessed, processingTime);
    }
    
    /**
     * Estimate optimal buffer size based on file size and available memory
     * 
     * @param fileSize the size of the file to process
     * @return optimal buffer size
     */
    public static int estimateOptimalBufferSize(long fileSize) {
        Runtime runtime = Runtime.getRuntime();
        long availableMemory = runtime.maxMemory() - (runtime.totalMemory() - runtime.freeMemory());
        
        // Use at most 1% of available memory for buffer
        long maxBufferSize = Math.max(DEFAULT_BUFFER_SIZE, availableMemory / 100);
        
        // For small files, use smaller buffers
        if (fileSize < 1024 * 1024) { // < 1MB
            return Math.min(DEFAULT_BUFFER_SIZE, (int) fileSize);
        }
        
        // For large files, use larger buffers but cap at MAX_BUFFER_SIZE
        return (int) Math.min(MAX_BUFFER_SIZE, maxBufferSize);
    }
    
    /**
     * Check if streaming processing is recommended for the given file
     * 
     * @param fileSize the size of the file
     * @return true if streaming is recommended
     */
    public static boolean shouldUseStreaming(long fileSize) {
        Runtime runtime = Runtime.getRuntime();
        long availableMemory = runtime.maxMemory() - (runtime.totalMemory() - runtime.freeMemory());
        
        // Use streaming if file is larger than 10% of available memory
        return fileSize > (availableMemory * 0.1);
    }
    
    /**
     * Get current buffer size
     */
    public int getBufferSize() {
        return bufferSize;
    }
    
    /**
     * Check if using direct buffers
     */
    public boolean isUsingDirectBuffers() {
        return useDirectBuffers;
    }
}
