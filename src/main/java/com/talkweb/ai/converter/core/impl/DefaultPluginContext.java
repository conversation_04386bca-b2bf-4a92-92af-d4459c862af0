package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.core.PluginContext;
import com.talkweb.ai.converter.core.PluginManager;
import org.slf4j.Logger;

import java.nio.file.Path;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认插件上下文实现
 */
public class DefaultPluginContext implements PluginContext {

    private final PluginManager pluginManager;
    private final ClassLoader pluginClassLoader;
    private final Properties configuration;
    private final Path workDir;
    private final Path tempDir;
    private final Logger logger;
    private final Map<String, Object> sharedObjects = new ConcurrentHashMap<>();

    public DefaultPluginContext(PluginManager pluginManager,
    ClassLoader pluginClassLoader,
                                Properties configuration,
                                Path workDir,
                                Path tempDir,
                                 Logger logger) {
        this.pluginManager = pluginManager;
        this.pluginClassLoader = pluginClassLoader;
        this.configuration = configuration;
        this.workDir = workDir;
        this.tempDir = tempDir;
        this.logger = logger;
    }

    @Override
    public ClassLoader getPluginClassLoader() {
        return pluginClassLoader;
    }

    @Override
    public Properties getConfiguration() {
        return configuration;
    }

    @Override
    public PluginManager getPluginManager() {
        return pluginManager;
    }

    @Override
    public String getWorkDir() {
        return workDir.toAbsolutePath().toString();
    }

    @Override
    public String getTempDir() {
        return tempDir.toAbsolutePath().toString();
    }

    @Override
    public void info(String message, Object... args) {
        logger.info(message, args);
    }

    @Override
    public void warn(String message, Object... args) {
        logger.warn(message, args);
    }

    @Override
    public void error(String message, Throwable throwable, Object... args) {
        logger.error(message, throwable, args);
    }

    @Override
    public void debug(String message, Object... args) {
        logger.debug(message, args);
    }

    @Override
    public void setSharedObject(String key, Object value) {
        sharedObjects.put(key, value);
    }

    @Override
    public Optional<Object> getSharedObject(String key) {
        return Optional.ofNullable(sharedObjects.get(key));
    }
}
