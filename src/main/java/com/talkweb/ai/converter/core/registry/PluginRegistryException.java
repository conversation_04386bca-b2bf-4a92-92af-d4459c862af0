package com.talkweb.ai.converter.core.registry;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginState;

/**
 * 插件注册表异常
 */
public class PluginRegistryException extends PluginException {
    
    private final String pluginId;
    private final RegistryErrorType errorType;

    public PluginRegistryException(String message, String pluginId) {
        this(message, pluginId, RegistryErrorType.UNKNOWN, null);
    }

    public PluginRegistryException(String message, String pluginId, Throwable cause) {
        this(message, pluginId, RegistryErrorType.UNKNOWN, cause);
    }

    public PluginRegistryException(String message, String pluginId, RegistryErrorType errorType, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginId = pluginId;
        this.errorType = errorType;
    }

    public String getPluginId() {
        return pluginId;
    }

    public RegistryErrorType getErrorType() {
        return errorType;
    }

    /**
     * 注册表错误类型
     */
    public enum RegistryErrorType {
        ALREADY_REGISTERED("Plugin already registered"),
        NOT_FOUND("Plugin not found"),
        INVALID_METADATA("Invalid plugin metadata"),
        DUPLICATE_ID("Duplicate plugin ID"),
        REGISTRATION_FAILED("Plugin registration failed"),
        UNREGISTRATION_FAILED("Plugin unregistration failed"),
        STATE_UPDATE_FAILED("Plugin state update failed"),
        UNKNOWN("Unknown error");

        private final String description;

        RegistryErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 工厂方法
    public static PluginRegistryException alreadyRegistered(String pluginId) {
        return new PluginRegistryException(
            "Plugin already registered: " + pluginId,
            pluginId,
            RegistryErrorType.ALREADY_REGISTERED,
            null
        );
    }

    public static PluginRegistryException notFound(String pluginId) {
        return new PluginRegistryException(
            "Plugin not found: " + pluginId,
            pluginId,
            RegistryErrorType.NOT_FOUND,
            null
        );
    }

    public static PluginRegistryException invalidMetadata(String pluginId, String reason) {
        return new PluginRegistryException(
            "Invalid plugin metadata: " + reason + " (Plugin: " + pluginId + ")",
            pluginId,
            RegistryErrorType.INVALID_METADATA,
            null
        );
    }

    public static PluginRegistryException duplicateId(String pluginId) {
        return new PluginRegistryException(
            "Duplicate plugin ID: " + pluginId,
            pluginId,
            RegistryErrorType.DUPLICATE_ID,
            null
        );
    }
}
