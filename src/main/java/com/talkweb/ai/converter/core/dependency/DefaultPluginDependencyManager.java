package com.talkweb.ai.converter.core.dependency;

import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 默认插件依赖管理器实现
 */
public class DefaultPluginDependencyManager implements PluginDependencyManager {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultPluginDependencyManager.class);
    
    private final Map<String, List<PluginDependency>> dependencyCache = new HashMap<>();

    @Override
    public List<PluginDependency> resolveDependencies(Plugin plugin) throws PluginDependencyException {
        String pluginId = plugin.getMetadata().getId();
        
        // 检查缓存
        if (dependencyCache.containsKey(pluginId)) {
            return new ArrayList<>(dependencyCache.get(pluginId));
        }
        
        try {
            List<PluginDependency> dependencies = parseDependenciesFromMetadata(plugin.getMetadata());
            dependencyCache.put(pluginId, dependencies);
            return dependencies;
        } catch (Exception e) {
            throw PluginDependencyException.resolutionFailed(pluginId, e.getMessage(), e);
        }
    }

    @Override
    public DependencyCheckResult checkDependencies(Plugin plugin, List<Plugin> availablePlugins) {
        List<PluginDependency> dependencies;
        try {
            dependencies = resolveDependencies(plugin);
        } catch (PluginDependencyException e) {
            return new DependencyCheckResult(false, Collections.emptyList(), 
                                           Collections.emptyList(), 
                                           List.of("Failed to resolve dependencies: " + e.getMessage()));
        }
        
        List<PluginDependency> missingDependencies = new ArrayList<>();
        List<PluginDependency> incompatibleDependencies = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        Map<String, Plugin> availablePluginMap = availablePlugins.stream()
            .collect(Collectors.toMap(p -> p.getMetadata().getId(), p -> p));
        
        for (PluginDependency dependency : dependencies) {
            Plugin availablePlugin = availablePluginMap.get(dependency.getPluginId());
            
            if (availablePlugin == null) {
                if (!dependency.isOptional()) {
                    missingDependencies.add(dependency);
                } else {
                    warnings.add("Optional dependency not found: " + dependency.getPluginId());
                }
            } else if (!isVersionCompatible(dependency, availablePlugin)) {
                if (!dependency.isOptional()) {
                    incompatibleDependencies.add(dependency);
                } else {
                    warnings.add("Optional dependency version incompatible: " + dependency.getPluginId());
                }
            }
        }
        
        boolean satisfied = missingDependencies.isEmpty() && incompatibleDependencies.isEmpty();
        return new DependencyCheckResult(satisfied, missingDependencies, incompatibleDependencies, warnings);
    }

    @Override
    public CircularDependencyResult detectCircularDependencies(List<Plugin> plugins) {
        Map<String, List<String>> dependencyGraph = buildDependencyGraph(plugins);
        List<List<String>> circularChains = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        Set<String> recursionStack = new HashSet<>();
        
        for (String pluginId : dependencyGraph.keySet()) {
            if (!visited.contains(pluginId)) {
                List<String> path = new ArrayList<>();
                if (detectCyclesDFS(pluginId, dependencyGraph, visited, recursionStack, path, circularChains)) {
                    // 找到循环依赖
                }
            }
        }
        
        return new CircularDependencyResult(!circularChains.isEmpty(), circularChains);
    }

    @Override
    public List<Plugin> calculateLoadOrder(List<Plugin> plugins) throws PluginDependencyException {
        // 检测循环依赖
        CircularDependencyResult circularResult = detectCircularDependencies(plugins);
        if (circularResult.hasCircularDependency()) {
            throw PluginDependencyException.circularDependency("", 
                circularResult.getCircularChains().get(0));
        }
        
        // 使用拓扑排序计算加载顺序
        Map<String, Plugin> pluginMap = plugins.stream()
            .collect(Collectors.toMap(p -> p.getMetadata().getId(), p -> p));
        
        Map<String, List<String>> dependencyGraph = buildDependencyGraph(plugins);
        List<String> sortedIds = topologicalSort(dependencyGraph);
        
        return sortedIds.stream()
            .map(pluginMap::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    @Override
    public List<PluginDependency> getDirectDependencies(Plugin plugin) {
        try {
            return resolveDependencies(plugin);
        } catch (PluginDependencyException e) {
            log.warn("Failed to get direct dependencies for plugin: {}", plugin.getMetadata().getId(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<PluginDependency> getTransitiveDependencies(Plugin plugin, List<Plugin> availablePlugins) {
        Set<PluginDependency> transitiveDeps = new HashSet<>();
        Set<String> visited = new HashSet<>();
        
        collectTransitiveDependencies(plugin, availablePlugins, transitiveDeps, visited);
        
        return new ArrayList<>(transitiveDeps);
    }

    @Override
    public List<Plugin> getDependents(String pluginId, List<Plugin> availablePlugins) {
        return availablePlugins.stream()
            .filter(plugin -> {
                List<PluginDependency> deps = getDirectDependencies(plugin);
                return deps.stream().anyMatch(dep -> dep.getPluginId().equals(pluginId));
            })
            .collect(Collectors.toList());
    }

    @Override
    public boolean isVersionCompatible(PluginDependency dependency, Plugin availablePlugin) {
        String availableVersion = availablePlugin.getMetadata().getVersion();
        return dependency.isVersionCompatible(availableVersion);
    }

    /**
     * 从插件元数据解析依赖信息
     */
    private List<PluginDependency> parseDependenciesFromMetadata(PluginMetadata metadata) {
        List<PluginDependency> dependencies = new ArrayList<>();
        
        // 这里假设依赖信息存储在元数据的描述或其他字段中
        // 实际实现可能需要根据具体的元数据格式调整
        String description = metadata.getDescription();
        if (description != null && description.contains("depends:")) {
            // 简单的依赖解析逻辑
            String[] parts = description.split("depends:");
            if (parts.length > 1) {
                String dependsStr = parts[1].trim();
                String[] depEntries = dependsStr.split(",");
                
                for (String depEntry : depEntries) {
                    try {
                        PluginDependency dependency = parseDependencyString(depEntry.trim());
                        dependencies.add(dependency);
                    } catch (Exception e) {
                        log.warn("Failed to parse dependency: {}", depEntry, e);
                    }
                }
            }
        }
        
        return dependencies;
    }

    /**
     * 解析依赖字符串
     */
    private PluginDependency parseDependencyString(String dependencyStr) {
        // 格式: pluginId:version[:optional]
        String[] parts = dependencyStr.split(":");
        if (parts.length < 2) {
            throw new IllegalArgumentException("Invalid dependency format: " + dependencyStr);
        }
        
        String pluginId = parts[0].trim();
        String version = parts[1].trim();
        boolean optional = parts.length > 2 && "optional".equals(parts[2].trim());
        
        return PluginDependency.builder()
            .pluginId(pluginId)
            .version(version)
            .optional(optional)
            .type(PluginDependency.DependencyType.RUNTIME)
            .build();
    }

    /**
     * 构建依赖图
     */
    private Map<String, List<String>> buildDependencyGraph(List<Plugin> plugins) {
        Map<String, List<String>> graph = new HashMap<>();

        for (Plugin plugin : plugins) {
            String pluginId = plugin.getMetadata().getId();
            List<String> dependencies = getDirectDependencies(plugin).stream()
                .map(PluginDependency::getPluginId)
                .collect(Collectors.toList());
            graph.put(pluginId, dependencies);
        }

        return graph;
    }

    /**
     * 使用DFS检测循环依赖
     */
    private boolean detectCyclesDFS(String node, Map<String, List<String>> graph,
                                   Set<String> visited, Set<String> recursionStack,
                                   List<String> path, List<List<String>> circularChains) {
        visited.add(node);
        recursionStack.add(node);
        path.add(node);

        List<String> neighbors = graph.getOrDefault(node, Collections.emptyList());
        for (String neighbor : neighbors) {
            if (!visited.contains(neighbor)) {
                if (detectCyclesDFS(neighbor, graph, visited, recursionStack, path, circularChains)) {
                    return true;
                }
            } else if (recursionStack.contains(neighbor)) {
                // 找到循环依赖
                int cycleStart = path.indexOf(neighbor);
                List<String> cycle = new ArrayList<>(path.subList(cycleStart, path.size()));
                cycle.add(neighbor); // 完成循环
                circularChains.add(cycle);
                return true;
            }
        }

        recursionStack.remove(node);
        path.remove(path.size() - 1);
        return false;
    }

    /**
     * 拓扑排序
     */
    private List<String> topologicalSort(Map<String, List<String>> graph) {
        Map<String, Integer> inDegree = new HashMap<>();

        // 计算入度
        for (String node : graph.keySet()) {
            inDegree.putIfAbsent(node, 0);
            for (String neighbor : graph.get(node)) {
                inDegree.put(neighbor, inDegree.getOrDefault(neighbor, 0) + 1);
            }
        }

        // 找到所有入度为0的节点
        Queue<String> queue = new LinkedList<>();
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.offer(entry.getKey());
            }
        }

        List<String> result = new ArrayList<>();
        while (!queue.isEmpty()) {
            String node = queue.poll();
            result.add(node);

            for (String neighbor : graph.getOrDefault(node, Collections.emptyList())) {
                inDegree.put(neighbor, inDegree.get(neighbor) - 1);
                if (inDegree.get(neighbor) == 0) {
                    queue.offer(neighbor);
                }
            }
        }

        return result;
    }

    /**
     * 收集传递依赖
     */
    private void collectTransitiveDependencies(Plugin plugin, List<Plugin> availablePlugins,
                                             Set<PluginDependency> transitiveDeps, Set<String> visited) {
        String pluginId = plugin.getMetadata().getId();
        if (visited.contains(pluginId)) {
            return;
        }

        visited.add(pluginId);
        List<PluginDependency> directDeps = getDirectDependencies(plugin);

        for (PluginDependency dep : directDeps) {
            transitiveDeps.add(dep);

            // 查找依赖的插件
            Optional<Plugin> depPlugin = availablePlugins.stream()
                .filter(p -> p.getMetadata().getId().equals(dep.getPluginId()))
                .findFirst();

            if (depPlugin.isPresent()) {
                collectTransitiveDependencies(depPlugin.get(), availablePlugins, transitiveDeps, visited);
            }
        }
    }

    /**
     * 清除依赖缓存
     */
    public void clearCache() {
        dependencyCache.clear();
    }

    /**
     * 清除指定插件的依赖缓存
     */
    public void clearCache(String pluginId) {
        dependencyCache.remove(pluginId);
    }
}
