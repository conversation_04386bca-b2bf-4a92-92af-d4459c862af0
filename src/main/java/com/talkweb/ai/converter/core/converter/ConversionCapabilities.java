package com.talkweb.ai.converter.core.converter;

import java.util.*;

/**
 * 转换器能力描述类，定义了转换器支持的功能和特性
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ConversionCapabilities {
    
    private final Set<String> supportedFeatures;
    private final Map<String, Object> capabilities;
    
    /**
     * 创建转换器能力描述
     */
    public ConversionCapabilities() {
        this.supportedFeatures = new HashSet<>();
        this.capabilities = new HashMap<>();
    }
    
    /**
     * 创建转换器能力描述
     * 
     * @param supportedFeatures 支持的特性集合
     * @param capabilities 能力映射
     */
    public ConversionCapabilities(Set<String> supportedFeatures, Map<String, Object> capabilities) {
        this.supportedFeatures = new HashSet<>(supportedFeatures);
        this.capabilities = new HashMap<>(capabilities);
    }
    
    /**
     * 检查是否支持指定特性
     * 
     * @param feature 特性名称
     * @return 如果支持返回true，否则返回false
     */
    public boolean supportsFeature(String feature) {
        return supportedFeatures.contains(feature);
    }
    
    /**
     * 获取支持的特性集合
     * 
     * @return 不可变的特性集合
     */
    public Set<String> getSupportedFeatures() {
        return Collections.unmodifiableSet(supportedFeatures);
    }
    
    /**
     * 获取能力值
     * 
     * @param key 能力键
     * @return 能力值的Optional包装
     */
    public Optional<Object> getCapability(String key) {
        return Optional.ofNullable(capabilities.get(key));
    }
    
    /**
     * 获取能力值，如果不存在则返回默认值
     * 
     * @param key 能力键
     * @param defaultValue 默认值
     * @param <T> 能力类型
     * @return 能力值，如果不存在则返回默认值
     */
    @SuppressWarnings("unchecked")
    public <T> T getCapability(String key, T defaultValue) {
        Object value = capabilities.get(key);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return (T) value;
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }
    
    /**
     * 获取所有能力
     * 
     * @return 不可变的能力映射
     */
    public Map<String, Object> getAllCapabilities() {
        return Collections.unmodifiableMap(capabilities);
    }
    
    /**
     * 创建能力描述构建器
     * 
     * @return 能力描述构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 能力描述构建器
     */
    public static class Builder {
        private final Set<String> supportedFeatures = new HashSet<>();
        private final Map<String, Object> capabilities = new HashMap<>();
        
        /**
         * 添加支持的特性
         * 
         * @param feature 特性名称
         * @return 当前构建器
         */
        public Builder feature(String feature) {
            supportedFeatures.add(feature);
            return this;
        }
        
        /**
         * 添加支持的特性集合
         * 
         * @param features 特性集合
         * @return 当前构建器
         */
        public Builder features(Collection<String> features) {
            supportedFeatures.addAll(features);
            return this;
        }
        
        /**
         * 添加能力
         * 
         * @param key 能力键
         * @param value 能力值
         * @return 当前构建器
         */
        public Builder capability(String key, Object value) {
            capabilities.put(key, value);
            return this;
        }
        
        /**
         * 构建能力描述对象
         * 
         * @return 能力描述对象
         */
        public ConversionCapabilities build() {
            return new ConversionCapabilities(supportedFeatures, capabilities);
        }
    }
    
    /**
     * 常用特性常量
     */
    public static final class Features {
        public static final String TABLES = "tables";
        public static final String IMAGES = "images";
        public static final String LINKS = "links";
        public static final String LISTS = "lists";
        public static final String HEADINGS = "headings";
        public static final String CODE_BLOCKS = "codeBlocks";
        public static final String MATH = "math";
        public static final String FOOTNOTES = "footnotes";
        public static final String METADATA = "metadata";
        public static final String STREAMING = "streaming";
        public static final String INCREMENTAL = "incremental";
        
        private Features() {
            // 防止实例化
        }
    }
}