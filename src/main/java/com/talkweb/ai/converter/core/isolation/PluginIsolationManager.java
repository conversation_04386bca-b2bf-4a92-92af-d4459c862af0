package com.talkweb.ai.converter.core.isolation;

import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.classloader.IsolatedPluginClassLoader;
import com.talkweb.ai.converter.core.security.PluginSandbox;
import com.talkweb.ai.converter.core.security.PluginSecurityManagerImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 插件隔离管理器
 * 负责管理插件间的隔离机制，包括类加载隔离、安全隔离等
 */
public class PluginIsolationManager {
    
    private static final Logger log = LoggerFactory.getLogger(PluginIsolationManager.class);
    
    private final Map<String, IsolatedPluginClassLoader> pluginClassLoaders = new ConcurrentHashMap<>();
    private final Map<String, PluginSandbox> pluginSandboxes = new ConcurrentHashMap<>();
    private final Map<String, PluginIsolationContext> isolationContexts = new ConcurrentHashMap<>();
    
    private PluginSecurityManagerImpl securityManager;
    private boolean isolationEnabled = true;

    public PluginIsolationManager() {
        initializeSecurityManager();
    }

    /**
     * 为插件创建隔离环境
     */
    public PluginIsolationContext createIsolationContext(String pluginId, Path pluginPath, 
                                                        PluginSandbox sandbox) throws PluginIsolationException {
        if (!isolationEnabled) {
            log.debug("Plugin isolation is disabled");
            return null;
        }
        
        try {
            log.info("Creating isolation context for plugin: {}", pluginId);
            
            // 创建隔离的类加载器
            IsolatedPluginClassLoader classLoader = createIsolatedClassLoader(pluginId, pluginPath);
            
            // 注册沙箱到安全管理器
            if (securityManager != null) {
                securityManager.registerPluginSandbox(pluginId, sandbox);
            }
            
            // 创建隔离上下文
            PluginIsolationContext context = new PluginIsolationContext(
                pluginId, classLoader, sandbox, this
            );
            
            // 缓存相关对象
            pluginClassLoaders.put(pluginId, classLoader);
            pluginSandboxes.put(pluginId, sandbox);
            isolationContexts.put(pluginId, context);
            
            log.info("Successfully created isolation context for plugin: {}", pluginId);
            return context;
            
        } catch (Exception e) {
            throw new PluginIsolationException("Failed to create isolation context for plugin: " + pluginId, e);
        }
    }

    /**
     * 销毁插件的隔离环境
     */
    public void destroyIsolationContext(String pluginId) {
        log.info("Destroying isolation context for plugin: {}", pluginId);
        
        try {
            // 移除隔离上下文
            PluginIsolationContext context = isolationContexts.remove(pluginId);
            if (context != null) {
                context.destroy();
            }
            
            // 关闭类加载器
            IsolatedPluginClassLoader classLoader = pluginClassLoaders.remove(pluginId);
            if (classLoader != null) {
                classLoader.close();
            }
            
            // 移除沙箱
            pluginSandboxes.remove(pluginId);
            
            // 从安全管理器注销
            if (securityManager != null) {
                securityManager.unregisterPluginSandbox(pluginId);
            }
            
            log.info("Successfully destroyed isolation context for plugin: {}", pluginId);
            
        } catch (Exception e) {
            log.error("Error destroying isolation context for plugin: {}", pluginId, e);
        }
    }

    /**
     * 获取插件的隔离上下文
     */
    public PluginIsolationContext getIsolationContext(String pluginId) {
        return isolationContexts.get(pluginId);
    }

    /**
     * 检查插件是否在隔离环境中运行
     */
    public boolean isPluginIsolated(String pluginId) {
        return isolationContexts.containsKey(pluginId);
    }

    /**
     * 配置插件间的通信权限
     */
    public void configurePluginCommunication(String fromPluginId, String toPluginId, boolean allowed) {
        IsolatedPluginClassLoader fromClassLoader = pluginClassLoaders.get(fromPluginId);
        if (fromClassLoader != null) {
            if (allowed) {
                // 允许访问目标插件的包
                fromClassLoader.addAllowedPackage("com.talkweb.ai.converter.plugins." + toPluginId);
                log.info("Allowed communication from plugin {} to plugin {}", fromPluginId, toPluginId);
            } else {
                // 阻止访问目标插件的包
                fromClassLoader.addBlockedPackage("com.talkweb.ai.converter.plugins." + toPluginId);
                log.info("Blocked communication from plugin {} to plugin {}", fromPluginId, toPluginId);
            }
        }
    }

    /**
     * 设置插件的共享包
     */
    public void setSharedPackages(String pluginId, String... packages) {
        IsolatedPluginClassLoader classLoader = pluginClassLoaders.get(pluginId);
        if (classLoader != null) {
            for (String packageName : packages) {
                classLoader.addSharedPackage(packageName);
            }
            log.info("Set shared packages for plugin {}: {}", pluginId, String.join(", ", packages));
        }
    }

    /**
     * 启用或禁用插件隔离
     */
    public void setIsolationEnabled(boolean enabled) {
        this.isolationEnabled = enabled;
        log.info("Plugin isolation {}", enabled ? "enabled" : "disabled");
    }

    /**
     * 检查隔离是否启用
     */
    public boolean isIsolationEnabled() {
        return isolationEnabled;
    }

    /**
     * 获取隔离统计信息
     */
    public IsolationStatistics getStatistics() {
        return new IsolationStatistics(
            isolationContexts.size(),
            pluginClassLoaders.size(),
            pluginSandboxes.size(),
            isolationEnabled
        );
    }

    /**
     * 清理所有隔离环境
     */
    public void cleanup() {
        log.info("Cleaning up all plugin isolation contexts");
        
        // 销毁所有隔离上下文
        for (String pluginId : isolationContexts.keySet()) {
            destroyIsolationContext(pluginId);
        }
        
        // 恢复原始安全管理器
        if (securityManager != null) {
            securityManager.restore();
            securityManager = null;
        }
        
        log.info("Plugin isolation cleanup completed");
    }

    /**
     * 创建隔离的类加载器
     */
    private IsolatedPluginClassLoader createIsolatedClassLoader(String pluginId, Path pluginPath) throws IOException, PluginIsolationException {
        // 检查文件是否存在
        if (!Files.exists(pluginPath)) {
            throw new PluginIsolationException("Plugin file does not exist: " + pluginPath, pluginId,
                PluginIsolationException.IsolationErrorType.CLASS_LOADER_CREATION_FAILED, null);
        }

        URL pluginUrl = pluginPath.toUri().toURL();
        IsolatedPluginClassLoader classLoader = new IsolatedPluginClassLoader(
            pluginId,
            new URL[]{pluginUrl},
            getClass().getClassLoader()
        );

        // 配置默认的隔离规则
        configureDefaultIsolationRules(classLoader, pluginId);

        return classLoader;
    }

    /**
     * 配置默认的隔离规则
     */
    private void configureDefaultIsolationRules(IsolatedPluginClassLoader classLoader, String pluginId) {
        // 阻止访问其他插件的包（除了当前插件）
        classLoader.addBlockedPackage("com.talkweb.ai.converter.plugins.");
        classLoader.addAllowedPackage("com.talkweb.ai.converter.plugins." + pluginId);
        
        // 允许访问公共API
        classLoader.addSharedPackage("com.talkweb.ai.converter.core.");
        classLoader.addSharedPackage("com.talkweb.ai.converter.api.");
        
        log.debug("Configured default isolation rules for plugin: {}", pluginId);
    }

    /**
     * 初始化安全管理器
     */
    private void initializeSecurityManager() {
        try {
            // 检查是否支持SecurityManager
            String javaVersion = System.getProperty("java.version");
            if (javaVersion.startsWith("17") || javaVersion.startsWith("18") ||
                javaVersion.startsWith("19") || javaVersion.startsWith("20") ||
                javaVersion.startsWith("21") || javaVersion.startsWith("22")) {
                log.warn("SecurityManager is deprecated in Java 17+, using alternative isolation mechanisms");
                return;
            }

            // 只有在没有安全管理器或者当前安全管理器不是我们的实现时才设置
            SecurityManager currentSM = System.getSecurityManager();
            if (!(currentSM instanceof PluginSecurityManagerImpl)) {
                securityManager = new PluginSecurityManagerImpl();
                System.setSecurityManager(securityManager);
                log.info("Installed plugin security manager");
            } else {
                securityManager = (PluginSecurityManagerImpl) currentSM;
                log.debug("Using existing plugin security manager");
            }
        } catch (SecurityException | UnsupportedOperationException e) {
            log.warn("Failed to install security manager, using alternative isolation mechanisms: {}", e.getMessage());
        }
    }

    /**
     * 隔离统计信息
     */
    public static class IsolationStatistics {
        private final int isolatedPlugins;
        private final int classLoaders;
        private final int sandboxes;
        private final boolean isolationEnabled;

        public IsolationStatistics(int isolatedPlugins, int classLoaders, int sandboxes, boolean isolationEnabled) {
            this.isolatedPlugins = isolatedPlugins;
            this.classLoaders = classLoaders;
            this.sandboxes = sandboxes;
            this.isolationEnabled = isolationEnabled;
        }

        public int getIsolatedPlugins() { return isolatedPlugins; }
        public int getClassLoaders() { return classLoaders; }
        public int getSandboxes() { return sandboxes; }
        public boolean isIsolationEnabled() { return isolationEnabled; }
    }
}
