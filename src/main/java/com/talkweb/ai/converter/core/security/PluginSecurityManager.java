package com.talkweb.ai.converter.core.security;

import com.talkweb.ai.converter.core.Plugin;

import java.nio.file.Path;
import java.security.Permission;
import java.util.List;
import java.util.Set;

/**
 * 插件安全管理器接口
 * 负责插件的安全验证、权限控制和沙箱隔离
 */
public interface PluginSecurityManager {

    /**
     * 验证插件签名
     * 
     * @param pluginPath 插件文件路径
     * @return 签名验证结果
     */
    SignatureVerificationResult verifySignature(Path pluginPath);

    /**
     * 检查插件权限
     * 
     * @param plugin 插件实例
     * @param permission 请求的权限
     * @return 如果允许访问返回true
     */
    boolean checkPermission(Plugin plugin, Permission permission);

    /**
     * 为插件分配权限
     * 
     * @param pluginId 插件ID
     * @param permissions 权限集合
     * @throws PluginSecurityException 如果分配失败
     */
    void grantPermissions(String pluginId, Set<Permission> permissions) throws PluginSecurityException;

    /**
     * 撤销插件权限
     * 
     * @param pluginId 插件ID
     * @param permissions 要撤销的权限集合
     */
    void revokePermissions(String pluginId, Set<Permission> permissions);

    /**
     * 获取插件的所有权限
     * 
     * @param pluginId 插件ID
     * @return 权限集合
     */
    Set<Permission> getPluginPermissions(String pluginId);

    /**
     * 创建插件沙箱环境
     * 
     * @param pluginId 插件ID
     * @return 沙箱环境
     * @throws PluginSecurityException 如果创建失败
     */
    PluginSandbox createSandbox(String pluginId) throws PluginSecurityException;

    /**
     * 销毁插件沙箱环境
     * 
     * @param pluginId 插件ID
     */
    void destroySandbox(String pluginId);

    /**
     * 检查插件是否在沙箱中运行
     * 
     * @param pluginId 插件ID
     * @return 如果在沙箱中返回true
     */
    boolean isInSandbox(String pluginId);

    /**
     * 添加安全策略
     * 
     * @param policy 安全策略
     */
    void addSecurityPolicy(SecurityPolicy policy);

    /**
     * 移除安全策略
     * 
     * @param policyId 策略ID
     */
    void removeSecurityPolicy(String policyId);

    /**
     * 获取所有安全策略
     * 
     * @return 安全策略列表
     */
    List<SecurityPolicy> getSecurityPolicies();

    /**
     * 审计插件安全事件
     * 
     * @param event 安全事件
     */
    void auditSecurityEvent(SecurityEvent event);

    /**
     * 获取安全审计日志
     * 
     * @param pluginId 插件ID（可选，为null时返回所有日志）
     * @return 审计日志列表
     */
    List<SecurityEvent> getAuditLog(String pluginId);

    /**
     * 签名验证结果
     */
    class SignatureVerificationResult {
        private final boolean valid;
        private final String signerInfo;
        private final String errorMessage;
        private final VerificationError errorType;

        public SignatureVerificationResult(boolean valid, String signerInfo) {
            this(valid, signerInfo, null, null);
        }

        public SignatureVerificationResult(boolean valid, String signerInfo, 
                                         String errorMessage, VerificationError errorType) {
            this.valid = valid;
            this.signerInfo = signerInfo;
            this.errorMessage = errorMessage;
            this.errorType = errorType;
        }

        public boolean isValid() {
            return valid;
        }

        public String getSignerInfo() {
            return signerInfo;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public VerificationError getErrorType() {
            return errorType;
        }

        public enum VerificationError {
            NO_SIGNATURE,
            INVALID_SIGNATURE,
            UNTRUSTED_SIGNER,
            EXPIRED_CERTIFICATE,
            REVOKED_CERTIFICATE,
            UNKNOWN_ERROR
        }
    }
}
