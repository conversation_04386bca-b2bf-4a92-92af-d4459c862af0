package com.talkweb.ai.converter.core.classloader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URL;
import java.net.URLClassLoader;
import java.security.CodeSource;
import java.security.PermissionCollection;
import java.security.Permissions;
import java.security.ProtectionDomain;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 隔离的插件类加载器
 * 提供插件间的类加载隔离和安全控制
 */
public class IsolatedPluginClassLoader extends URLClassLoader {
    
    private static final Logger log = LoggerFactory.getLogger(IsolatedPluginClassLoader.class);
    
    private final String pluginId;
    private final Set<String> allowedPackages = ConcurrentHashMap.newKeySet();
    private final Set<String> blockedPackages = ConcurrentHashMap.newKeySet();
    private final Set<String> sharedPackages = ConcurrentHashMap.newKeySet();
    private final ClassLoader parentClassLoader;
    
    // 插件间共享的类缓存
    private static final Set<String> SHARED_CLASSES = ConcurrentHashMap.newKeySet();
    
    static {
        // 默认共享的包
        SHARED_CLASSES.add("java.");
        SHARED_CLASSES.add("javax.");
        SHARED_CLASSES.add("org.slf4j.");
        SHARED_CLASSES.add("org.apache.commons.");
        SHARED_CLASSES.add("com.fasterxml.jackson.");
    }

    public IsolatedPluginClassLoader(String pluginId, URL[] urls, ClassLoader parent) {
        super(urls, parent);
        this.pluginId = pluginId;
        this.parentClassLoader = parent;
        
        // 设置默认的共享包
        sharedPackages.addAll(SHARED_CLASSES);
        
        // 默认阻止访问其他插件的包
        blockedPackages.add("com.talkweb.ai.converter.plugins.");
        
        log.debug("Created isolated class loader for plugin: {}", pluginId);
    }

    @Override
    public Class<?> loadClass(String name) throws ClassNotFoundException {
        return loadClass(name, false);
    }

    @Override
    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
        synchronized (getClassLoadingLock(name)) {
            // 首先检查类是否已经被加载
            Class<?> c = findLoadedClass(name);
            if (c == null) {
                // 检查是否允许加载这个类
                if (!isClassLoadingAllowed(name)) {
                    log.warn("Class loading blocked for plugin {}: {}", pluginId, name);
                    throw new ClassNotFoundException("Class loading blocked: " + name + " for plugin: " + pluginId);
                }
                
                // 如果是共享类，从父类加载器加载
                if (isSharedClass(name)) {
                    try {
                        c = parentClassLoader.loadClass(name);
                    } catch (ClassNotFoundException e) {
                        // 如果父类加载器找不到，尝试从当前类加载器加载
                        c = findClass(name);
                    }
                } else {
                    // 插件特有的类，从当前类加载器加载
                    try {
                        c = findClass(name);
                    } catch (ClassNotFoundException e) {
                        // 如果当前类加载器找不到，尝试从父类加载器加载
                        c = parentClassLoader.loadClass(name);
                    }
                }
            }
            
            if (resolve) {
                resolveClass(c);
            }
            
            return c;
        }
    }

    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        log.debug("Finding class {} in plugin: {}", name, pluginId);
        return super.findClass(name);
    }

    @Override
    public URL getResource(String name) {
        // 首先尝试从当前类加载器获取资源
        URL url = findResource(name);
        if (url == null) {
            // 如果找不到，从父类加载器获取
            url = parentClassLoader.getResource(name);
        }
        return url;
    }

    @Override
    public Enumeration<URL> getResources(String name) throws IOException {
        // 合并当前类加载器和父类加载器的资源
        Enumeration<URL> localResources = findResources(name);
        Enumeration<URL> parentResources = parentClassLoader.getResources(name);
        
        return new CompoundEnumeration<>(localResources, parentResources);
    }

    @Override
    protected PermissionCollection getPermissions(CodeSource codesource) {
        // 为插件代码提供受限的权限
        Permissions permissions = new Permissions();
        
        // 这里可以根据插件的安全策略添加特定的权限
        // 默认情况下，插件只有最基本的权限
        
        return permissions;
    }

    /**
     * 检查是否允许加载指定的类
     */
    private boolean isClassLoadingAllowed(String className) {
        // 检查是否在阻止列表中
        for (String blockedPackage : blockedPackages) {
            if (className.startsWith(blockedPackage)) {
                // 检查是否有例外（在允许列表中）
                boolean allowed = allowedPackages.stream()
                    .anyMatch(className::startsWith);
                if (!allowed) {
                    return false;
                }
            }
        }
        
        return true;
    }

    /**
     * 检查是否为共享类
     */
    private boolean isSharedClass(String className) {
        return sharedPackages.stream().anyMatch(className::startsWith);
    }

    /**
     * 添加允许的包
     */
    public void addAllowedPackage(String packageName) {
        allowedPackages.add(packageName);
        log.debug("Added allowed package for plugin {}: {}", pluginId, packageName);
    }

    /**
     * 添加阻止的包
     */
    public void addBlockedPackage(String packageName) {
        blockedPackages.add(packageName);
        log.debug("Added blocked package for plugin {}: {}", pluginId, packageName);
    }

    /**
     * 添加共享的包
     */
    public void addSharedPackage(String packageName) {
        sharedPackages.add(packageName);
        log.debug("Added shared package for plugin {}: {}", pluginId, packageName);
    }

    /**
     * 获取插件ID
     */
    public String getPluginId() {
        return pluginId;
    }

    @Override
    public String toString() {
        return "IsolatedPluginClassLoader{plugin-" + pluginId + "}";
    }

    /**
     * 复合枚举器，用于合并多个枚举
     */
    private static class CompoundEnumeration<T> implements Enumeration<T> {
        private final Enumeration<T>[] enumerations;
        private int currentIndex = 0;

        @SafeVarargs
        public CompoundEnumeration(Enumeration<T>... enumerations) {
            this.enumerations = enumerations;
        }

        @Override
        public boolean hasMoreElements() {
            while (currentIndex < enumerations.length) {
                if (enumerations[currentIndex].hasMoreElements()) {
                    return true;
                }
                currentIndex++;
            }
            return false;
        }

        @Override
        public T nextElement() {
            if (!hasMoreElements()) {
                throw new java.util.NoSuchElementException();
            }
            return enumerations[currentIndex].nextElement();
        }
    }

    /**
     * 清理资源
     */
    @Override
    public void close() throws IOException {
        log.debug("Closing isolated class loader for plugin: {}", pluginId);
        super.close();
    }
}
