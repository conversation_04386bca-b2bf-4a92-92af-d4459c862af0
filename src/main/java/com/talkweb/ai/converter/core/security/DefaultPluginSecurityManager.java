package com.talkweb.ai.converter.core.security;

import com.talkweb.ai.converter.core.Plugin;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Path;
import java.security.Permission;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.jar.JarFile;
import java.util.jar.Manifest;

/**
 * 默认插件安全管理器实现
 */
public class DefaultPluginSecurityManager implements PluginSecurityManager {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultPluginSecurityManager.class);
    
    private final Map<String, Set<Permission>> pluginPermissions = new ConcurrentHashMap<>();
    private final Map<String, PluginSandbox> pluginSandboxes = new ConcurrentHashMap<>();
    private final List<SecurityPolicy> securityPolicies = new CopyOnWriteArrayList<>();
    private final List<SecurityEvent> auditLog = new CopyOnWriteArrayList<>();
    private final Set<X509Certificate> trustedCertificates = new HashSet<>();
    
    private boolean signatureVerificationEnabled = true;
    private boolean sandboxEnabled = true;

    public DefaultPluginSecurityManager() {
        // 初始化默认安全策略
        initializeDefaultPolicies();
    }

    @Override
    public SignatureVerificationResult verifySignature(Path pluginPath) {
        if (!signatureVerificationEnabled) {
            log.debug("Signature verification is disabled");
            return new SignatureVerificationResult(true, "Verification disabled");
        }
        
        try (JarFile jarFile = new JarFile(pluginPath.toFile(), true)) {
            Manifest manifest = jarFile.getManifest();
            if (manifest == null) {
                return new SignatureVerificationResult(false, null, 
                    "No manifest found", SignatureVerificationResult.VerificationError.NO_SIGNATURE);
            }
            
            // 检查JAR文件的签名
            Certificate[] certificates = getJarCertificates(jarFile);
            if (certificates == null || certificates.length == 0) {
                return new SignatureVerificationResult(false, null,
                    "No certificates found", SignatureVerificationResult.VerificationError.NO_SIGNATURE);
            }
            
            // 验证证书链
            for (Certificate cert : certificates) {
                if (cert instanceof X509Certificate) {
                    X509Certificate x509Cert = (X509Certificate) cert;
                    
                    // 检查证书是否过期
                    try {
                        x509Cert.checkValidity();
                    } catch (Exception e) {
                        return new SignatureVerificationResult(false, null,
                            "Certificate expired: " + e.getMessage(), 
                            SignatureVerificationResult.VerificationError.EXPIRED_CERTIFICATE);
                    }
                    
                    // 检查证书是否受信任
                    if (!isCertificateTrusted(x509Cert)) {
                        return new SignatureVerificationResult(false, x509Cert.getSubjectDN().toString(),
                            "Untrusted certificate", 
                            SignatureVerificationResult.VerificationError.UNTRUSTED_SIGNER);
                    }
                }
            }
            
            String signerInfo = certificates[0] instanceof X509Certificate ? 
                ((X509Certificate) certificates[0]).getSubjectDN().toString() : "Unknown";
            
            return new SignatureVerificationResult(true, signerInfo);
            
        } catch (IOException e) {
            log.error("Failed to verify signature for plugin: {}", pluginPath, e);
            return new SignatureVerificationResult(false, null,
                "IO error: " + e.getMessage(), SignatureVerificationResult.VerificationError.UNKNOWN_ERROR);
        }
    }

    @Override
    public boolean checkPermission(Plugin plugin, Permission permission) {
        String pluginId = plugin.getMetadata().getId();
        
        // 检查插件是否有明确的权限
        Set<Permission> permissions = pluginPermissions.get(pluginId);
        if (permissions != null && permissions.contains(permission)) {
            return true;
        }
        
        // 应用安全策略
        for (SecurityPolicy policy : securityPolicies) {
            if (!policy.isPermissionAllowed(permission)) {
                auditSecurityEvent(SecurityEvent.permissionDenied(pluginId, 
                    permission.getName(), "Denied by policy: " + policy.getPolicyId()));
                return false;
            }
        }
        
        return true;
    }

    @Override
    public void grantPermissions(String pluginId, Set<Permission> permissions) throws PluginSecurityException {
        if (pluginId == null || permissions == null) {
            throw new IllegalArgumentException("Plugin ID and permissions cannot be null");
        }
        
        Set<Permission> currentPermissions = pluginPermissions.computeIfAbsent(pluginId, k -> new HashSet<>());
        currentPermissions.addAll(permissions);
        
        log.info("Granted {} permissions to plugin: {}", permissions.size(), pluginId);
        
        for (Permission permission : permissions) {
            auditSecurityEvent(SecurityEvent.builder()
                .eventId(generateEventId())
                .pluginId(pluginId)
                .eventType(SecurityEvent.EventType.PERMISSION_GRANTED)
                .level(SecurityEvent.EventLevel.INFO)
                .message("Permission granted: " + permission.getName())
                .source("DefaultPluginSecurityManager")
                .build());
        }
    }

    @Override
    public void revokePermissions(String pluginId, Set<Permission> permissions) {
        Set<Permission> currentPermissions = pluginPermissions.get(pluginId);
        if (currentPermissions != null) {
            currentPermissions.removeAll(permissions);
            log.info("Revoked {} permissions from plugin: {}", permissions.size(), pluginId);
        }
    }

    @Override
    public Set<Permission> getPluginPermissions(String pluginId) {
        Set<Permission> permissions = pluginPermissions.get(pluginId);
        return permissions != null ? new HashSet<>(permissions) : new HashSet<>();
    }

    @Override
    public PluginSandbox createSandbox(String pluginId) throws PluginSecurityException {
        if (!sandboxEnabled) {
            log.debug("Sandbox is disabled for plugin: {}", pluginId);
            return null;
        }
        
        if (pluginSandboxes.containsKey(pluginId)) {
            throw PluginSecurityException.sandboxCreationFailed(pluginId, 
                new IllegalStateException("Sandbox already exists"));
        }
        
        try {
            PluginSandbox sandbox = new DefaultPluginSandbox(pluginId);
            pluginSandboxes.put(pluginId, sandbox);
            
            auditSecurityEvent(SecurityEvent.builder()
                .eventId(generateEventId())
                .pluginId(pluginId)
                .eventType(SecurityEvent.EventType.SANDBOX_CREATED)
                .level(SecurityEvent.EventLevel.INFO)
                .message("Sandbox created successfully")
                .source("DefaultPluginSecurityManager")
                .build());
            
            log.info("Created sandbox for plugin: {}", pluginId);
            return sandbox;
            
        } catch (Exception e) {
            throw PluginSecurityException.sandboxCreationFailed(pluginId, e);
        }
    }

    @Override
    public void destroySandbox(String pluginId) {
        PluginSandbox sandbox = pluginSandboxes.remove(pluginId);
        if (sandbox != null) {
            sandbox.destroy();
            
            auditSecurityEvent(SecurityEvent.builder()
                .eventId(generateEventId())
                .pluginId(pluginId)
                .eventType(SecurityEvent.EventType.SANDBOX_DESTROYED)
                .level(SecurityEvent.EventLevel.INFO)
                .message("Sandbox destroyed")
                .source("DefaultPluginSecurityManager")
                .build());
            
            log.info("Destroyed sandbox for plugin: {}", pluginId);
        }
    }

    @Override
    public boolean isInSandbox(String pluginId) {
        return pluginSandboxes.containsKey(pluginId);
    }

    @Override
    public void addSecurityPolicy(SecurityPolicy policy) {
        if (policy != null) {
            securityPolicies.add(policy);
            log.info("Added security policy: {}", policy.getPolicyId());
        }
    }

    @Override
    public void removeSecurityPolicy(String policyId) {
        securityPolicies.removeIf(policy -> policy.getPolicyId().equals(policyId));
        log.info("Removed security policy: {}", policyId);
    }

    @Override
    public List<SecurityPolicy> getSecurityPolicies() {
        return new ArrayList<>(securityPolicies);
    }

    @Override
    public void auditSecurityEvent(SecurityEvent event) {
        auditLog.add(event);
        
        // 根据事件级别记录日志
        switch (event.getLevel()) {
            case INFO:
                log.info("Security event: {} - {}", event.getEventType(), event.getMessage());
                break;
            case WARNING:
                log.warn("Security event: {} - {}", event.getEventType(), event.getMessage());
                break;
            case ERROR:
                log.error("Security event: {} - {}", event.getEventType(), event.getMessage());
                break;
            case CRITICAL:
                log.error("CRITICAL Security event: {} - {}", event.getEventType(), event.getMessage());
                break;
        }
    }

    @Override
    public List<SecurityEvent> getAuditLog(String pluginId) {
        if (pluginId == null) {
            return new ArrayList<>(auditLog);
        }
        
        return auditLog.stream()
            .filter(event -> pluginId.equals(event.getPluginId()))
            .toList();
    }

    /**
     * 获取JAR文件的证书
     */
    private Certificate[] getJarCertificates(JarFile jarFile) {
        // 简化实现：实际应该遍历所有条目并验证签名
        // 这里只是示例实现
        return null;
    }

    /**
     * 检查证书是否受信任
     */
    private boolean isCertificateTrusted(X509Certificate certificate) {
        return trustedCertificates.contains(certificate) || 
               trustedCertificates.stream().anyMatch(trusted -> 
                   trusted.getSubjectDN().equals(certificate.getSubjectDN()));
    }

    /**
     * 初始化默认安全策略
     */
    private void initializeDefaultPolicies() {
        // 添加默认的限制性策略
        SecurityPolicy defaultPolicy = SecurityPolicy.builder()
            .policyId("default-restrictive")
            .name("Default Restrictive Policy")
            .description("Default security policy with restrictive permissions")
            .type(SecurityPolicy.PolicyType.RESTRICTIVE)
            .enabled(true)
            .build();
        
        addSecurityPolicy(defaultPolicy);
    }

    /**
     * 添加受信任的证书
     */
    public void addTrustedCertificate(X509Certificate certificate) {
        trustedCertificates.add(certificate);
        log.info("Added trusted certificate: {}", certificate.getSubjectDN());
    }

    /**
     * 设置签名验证是否启用
     */
    public void setSignatureVerificationEnabled(boolean enabled) {
        this.signatureVerificationEnabled = enabled;
        log.info("Signature verification {}", enabled ? "enabled" : "disabled");
    }

    /**
     * 设置沙箱是否启用
     */
    public void setSandboxEnabled(boolean enabled) {
        this.sandboxEnabled = enabled;
        log.info("Sandbox {}", enabled ? "enabled" : "disabled");
    }

    /**
     * 生成事件ID
     */
    private String generateEventId() {
        return "SEC_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
}
