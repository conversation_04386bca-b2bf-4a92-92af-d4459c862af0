package com.talkweb.ai.converter.core.adapter;

import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginContext;
import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginMetadata;
import com.talkweb.ai.converter.core.PluginState;
import com.talkweb.ai.converter.core.converter.DocumentConverter;

import java.util.logging.Logger;

/**
 * 转换器插件适配器，将DocumentConverter适配为Plugin接口
 * 
 * 这个适配器将转换器逻辑与插件管理逻辑分离，实现了关注点分离原则。
 * 转换器只需要关注文档转换逻辑，而插件生命周期管理由适配器处理。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ConverterPluginAdapter implements Plugin {
    
    private static final Logger logger = Logger.getLogger(ConverterPluginAdapter.class.getName());
    
    private final DocumentConverter converter;
    private final PluginMetadata metadata;
    private PluginState state = PluginState.LOADED;
    private PluginContext context;
    
    /**
     * 创建转换器插件适配器
     * 
     * @param converter 文档转换器实例
     * @param metadata 插件元数据
     */
    public ConverterPluginAdapter(DocumentConverter converter, PluginMetadata metadata) {
        this.converter = converter;
        this.metadata = metadata;
        
        if (converter == null) {
            throw new IllegalArgumentException("Converter cannot be null");
        }
        if (metadata == null) {
            throw new IllegalArgumentException("Metadata cannot be null");
        }
        
        logger.info("Created converter plugin adapter for: " + metadata.getName());
    }
    
    /**
     * 获取被适配的转换器实例
     * 
     * @return 文档转换器实例
     */
    public DocumentConverter getConverter() {
        return converter;
    }
    
    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public PluginState getState() {
        return state;
    }
    
    @Override
    public void init(PluginContext context) throws PluginException {
        try {
            logger.info("Initializing converter plugin: " + metadata.getName());
            
            this.context = context;
            
            // 如果转换器实现了可初始化接口，则调用初始化方法
            if (converter instanceof Initializable) {
                ((Initializable) converter).initialize(context);
            }
            
            this.state = PluginState.READY;
            
            logger.info("Successfully initialized converter plugin: " + metadata.getName());
            
        } catch (Exception e) {
            this.state = PluginState.ERROR;
            String errorMsg = "Failed to initialize converter plugin: " + metadata.getName();
            logger.severe(errorMsg + " - " + e.getMessage());
            throw new PluginException(errorMsg, e);
        }
    }
    
    @Override
    public void start() throws PluginException {
        try {
            logger.info("Starting converter plugin: " + metadata.getName());
            
            if (state != PluginState.READY) {
                throw new PluginException("Plugin must be in READY state to start, current state: " + state);
            }
            
            // 如果转换器实现了可启动接口，则调用启动方法
            if (converter instanceof Startable) {
                ((Startable) converter).start();
            }
            
            this.state = PluginState.RUNNING;
            
            logger.info("Successfully started converter plugin: " + metadata.getName());
            
        } catch (Exception e) {
            this.state = PluginState.ERROR;
            String errorMsg = "Failed to start converter plugin: " + metadata.getName();
            logger.severe(errorMsg + " - " + e.getMessage());
            throw new PluginException(errorMsg, e);
        }
    }
    
    @Override
    public void stop() throws PluginException {
        try {
            logger.info("Stopping converter plugin: " + metadata.getName());
            
            // 如果转换器实现了可停止接口，则调用停止方法
            if (converter instanceof Stoppable) {
                ((Stoppable) converter).stop();
            }
            
            this.state = PluginState.STOPPED;
            
            logger.info("Successfully stopped converter plugin: " + metadata.getName());
            
        } catch (Exception e) {
            this.state = PluginState.ERROR;
            String errorMsg = "Failed to stop converter plugin: " + metadata.getName();
            logger.severe(errorMsg + " - " + e.getMessage());
            throw new PluginException(errorMsg, e);
        }
    }
    
    @Override
    public void destroy() throws PluginException {
        try {
            logger.info("Destroying converter plugin: " + metadata.getName());
            
            // 调用转换器的销毁方法
            converter.destroy();
            
            this.state = PluginState.DESTROYED;
            this.context = null;
            
            logger.info("Successfully destroyed converter plugin: " + metadata.getName());
            
        } catch (Exception e) {
            this.state = PluginState.ERROR;
            String errorMsg = "Failed to destroy converter plugin: " + metadata.getName();
            logger.severe(errorMsg + " - " + e.getMessage());
            throw new PluginException(errorMsg, e);
        }
    }
    
    /**
     * 可初始化接口，转换器可以选择实现此接口来接收初始化通知
     */
    public interface Initializable {
        void initialize(PluginContext context) throws Exception;
    }
    
    /**
     * 可启动接口，转换器可以选择实现此接口来接收启动通知
     */
    public interface Startable {
        void start() throws Exception;
    }
    
    /**
     * 可停止接口，转换器可以选择实现此接口来接收停止通知
     */
    public interface Stoppable {
        void stop() throws Exception;
    }
    
    @Override
    public String toString() {
        return "ConverterPluginAdapter{" +
                "converter=" + converter.getClass().getSimpleName() +
                ", metadata=" + metadata +
                ", state=" + state +
                '}';
    }
}
