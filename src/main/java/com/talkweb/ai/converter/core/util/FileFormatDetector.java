package com.talkweb.ai.converter.core.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 文件格式检测器，用于检测文件的格式和类型
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class FileFormatDetector {
    
    // 文件魔数映射
    private static final Map<String, byte[]> MAGIC_NUMBERS = new HashMap<>();
    
    static {
        // PDF
        MAGIC_NUMBERS.put("pdf", new byte[] { '%', 'P', 'D', 'F' });
        
        // Office Open XML (DOCX, XLSX, PPTX)
        MAGIC_NUMBERS.put("docx", new byte[] { 'P', 'K', 0x03, 0x04 });
        MAGIC_NUMBERS.put("xlsx", new byte[] { 'P', 'K', 0x03, 0x04 });
        MAGIC_NUMBERS.put("pptx", new byte[] { 'P', 'K', 0x03, 0x04 });
        
        // ODF (ODT, ODS, ODP)
        MAGIC_NUMBERS.put("odt", new byte[] { 'P', 'K', 0x03, 0x04 });
        MAGIC_NUMBERS.put("ods", new byte[] { 'P', 'K', 0x03, 0x04 });
        MAGIC_NUMBERS.put("odp", new byte[] { 'P', 'K', 0x03, 0x04 });
        
        // RTF
        MAGIC_NUMBERS.put("rtf", new byte[] { '{', '\\', 'r', 't', 'f' });
        
        // Old Office formats (DOC, XLS, PPT)
        MAGIC_NUMBERS.put("doc", new byte[] { (byte) 0xD0, (byte) 0xCF, (byte) 0x11, (byte) 0xE0 });
        MAGIC_NUMBERS.put("xls", new byte[] { (byte) 0xD0, (byte) 0xCF, (byte) 0x11, (byte) 0xE0 });
        MAGIC_NUMBERS.put("ppt", new byte[] { (byte) 0xD0, (byte) 0xCF, (byte) 0x11, (byte) 0xE0 });
    }
    
    /**
     * 检测文件格式
     * 
     * @param file 文件
     * @return 文件格式（扩展名），如果无法检测则返回null
     */
    public static String detect(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return null;
        }
        
        // 首先尝试从文件扩展名判断
        String extension = getFileExtension(file);
        if (extension != null && !extension.isEmpty()) {
            return extension.toLowerCase();
        }
        
        // 如果没有扩展名或扩展名不明确，尝试通过魔数判断
        try (InputStream is = new FileInputStream(file)) {
            byte[] buffer = new byte[8]; // 读取前8个字节用于检测
            int bytesRead = is.read(buffer);
            
            if (bytesRead > 0) {
                for (Map.Entry<String, byte[]> entry : MAGIC_NUMBERS.entrySet()) {
                    byte[] magic = entry.getValue();
                    if (bytesRead >= magic.length && startsWith(buffer, magic)) {
                        return entry.getKey();
                    }
                }
            }
        } catch (IOException e) {
            // 忽略IO异常
        }
        
        // 无法确定格式
        return null;
    }
    
    /**
     * 检查文件是否支持指定的扩展名集合
     * 
     * @param file 文件
     * @param extensions 扩展名集合
     * @return 如果支持返回true，否则返回false
     */
    public static boolean isSupported(File file, Set<String> extensions) {
        if (file == null || !file.exists() || !file.isFile() || extensions == null || extensions.isEmpty()) {
            return false;
        }
        
        // 获取文件扩展名
        String extension = getFileExtension(file);
        if (extension == null || extension.isEmpty()) {
            return false;
        }
        
        // 检查是否在支持的扩展名集合中
        return extensions.contains(extension.toLowerCase());
    }
    
    /**
     * 获取文件的MIME类型
     * 
     * @param file 文件
     * @return MIME类型，如果无法检测则返回null
     */
    public static String getMimeType(File file) {
        String extension = detect(file);
        if (extension == null) {
            return null;
        }
        
        switch (extension) {
            case "pdf":
                return "application/pdf";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "doc":
                return "application/msword";
            case "xls":
                return "application/vnd.ms-excel";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "odt":
                return "application/vnd.oasis.opendocument.text";
            case "ods":
                return "application/vnd.oasis.opendocument.spreadsheet";
            case "odp":
                return "application/vnd.oasis.opendocument.presentation";
            case "rtf":
                return "application/rtf";
            case "html":
            case "htm":
                return "text/html";
            case "txt":
                return "text/plain";
            case "xml":
                return "application/xml";
            case "json":
                return "application/json";
            case "md":
                return "text/markdown";
            default:
                return "application/octet-stream";
        }
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param file 文件
     * @return 文件扩展名（不包含点号），如果没有扩展名则返回null
     */
    public static String getFileExtension(File file) {
        if (file == null) {
            return null;
        }
        
        String name = file.getName();
        int lastDotIndex = name.lastIndexOf('.');
        
        if (lastDotIndex > 0 && lastDotIndex < name.length() - 1) {
            return name.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return null;
    }
    
    /**
     * 检查数组是否以指定的前缀开始
     * 
     * @param array 数组
     * @param prefix 前缀
     * @return 如果数组以指定的前缀开始返回true，否则返回false
     */
    private static boolean startsWith(byte[] array, byte[] prefix) {
        if (array.length < prefix.length) {
            return false;
        }
        
        for (int i = 0; i < prefix.length; i++) {
            if (array[i] != prefix[i]) {
                return false;
            }
        }
        
        return true;
    }
}