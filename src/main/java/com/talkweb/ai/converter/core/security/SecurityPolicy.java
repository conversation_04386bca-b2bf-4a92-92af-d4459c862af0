package com.talkweb.ai.converter.core.security;

import java.security.Permission;
import java.util.List;
import java.util.Set;

/**
 * 安全策略
 * 定义插件的安全规则和权限
 */
public class SecurityPolicy {
    
    private final String policyId;
    private final String name;
    private final String description;
    private final PolicyType type;
    private final Set<Permission> allowedPermissions;
    private final Set<Permission> deniedPermissions;
    private final List<SecurityRule> rules;
    private final boolean enabled;

    public SecurityPolicy(String policyId, String name, String description, PolicyType type,
                         Set<Permission> allowedPermissions, Set<Permission> deniedPermissions,
                         List<SecurityRule> rules, boolean enabled) {
        this.policyId = policyId;
        this.name = name;
        this.description = description;
        this.type = type;
        this.allowedPermissions = allowedPermissions;
        this.deniedPermissions = deniedPermissions;
        this.rules = rules;
        this.enabled = enabled;
    }

    public String getPolicyId() {
        return policyId;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public PolicyType getType() {
        return type;
    }

    public Set<Permission> getAllowedPermissions() {
        return allowedPermissions;
    }

    public Set<Permission> getDeniedPermissions() {
        return deniedPermissions;
    }

    public List<SecurityRule> getRules() {
        return rules;
    }

    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 检查权限是否被允许
     * 
     * @param permission 权限
     * @return 如果允许返回true
     */
    public boolean isPermissionAllowed(Permission permission) {
        if (!enabled) {
            return true; // 策略未启用时默认允许
        }
        
        // 检查明确拒绝的权限
        if (deniedPermissions.contains(permission)) {
            return false;
        }
        
        // 检查明确允许的权限
        if (allowedPermissions.contains(permission)) {
            return true;
        }
        
        // 应用规则
        for (SecurityRule rule : rules) {
            if (rule.matches(permission)) {
                return rule.isAllow();
            }
        }
        
        // 默认策略
        return type == PolicyType.PERMISSIVE;
    }

    /**
     * 策略类型
     */
    public enum PolicyType {
        /**
         * 宽松策略：默认允许，明确拒绝
         */
        PERMISSIVE,
        
        /**
         * 严格策略：默认拒绝，明确允许
         */
        RESTRICTIVE,
        
        /**
         * 自定义策略：完全基于规则
         */
        CUSTOM
    }

    /**
     * 安全规则
     */
    public static class SecurityRule {
        private final String ruleId;
        private final String pattern;
        private final RuleType type;
        private final boolean allow;
        private final int priority;

        public SecurityRule(String ruleId, String pattern, RuleType type, boolean allow, int priority) {
            this.ruleId = ruleId;
            this.pattern = pattern;
            this.type = type;
            this.allow = allow;
            this.priority = priority;
        }

        public String getRuleId() {
            return ruleId;
        }

        public String getPattern() {
            return pattern;
        }

        public RuleType getType() {
            return type;
        }

        public boolean isAllow() {
            return allow;
        }

        public int getPriority() {
            return priority;
        }

        /**
         * 检查权限是否匹配此规则
         * 
         * @param permission 权限
         * @return 如果匹配返回true
         */
        public boolean matches(Permission permission) {
            switch (type) {
                case CLASS_NAME:
                    return permission.getClass().getName().matches(pattern);
                case PERMISSION_NAME:
                    return permission.getName().matches(pattern);
                case ACTION:
                    return permission.getActions() != null && permission.getActions().matches(pattern);
                default:
                    return false;
            }
        }

        /**
         * 规则类型
         */
        public enum RuleType {
            CLASS_NAME,
            PERMISSION_NAME,
            ACTION
        }
    }

    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 策略构建器
     */
    public static class Builder {
        private String policyId;
        private String name;
        private String description;
        private PolicyType type = PolicyType.RESTRICTIVE;
        private Set<Permission> allowedPermissions = Set.of();
        private Set<Permission> deniedPermissions = Set.of();
        private List<SecurityRule> rules = List.of();
        private boolean enabled = true;

        public Builder policyId(String policyId) {
            this.policyId = policyId;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder type(PolicyType type) {
            this.type = type;
            return this;
        }

        public Builder allowedPermissions(Set<Permission> allowedPermissions) {
            this.allowedPermissions = allowedPermissions;
            return this;
        }

        public Builder deniedPermissions(Set<Permission> deniedPermissions) {
            this.deniedPermissions = deniedPermissions;
            return this;
        }

        public Builder rules(List<SecurityRule> rules) {
            this.rules = rules;
            return this;
        }

        public Builder enabled(boolean enabled) {
            this.enabled = enabled;
            return this;
        }

        public SecurityPolicy build() {
            return new SecurityPolicy(policyId, name, description, type, 
                                    allowedPermissions, deniedPermissions, rules, enabled);
        }
    }
}
