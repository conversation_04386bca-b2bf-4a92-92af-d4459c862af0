package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.core.ConversionException;

/**
 * Specialized exception for PDF conversion errors with detailed error categorization
 */
public class PdfConversionException extends ConversionException {
    
    /**
     * Categories of PDF conversion errors
     */
    public enum ErrorType {
        FILE_NOT_FOUND("PDF file not found or inaccessible"),
        CORRUPTED_FILE("PDF file is corrupted or malformed"),
        ENCRYPTED_FILE("PDF file is encrypted and requires password"),
        UNSUPPORTED_VERSION("PDF version is not supported"),
        MEMORY_ERROR("Insufficient memory to process PDF"),
        PARSING_ERROR("Error parsing PDF content"),
        IO_ERROR("Input/output error during processing"),
        TIMEOUT_ERROR("Processing timeout exceeded"),
        UNKNOWN_ERROR("Unknown error occurred");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private final ErrorType errorType;
    private final String fileName;
    private final int pageNumber;
    
    /**
     * Create a PDF conversion exception with error type
     */
    public PdfConversionException(ErrorType errorType, String message) {
        super(message);
        this.errorType = errorType;
        this.fileName = null;
        this.pageNumber = -1;
    }
    
    /**
     * Create a PDF conversion exception with error type and cause
     */
    public PdfConversionException(ErrorType errorType, String message, Throwable cause) {
        super(message, cause);
        this.errorType = errorType;
        this.fileName = null;
        this.pageNumber = -1;
    }
    
    /**
     * Create a PDF conversion exception with file context
     */
    public PdfConversionException(ErrorType errorType, String message, String fileName) {
        super(message);
        this.errorType = errorType;
        this.fileName = fileName;
        this.pageNumber = -1;
    }
    
    /**
     * Create a PDF conversion exception with file and page context
     */
    public PdfConversionException(ErrorType errorType, String message, String fileName, int pageNumber) {
        super(message);
        this.errorType = errorType;
        this.fileName = fileName;
        this.pageNumber = pageNumber;
    }
    
    /**
     * Create a PDF conversion exception with full context
     */
    public PdfConversionException(ErrorType errorType, String message, String fileName, int pageNumber, Throwable cause) {
        super(message, cause);
        this.errorType = errorType;
        this.fileName = fileName;
        this.pageNumber = pageNumber;
    }
    
    /**
     * Get the error type
     */
    public ErrorType getErrorType() {
        return errorType;
    }
    
    /**
     * Get the file name associated with the error
     */
    public String getFileName() {
        return fileName;
    }
    
    /**
     * Get the page number where the error occurred
     */
    public int getPageNumber() {
        return pageNumber;
    }
    
    /**
     * Check if this is a recoverable error
     */
    public boolean isRecoverable() {
        return errorType == ErrorType.PARSING_ERROR || 
               errorType == ErrorType.TIMEOUT_ERROR ||
               errorType == ErrorType.MEMORY_ERROR;
    }
    
    /**
     * Get a detailed error message with context
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(errorType.getDescription()).append(": ").append(getMessage());
        
        if (fileName != null) {
            sb.append(" (File: ").append(fileName).append(")");
        }
        
        if (pageNumber > 0) {
            sb.append(" (Page: ").append(pageNumber).append(")");
        }
        
        return sb.toString();
    }
    
    /**
     * Factory methods for common error scenarios
     */
    public static PdfConversionException fileNotFound(String fileName) {
        return new PdfConversionException(ErrorType.FILE_NOT_FOUND, 
            "PDF file not found: " + fileName, fileName);
    }
    
    public static PdfConversionException corruptedFile(String fileName, Throwable cause) {
        return new PdfConversionException(ErrorType.CORRUPTED_FILE, 
            "PDF file is corrupted or malformed: " + fileName, fileName, -1, cause);
    }
    
    public static PdfConversionException encryptedFile(String fileName) {
        return new PdfConversionException(ErrorType.ENCRYPTED_FILE, 
            "PDF file is encrypted and requires password: " + fileName, fileName);
    }
    
    public static PdfConversionException unsupportedVersion(String fileName, String version) {
        return new PdfConversionException(ErrorType.UNSUPPORTED_VERSION, 
            "PDF version " + version + " is not supported: " + fileName, fileName);
    }
    
    public static PdfConversionException memoryError(String fileName, int pageNumber) {
        return new PdfConversionException(ErrorType.MEMORY_ERROR, 
            "Insufficient memory to process PDF", fileName, pageNumber);
    }
    
    public static PdfConversionException parsingError(String fileName, int pageNumber, Throwable cause) {
        return new PdfConversionException(ErrorType.PARSING_ERROR, 
            "Error parsing PDF content", fileName, pageNumber, cause);
    }
    
    public static PdfConversionException ioError(String fileName, Throwable cause) {
        return new PdfConversionException(ErrorType.IO_ERROR, 
            "Input/output error during processing: " + fileName, fileName, -1, cause);
    }
    
    public static PdfConversionException timeoutError(String fileName, int pageNumber) {
        return new PdfConversionException(ErrorType.TIMEOUT_ERROR, 
            "Processing timeout exceeded", fileName, pageNumber);
    }
}
