package com.talkweb.ai.converter.core.dependency;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginState;

import java.util.List;

/**
 * 插件依赖异常
 */
public class PluginDependencyException extends PluginException {
    
    private final String pluginId;
    private final DependencyErrorType errorType;
    private final List<PluginDependency> problematicDependencies;

    public PluginDependencyException(String message, String pluginId) {
        this(message, pluginId, DependencyErrorType.UNKNOWN, null, null);
    }

    public PluginDependencyException(String message, String pluginId, DependencyErrorType errorType) {
        this(message, pluginId, errorType, null, null);
    }

    public PluginDependencyException(String message, String pluginId, DependencyErrorType errorType, 
                                   List<PluginDependency> problematicDependencies, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginId = pluginId;
        this.errorType = errorType;
        this.problematicDependencies = problematicDependencies;
    }

    public String getPluginId() {
        return pluginId;
    }

    public DependencyErrorType getErrorType() {
        return errorType;
    }

    public List<PluginDependency> getProblematicDependencies() {
        return problematicDependencies;
    }

    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(errorType.getDescription()).append(": ").append(getMessage());
        
        if (pluginId != null) {
            sb.append(" (Plugin: ").append(pluginId).append(")");
        }
        
        if (problematicDependencies != null && !problematicDependencies.isEmpty()) {
            sb.append(" (Dependencies: ");
            for (int i = 0; i < problematicDependencies.size(); i++) {
                if (i > 0) sb.append(", ");
                PluginDependency dep = problematicDependencies.get(i);
                sb.append(dep.getPluginId()).append(":").append(dep.getVersion());
            }
            sb.append(")");
        }
        
        return sb.toString();
    }

    /**
     * 依赖错误类型
     */
    public enum DependencyErrorType {
        MISSING_DEPENDENCY("Missing required dependency"),
        VERSION_INCOMPATIBLE("Version incompatible"),
        CIRCULAR_DEPENDENCY("Circular dependency detected"),
        DEPENDENCY_RESOLUTION_FAILED("Dependency resolution failed"),
        INVALID_DEPENDENCY_FORMAT("Invalid dependency format"),
        UNKNOWN("Unknown dependency error");

        private final String description;

        DependencyErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 工厂方法
    public static PluginDependencyException missingDependency(String pluginId, PluginDependency dependency) {
        return new PluginDependencyException(
            "Missing required dependency: " + dependency.getPluginId() + ":" + dependency.getVersion(),
            pluginId,
            DependencyErrorType.MISSING_DEPENDENCY,
            List.of(dependency),
            null
        );
    }

    public static PluginDependencyException versionIncompatible(String pluginId, PluginDependency dependency, 
                                                              String availableVersion) {
        return new PluginDependencyException(
            String.format("Version incompatible: required %s:%s, available %s", 
                         dependency.getPluginId(), dependency.getVersion(), availableVersion),
            pluginId,
            DependencyErrorType.VERSION_INCOMPATIBLE,
            List.of(dependency),
            null
        );
    }

    public static PluginDependencyException circularDependency(String pluginId, List<String> dependencyChain) {
        return new PluginDependencyException(
            "Circular dependency detected: " + String.join(" -> ", dependencyChain),
            pluginId,
            DependencyErrorType.CIRCULAR_DEPENDENCY,
            null,
            null
        );
    }

    public static PluginDependencyException resolutionFailed(String pluginId, String reason, Throwable cause) {
        return new PluginDependencyException(
            "Dependency resolution failed: " + reason,
            pluginId,
            DependencyErrorType.DEPENDENCY_RESOLUTION_FAILED,
            null,
            cause
        );
    }

    public static PluginDependencyException invalidFormat(String pluginId, String dependencyString, Throwable cause) {
        return new PluginDependencyException(
            "Invalid dependency format: " + dependencyString,
            pluginId,
            DependencyErrorType.INVALID_DEPENDENCY_FORMAT,
            null,
            cause
        );
    }
}
