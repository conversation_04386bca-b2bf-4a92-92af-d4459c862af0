package com.talkweb.ai.converter.core.util;

/**
 * Interface for caches that provide statistics
 */
public interface StatisticsAwareCache {
    
    /**
     * Get the current size of the cache
     * 
     * @return current cache size
     */
    int size();
    
    /**
     * Get the number of cache hits
     * 
     * @return hit count
     */
    long getHitCount();
    
    /**
     * Get the number of cache misses
     * 
     * @return miss count
     */
    long getMissCount();
    
    /**
     * Get the cache hit rate
     * 
     * @return hit rate (0.0 to 1.0)
     */
    default double getHitRate() {
        long hits = getHitCount();
        long misses = getMissCount();
        long total = hits + misses;
        return total > 0 ? (double) hits / total : 0.0;
    }
}
