package com.talkweb.ai.converter.core.hotreload;

import com.talkweb.ai.converter.core.PluginManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 默认热重载管理器实现
 */
public class DefaultHotReloadManager implements HotReloadManager {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultHotReloadManager.class);
    
    private final PluginManager pluginManager;
    private final Set<Path> watchDirectories = ConcurrentHashMap.newKeySet();
    private final List<HotReloadListener> listeners = new CopyOnWriteArrayList<>();
    private final Map<Path, Long> lastModifiedTimes = new ConcurrentHashMap<>();
    private final Map<Path, ScheduledFuture<?>> pendingReloads = new ConcurrentHashMap<>();
    private final Set<String> supportedExtensions = ConcurrentHashMap.newKeySet();
    
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final AtomicLong totalReloads = new AtomicLong(0);
    private final AtomicLong successfulReloads = new AtomicLong(0);
    private final AtomicLong failedReloads = new AtomicLong(0);
    private final AtomicLong totalReloadTime = new AtomicLong(0);
    private final AtomicLong lastReloadTime = new AtomicLong(0);
    
    private WatchService watchService;
    private Thread watchThread;
    private ScheduledExecutorService debounceExecutor;
    private long debounceDelay = 500; // 默认500ms防抖延迟

    public DefaultHotReloadManager(PluginManager pluginManager) {
        this.pluginManager = pluginManager;
        
        // 默认支持的文件扩展名
        supportedExtensions.add(".jar");
        supportedExtensions.add(".zip");
        
        // 初始化防抖执行器
        this.debounceExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "HotReload-Debounce");
            t.setDaemon(true);
            return t;
        });
    }

    @Override
    public void start(List<Path> watchDirectories) throws HotReloadException {
        if (running.compareAndSet(false, true)) {
            try {
                this.watchService = FileSystems.getDefault().newWatchService();
                
                // 注册监控目录
                for (Path directory : watchDirectories) {
                    addWatchDirectory(directory);
                }
                
                // 启动监控线程
                startWatchThread();
                
                // 通知监听器
                notifyListeners(l -> l.onWatchStarted(new ArrayList<>(watchDirectories)));
                
                log.info("Hot reload manager started, watching {} directories", watchDirectories.size());
                
            } catch (IOException e) {
                running.set(false);
                throw HotReloadException.watchServiceError("Failed to start watch service", e);
            }
        } else {
            log.warn("Hot reload manager is already running");
        }
    }

    @Override
    public void stop() {
        if (running.compareAndSet(true, false)) {
            try {
                // 停止监控线程
                if (watchThread != null) {
                    watchThread.interrupt();
                    try {
                        // Wait for the watch thread to finish
                        watchThread.join(5000); // Wait up to 5 seconds
                        if (watchThread.isAlive()) {
                            log.warn("Watch thread did not terminate in time");
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("Interrupted while waiting for watch thread to stop");
                    }
                }

                // 关闭监控服务 (now safe to close)
                if (watchService != null) {
                    watchService.close();
                    watchService = null;
                }

                // 取消所有待处理的重载
                pendingReloads.values().forEach(future -> future.cancel(true));
                pendingReloads.clear();

                // 清理状态
                watchDirectories.clear();
                lastModifiedTimes.clear();

                // 通知监听器
                notifyListeners(HotReloadListener::onWatchStopped);

                log.info("Hot reload manager stopped");

            } catch (IOException e) {
                log.error("Error stopping hot reload manager", e);
            }
        }
    }

    @Override
    public boolean isRunning() {
        return running.get();
    }

    @Override
    public void addWatchDirectory(Path directory) throws HotReloadException {
        if (!Files.exists(directory) || !Files.isDirectory(directory)) {
            throw HotReloadException.fileAccessError(directory, "Directory does not exist or is not a directory", null);
        }
        
        try {
            // 注册目录监控
            directory.register(watchService, 
                StandardWatchEventKinds.ENTRY_CREATE,
                StandardWatchEventKinds.ENTRY_MODIFY,
                StandardWatchEventKinds.ENTRY_DELETE);
            
            watchDirectories.add(directory);
            
            // 扫描现有文件并记录修改时间
            scanExistingFiles(directory);
            
            // 通知监听器
            notifyListeners(l -> l.onWatchDirectoryAdded(directory));
            
            log.info("Added watch directory: {}", directory);
            
        } catch (IOException e) {
            throw HotReloadException.watchServiceError("Failed to register watch directory: " + directory, e);
        }
    }

    @Override
    public void removeWatchDirectory(Path directory) {
        if (watchDirectories.remove(directory)) {
            // 移除相关的文件记录
            lastModifiedTimes.entrySet().removeIf(entry -> entry.getKey().startsWith(directory));
            
            // 通知监听器
            notifyListeners(l -> l.onWatchDirectoryRemoved(directory));
            
            log.info("Removed watch directory: {}", directory);
        }
    }

    @Override
    public Set<Path> getWatchDirectories() {
        return new HashSet<>(watchDirectories);
    }

    @Override
    public void triggerReload(Path pluginPath) throws HotReloadException {
        if (!isPluginFile(pluginPath)) {
            throw HotReloadException.invalidPluginFile(pluginPath, "Not a supported plugin file");
        }
        
        performReload(pluginPath);
    }

    @Override
    public void addHotReloadListener(HotReloadListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
            log.debug("Added hot reload listener: {}", listener.getClass().getSimpleName());
        }
    }

    @Override
    public void removeHotReloadListener(HotReloadListener listener) {
        if (listeners.remove(listener)) {
            log.debug("Removed hot reload listener: {}", listener.getClass().getSimpleName());
        }
    }

    @Override
    public void setDebounceDelay(long delayMs) {
        this.debounceDelay = Math.max(0, delayMs);
        log.debug("Set debounce delay to {}ms", this.debounceDelay);
    }

    @Override
    public long getDebounceDelay() {
        return debounceDelay;
    }

    @Override
    public void setSupportedExtensions(Set<String> extensions) {
        supportedExtensions.clear();
        supportedExtensions.addAll(extensions);
        log.debug("Set supported extensions: {}", extensions);
    }

    @Override
    public Set<String> getSupportedExtensions() {
        return new HashSet<>(supportedExtensions);
    }

    @Override
    public HotReloadStatistics getStatistics() {
        long avgReloadTime = totalReloads.get() > 0 ? 
            totalReloadTime.get() / totalReloads.get() : 0;
        
        return new HotReloadStatistics(
            totalReloads.get(),
            successfulReloads.get(),
            failedReloads.get(),
            avgReloadTime,
            lastReloadTime.get(),
            watchDirectories.size(),
            lastModifiedTimes.size()
        );
    }

    @Override
    public void clearStatistics() {
        totalReloads.set(0);
        successfulReloads.set(0);
        failedReloads.set(0);
        totalReloadTime.set(0);
        lastReloadTime.set(0);
        log.debug("Cleared hot reload statistics");
    }

    /**
     * 启动监控线程
     */
    private void startWatchThread() {
        watchThread = new Thread(this::watchLoop, "HotReload-Watch");
        watchThread.setDaemon(true);
        watchThread.start();
    }

    /**
     * 监控循环
     */
    private void watchLoop() {
        log.debug("Hot reload watch thread started");

        while (running.get() && !Thread.currentThread().isInterrupted() && watchService != null) {
            try {
                WatchKey key = watchService.take();

                for (WatchEvent<?> event : key.pollEvents()) {
                    WatchEvent.Kind<?> kind = event.kind();

                    if (kind == StandardWatchEventKinds.OVERFLOW) {
                        log.warn("Watch service overflow detected");
                        continue;
                    }

                    @SuppressWarnings("unchecked")
                    WatchEvent<Path> pathEvent = (WatchEvent<Path>) event;
                    Path fileName = pathEvent.context();
                    Path directory = (Path) key.watchable();
                    Path fullPath = directory.resolve(fileName);

                    handleFileEvent(fullPath, kind);
                }

                key.reset();

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (java.nio.file.ClosedWatchServiceException e) {
                // This is expected during shutdown
                log.debug("Watch service closed during shutdown");
                break;
            } catch (Exception e) {
                if (running.get()) {
                    log.error("Error in watch loop", e);
                } else {
                    log.debug("Error in watch loop during shutdown: {}", e.getMessage());
                }
            }
        }

        log.debug("Hot reload watch thread stopped");
    }

    /**
     * 处理文件事件
     */
    private void handleFileEvent(Path filePath, WatchEvent.Kind<?> kind) {
        if (!isPluginFile(filePath)) {
            return;
        }

        HotReloadListener.FileChangeType changeType;
        if (kind == StandardWatchEventKinds.ENTRY_CREATE) {
            changeType = HotReloadListener.FileChangeType.CREATED;
        } else if (kind == StandardWatchEventKinds.ENTRY_MODIFY) {
            changeType = HotReloadListener.FileChangeType.MODIFIED;
        } else if (kind == StandardWatchEventKinds.ENTRY_DELETE) {
            changeType = HotReloadListener.FileChangeType.DELETED;
        } else {
            return;
        }

        log.debug("File event detected: {} - {}", changeType, filePath);

        // 通知监听器
        notifyListeners(l -> l.onFileChanged(filePath, changeType));

        // 对于创建和修改事件，触发重载
        if (changeType == HotReloadListener.FileChangeType.CREATED ||
            changeType == HotReloadListener.FileChangeType.MODIFIED) {

            scheduleReload(filePath);
        }
    }

    /**
     * 调度重载（带防抖）
     */
    private void scheduleReload(Path filePath) {
        // 取消之前的重载任务
        ScheduledFuture<?> previousTask = pendingReloads.get(filePath);
        if (previousTask != null) {
            previousTask.cancel(false);
        }

        // 调度新的重载任务
        ScheduledFuture<?> reloadTask = debounceExecutor.schedule(() -> {
            try {
                performReload(filePath);
            } catch (Exception e) {
                log.error("Error performing reload for: {}", filePath, e);
                notifyListeners(l -> l.onReloadFailure(filePath, e));
            } finally {
                pendingReloads.remove(filePath);
            }
        }, debounceDelay, TimeUnit.MILLISECONDS);

        pendingReloads.put(filePath, reloadTask);
    }

    /**
     * 执行重载
     */
    private void performReload(Path pluginPath) throws HotReloadException {
        long startTime = System.currentTimeMillis();

        try {
            // 检查文件是否真的发生了变化
            if (!hasFileChanged(pluginPath)) {
                log.debug("File has not changed, skipping reload: {}", pluginPath);
                return;
            }

            // 通知监听器重载开始
            boolean allowed = true;
            for (HotReloadListener listener : listeners) {
                if (!listener.beforeReload(pluginPath)) {
                    allowed = false;
                    break;
                }
            }

            if (!allowed) {
                log.debug("Reload cancelled by listener for: {}", pluginPath);
                return;
            }

            log.info("Performing hot reload for: {}", pluginPath);

            // 执行实际的重载操作
            try {
                pluginManager.installPlugin(pluginPath, true); // 强制重新安装
            } catch (Exception pluginException) {
                throw new HotReloadException("Plugin installation failed: " + pluginException.getMessage(),
                                           pluginPath, HotReloadException.ReloadErrorType.PLUGIN_LOAD_ERROR,
                                           pluginException);
            }

            long reloadTime = System.currentTimeMillis() - startTime;

            // 更新统计信息
            totalReloads.incrementAndGet();
            successfulReloads.incrementAndGet();
            totalReloadTime.addAndGet(reloadTime);
            lastReloadTime.set(System.currentTimeMillis());

            // 更新文件修改时间
            updateLastModifiedTime(pluginPath);

            // 通知监听器重载成功
            notifyListeners(l -> l.onReloadSuccess(pluginPath, reloadTime));

            log.info("Hot reload completed successfully for: {} ({}ms)", pluginPath, reloadTime);

        } catch (Exception e) {
            long reloadTime = System.currentTimeMillis() - startTime;

            // 更新统计信息
            totalReloads.incrementAndGet();
            failedReloads.incrementAndGet();
            totalReloadTime.addAndGet(reloadTime);

            // 通知监听器重载失败
            notifyListeners(l -> l.onReloadFailure(pluginPath, e));

            log.error("Hot reload failed for: {} ({}ms)", pluginPath, reloadTime, e);

            if (e instanceof HotReloadException) {
                throw e;
            } else {
                throw HotReloadException.pluginLoadError(pluginPath, e.getMessage(), e);
            }
        }
    }

    /**
     * 检查文件是否发生变化
     */
    private boolean hasFileChanged(Path filePath) {
        try {
            if (!Files.exists(filePath)) {
                return false;
            }

            long currentModified = Files.getLastModifiedTime(filePath).toMillis();
            Long lastModified = lastModifiedTimes.get(filePath);

            return lastModified == null || currentModified > lastModified;

        } catch (IOException e) {
            log.warn("Failed to check file modification time: {}", filePath, e);
            return true; // 假设文件已变化
        }
    }

    /**
     * 更新文件修改时间
     */
    private void updateLastModifiedTime(Path filePath) {
        try {
            long modifiedTime = Files.getLastModifiedTime(filePath).toMillis();
            lastModifiedTimes.put(filePath, modifiedTime);
        } catch (IOException e) {
            log.warn("Failed to update last modified time for: {}", filePath, e);
        }
    }

    /**
     * 扫描现有文件
     */
    private void scanExistingFiles(Path directory) {
        try {
            Files.walk(directory, 1)
                .filter(Files::isRegularFile)
                .filter(this::isPluginFile)
                .forEach(this::updateLastModifiedTime);
        } catch (IOException e) {
            log.warn("Failed to scan existing files in: {}", directory, e);
        }
    }

    /**
     * 检查是否为插件文件
     */
    private boolean isPluginFile(Path filePath) {
        String fileName = filePath.getFileName().toString().toLowerCase();
        return supportedExtensions.stream().anyMatch(fileName::endsWith);
    }

    /**
     * 通知监听器
     */
    private void notifyListeners(ListenerNotification notification) {
        for (HotReloadListener listener : listeners) {
            try {
                notification.notify(listener);
            } catch (Exception e) {
                log.warn("Error notifying hot reload listener: {}", listener.getClass().getSimpleName(), e);
            }
        }
    }

    @FunctionalInterface
    private interface ListenerNotification {
        void notify(HotReloadListener listener);
    }

    /**
     * 关闭资源
     */
    public void close() {
        stop();

        if (debounceExecutor != null && !debounceExecutor.isShutdown()) {
            debounceExecutor.shutdown();
            try {
                if (!debounceExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    debounceExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                debounceExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
