package com.talkweb.ai.converter.core.util;

/**
 * Cache statistics holder
 */
public class CacheStatistics {
    
    private final long size;
    private final long hitCount;
    private final long missCount;
    private final double hitRate;
    
    public CacheStatistics(long size, long hitCount, long missCount, double hitRate) {
        this.size = size;
        this.hitCount = hitCount;
        this.missCount = missCount;
        this.hitRate = hitRate;
    }
    
    public long getSize() {
        return size;
    }
    
    public long getHitCount() {
        return hitCount;
    }
    
    public long getMissCount() {
        return missCount;
    }
    
    public double getHitRate() {
        return hitRate;
    }
    
    public long getTotalRequests() {
        return hitCount + missCount;
    }
    
    @Override
    public String toString() {
        return String.format("CacheStatistics{size=%d, hits=%d, misses=%d, hitRate=%.2f%%}", 
                size, hitCount, missCount, hitRate * 100);
    }
}
