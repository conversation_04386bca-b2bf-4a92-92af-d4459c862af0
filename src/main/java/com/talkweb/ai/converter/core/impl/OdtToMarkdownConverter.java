package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.converter.AbstractDocumentConverter;
import com.talkweb.ai.converter.core.converter.ConversionCapabilities;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.ConversionMetadata;
import com.talkweb.ai.converter.util.odt.OdtConversionConfig;
import com.talkweb.ai.converter.util.odt.OdtConversionMode;

import java.io.File;
import java.nio.file.Path;
import java.util.Set;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * ODT to Markdown converter implementation using the new architecture
 * 
 * This converter wraps the existing utility converter and provides
 * the new interface while maintaining backward compatibility.
 * 
 * Features:
 * - Full compatibility with ODT (OpenDocument Text) format documents
 * - Comprehensive content extraction (text, formatting, tables, lists)
 * - Structure preservation with proper Markdown hierarchy
 * - Configurable conversion options
 * - High-performance processing with memory optimization
 * - Robust error handling and recovery mechanisms
 * - Support for various ODT versions and features
 * 
 * <AUTHOR> Assistant
 * @version 3.0 (Refactored to use new architecture)
 */
public class OdtToMarkdownConverter extends AbstractDocumentConverter {
    
    private static final Logger logger = Logger.getLogger(OdtToMarkdownConverter.class.getName());
    
    /**
     * Default constructor
     */
    public OdtToMarkdownConverter() {
        super();
    }
    
    @Override
    protected ConversionMetadata createMetadata() {
        return ConversionMetadata.builder("ODT to Markdown Converter")
                .description("Converts ODT files to Markdown format with comprehensive content extraction and structure preservation")
                .version("3.0")
                .attribute("author", "AI Assistant")
                .attribute("supportedInputFormats", Set.of("odt"))
                .attribute("supportedOutputFormats", Set.of("md"))
                .build();
    }
    
    @Override
    public Set<String> getSupportedExtensions() {
        return Set.of("odt");
    }
    
    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.HEADINGS)
                .feature(ConversionCapabilities.Features.TABLES)
                .feature(ConversionCapabilities.Features.LISTS)
                .feature(ConversionCapabilities.Features.IMAGES)
                .feature(ConversionCapabilities.Features.METADATA)
                .capability("strictMode", true)
                .capability("looseMode", true)
                .capability("advancedParsing", true)
                .capability("structurePreservation", true)
                .capability("maxFileSize", 100 * 1024 * 1024) // 100MB
                .build();
    }
    
    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) throws ConversionException {
        logger.info("Starting ODT conversion for: " + inputFile.getName());
        
        String parentDir = inputFile.getParent();
        if (parentDir == null) {
            parentDir = ".";
        }
        Path outputPath = Path.of(parentDir, inputFile.getName().replaceAll("\\.odt$", ".md"));
        
        try {
            // Create conversion configuration from context
            OdtConversionConfig config = createConfigFromContext(context);
            
            // Use the existing utility converter
            com.talkweb.ai.converter.util.OdtToMarkdownConverter utilConverter = 
                new com.talkweb.ai.converter.util.OdtToMarkdownConverter(null);
            
            // Configure the utility converter
            utilConverter.setConfig(config);
            
            // Perform conversion
            ConversionResult result = utilConverter.convert(inputFile);
            
            // Update the output path to match our convention
            return new ConversionResult(
                result.getStatus(),
                inputFile.getPath(),
                outputPath.toString(),
                result.getContent()
            );
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ODT conversion failed: " + inputFile.getName(), e);
            throw new ConversionException("ODT conversion failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Creates OdtConversionConfig from ConversionContext
     */
    private OdtConversionConfig createConfigFromContext(ConversionContext context) {
        OdtConversionConfig config = new OdtConversionConfig();
        
        // Apply conversion mode
        String modeStr = context.getOptions().getStringOption("conversionMode", "LOOSE");
        try {
            OdtConversionMode mode = OdtConversionMode.valueOf(modeStr.toUpperCase());
            config.setMode(mode);
        } catch (IllegalArgumentException e) {
            logger.warning("Invalid conversion mode: " + modeStr + ", using LOOSE mode");
            config.setMode(OdtConversionMode.LOOSE);
        }
        
        // Apply boolean options
        config.setUseAdvancedParsing(context.getOptions().getBooleanOption("useAdvancedParsing", true));
        config.setPreserveFormatting(context.getOptions().getBooleanOption("preserveFormatting", true));
        config.setConvertTables(context.getOptions().getBooleanOption("convertTables", true));
        config.setConvertImages(context.getOptions().getBooleanOption("convertImages", false));
        config.setIncludeMetadata(context.getOptions().getBooleanOption("includeMetadata", false));
        config.setNormalizeWhitespace(context.getOptions().getBooleanOption("normalizeWhitespace", true));
        
        // Apply string options
        config.setDefaultEncoding(context.getOptions().getStringOption("defaultEncoding", "UTF-8"));
        
        // Apply integer options
        config.setMaxDocumentSize(context.getOptions().getIntOption("maxDocumentSize", 50 * 1024 * 1024)); // 50MB default
        
        return config;
    }
    
    /**
     * Validates ODT file format
     */
    @Override
    protected void validateInput(File inputFile, ConversionContext context) throws ConversionException {
        super.validateInput(inputFile, context);
        
        String fileName = inputFile.getName().toLowerCase();
        if (!fileName.endsWith(".odt")) {
            throw new ConversionException("Unsupported file format. Only .odt files are supported.");
        }
        
        // Additional validation for file size
        long fileSize = inputFile.length();
        long maxSize = 100L * 1024 * 1024; // 100MB default
        
        if (fileSize > maxSize) {
            throw new ConversionException("File size (" + fileSize + " bytes) exceeds maximum allowed size (" + maxSize + " bytes)");
        }
        
        // Check if file is readable
        if (!inputFile.canRead()) {
            throw new ConversionException("Cannot read input file: " + inputFile.getPath());
        }
        
        // Basic ODT format validation (ODT is a ZIP file)
        try {
            java.util.zip.ZipFile zipFile = new java.util.zip.ZipFile(inputFile);
            
            // Check for required ODT entries
            if (zipFile.getEntry("content.xml") == null) {
                zipFile.close();
                throw new ConversionException("File does not appear to be a valid ODT document (missing content.xml)");
            }
            
            if (zipFile.getEntry("META-INF/manifest.xml") == null) {
                zipFile.close();
                throw new ConversionException("File does not appear to be a valid ODT document (missing manifest.xml)");
            }
            
            zipFile.close();
            
        } catch (java.io.IOException e) {
            throw new ConversionException("Failed to validate ODT file: " + e.getMessage(), e);
        }
    }
    
    /**
     * Gets conversion statistics
     */
    public ConversionStatistics getLastConversionStatistics() {
        // This would be implemented to return statistics from the last conversion
        // For now, return a placeholder
        return new ConversionStatistics();
    }
    
    /**
     * Conversion statistics holder
     */
    public static class ConversionStatistics {
        private int paragraphCount = 0;
        private int tableCount = 0;
        private int headingCount = 0;
        private int imageCount = 0;
        private int characterCount = 0;
        private long processingTimeMs = 0;
        
        // Getters and setters
        public int getParagraphCount() { return paragraphCount; }
        public void setParagraphCount(int paragraphCount) { this.paragraphCount = paragraphCount; }
        
        public int getTableCount() { return tableCount; }
        public void setTableCount(int tableCount) { this.tableCount = tableCount; }
        
        public int getHeadingCount() { return headingCount; }
        public void setHeadingCount(int headingCount) { this.headingCount = headingCount; }
        
        public int getImageCount() { return imageCount; }
        public void setImageCount(int imageCount) { this.imageCount = imageCount; }
        
        public int getCharacterCount() { return characterCount; }
        public void setCharacterCount(int characterCount) { this.characterCount = characterCount; }
        
        public long getProcessingTimeMs() { return processingTimeMs; }
        public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
        
        @Override
        public String toString() {
            return String.format("ConversionStatistics{paragraphs=%d, tables=%d, headings=%d, images=%d, characters=%d, time=%dms}",
                    paragraphCount, tableCount, headingCount, imageCount, characterCount, processingTimeMs);
        }
    }
}
