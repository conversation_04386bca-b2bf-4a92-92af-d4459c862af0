package com.talkweb.ai.converter.core.registry;

import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginMetadata;
import com.talkweb.ai.converter.core.PluginState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 默认插件注册表实现
 */
public class DefaultPluginRegistry implements PluginRegistry {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultPluginRegistry.class);
    
    private final Map<String, Plugin> plugins = new ConcurrentHashMap<>();
    private final Map<String, PluginMetadata> metadataCache = new ConcurrentHashMap<>();
    private final long creationTime = System.currentTimeMillis();

    @Override
    public void registerPlugin(Plugin plugin) throws PluginRegistryException {
        if (plugin == null) {
            throw new IllegalArgumentException("Plugin cannot be null");
        }
        
        PluginMetadata metadata = plugin.getMetadata();
        if (metadata == null) {
            throw PluginRegistryException.invalidMetadata("unknown", "Plugin metadata is null");
        }
        
        String pluginId = metadata.getId();
        if (pluginId == null || pluginId.trim().isEmpty()) {
            throw PluginRegistryException.invalidMetadata("unknown", "Plugin ID is null or empty");
        }
        
        log.debug("Registering plugin: {}", pluginId);
        
        // 检查是否已注册
        if (plugins.containsKey(pluginId)) {
            throw PluginRegistryException.alreadyRegistered(pluginId);
        }
        
        // 注册插件
        plugins.put(pluginId, plugin);
        metadataCache.put(pluginId, metadata);
        
        log.info("Successfully registered plugin: {} v{}", pluginId, metadata.getVersion());
    }

    @Override
    public boolean unregisterPlugin(String pluginId) {
        if (pluginId == null || pluginId.trim().isEmpty()) {
            return false;
        }
        
        log.debug("Unregistering plugin: {}", pluginId);
        
        Plugin removedPlugin = plugins.remove(pluginId);
        metadataCache.remove(pluginId);
        
        if (removedPlugin != null) {
            log.info("Successfully unregistered plugin: {}", pluginId);
            return true;
        }
        
        return false;
    }

    @Override
    public Optional<Plugin> getPlugin(String pluginId) {
        if (pluginId == null || pluginId.trim().isEmpty()) {
            return Optional.empty();
        }
        return Optional.ofNullable(plugins.get(pluginId));
    }

    @Override
    public Optional<Plugin> getPlugin(String pluginId, String version) {
        return getPlugin(pluginId)
            .filter(plugin -> {
                PluginMetadata metadata = plugin.getMetadata();
                return metadata != null && Objects.equals(metadata.getVersion(), version);
            });
    }

    @Override
    public Collection<Plugin> getAllPlugins() {
        return new ArrayList<>(plugins.values());
    }

    @Override
    public List<Plugin> getPluginsByState(PluginState state) {
        if (state == null) {
            return Collections.emptyList();
        }
        
        return plugins.values().stream()
            .filter(plugin -> plugin.getState() == state)
            .collect(Collectors.toList());
    }

    @Override
    public boolean isPluginRegistered(String pluginId) {
        return pluginId != null && plugins.containsKey(pluginId);
    }

    @Override
    public int getPluginCount() {
        return plugins.size();
    }

    @Override
    public int getPluginCountByState(PluginState state) {
        if (state == null) {
            return 0;
        }
        
        return (int) plugins.values().stream()
            .filter(plugin -> plugin.getState() == state)
            .count();
    }

    @Override
    public boolean updatePluginState(String pluginId, PluginState newState) {
        if (pluginId == null || newState == null) {
            return false;
        }
        
        Plugin plugin = plugins.get(pluginId);
        if (plugin == null) {
            return false;
        }
        
        // 注意：这里假设Plugin接口有setState方法，实际可能需要调整
        // 由于当前Plugin接口没有setState方法，这里只是记录日志
        log.debug("Plugin state update requested for {}: {} -> {}", 
                 pluginId, plugin.getState(), newState);
        
        return true;
    }

    @Override
    public Optional<PluginMetadata> getPluginMetadata(String pluginId) {
        if (pluginId == null || pluginId.trim().isEmpty()) {
            return Optional.empty();
        }
        return Optional.ofNullable(metadataCache.get(pluginId));
    }

    @Override
    public List<Plugin> findPlugins(PluginSearchCriteria criteria) {
        if (criteria == null) {
            return new ArrayList<>(plugins.values());
        }
        
        return plugins.values().stream()
            .filter(plugin -> matchesCriteria(plugin, criteria))
            .collect(Collectors.toList());
    }

    @Override
    public void clear() {
        log.info("Clearing plugin registry ({} plugins)", plugins.size());
        plugins.clear();
        metadataCache.clear();
    }

    @Override
    public RegistryStatistics getStatistics() {
        int totalPlugins = plugins.size();
        int runningPlugins = getPluginCountByState(PluginState.RUNNING);
        int stoppedPlugins = getPluginCountByState(PluginState.STOPPED);
        int failedPlugins = getPluginCountByState(PluginState.FAILED);
        
        return new RegistryStatistics(totalPlugins, runningPlugins, stoppedPlugins, 
                                    failedPlugins, creationTime);
    }

    /**
     * 检查插件是否匹配搜索条件
     */
    private boolean matchesCriteria(Plugin plugin, PluginSearchCriteria criteria) {
        PluginMetadata metadata = plugin.getMetadata();
        if (metadata == null) {
            return false;
        }
        
        // 检查状态
        if (criteria.getState() != null && plugin.getState() != criteria.getState()) {
            return false;
        }
        
        // 检查名称模式
        if (criteria.getNamePattern() != null) {
            if (!matchesPattern(metadata.getName(), criteria.getNamePattern(), criteria.isExactMatch())) {
                return false;
            }
        }
        
        // 检查提供者模式
        if (criteria.getProviderPattern() != null) {
            if (!matchesPattern(metadata.getProvider(), criteria.getProviderPattern(), criteria.isExactMatch())) {
                return false;
            }
        }
        
        // 检查版本模式
        if (criteria.getVersionPattern() != null) {
            if (!matchesPattern(metadata.getVersion(), criteria.getVersionPattern(), criteria.isExactMatch())) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 检查字符串是否匹配模式
     */
    private boolean matchesPattern(String value, String pattern, boolean exactMatch) {
        if (value == null) {
            return pattern == null;
        }
        
        if (pattern == null) {
            return true;
        }
        
        if (exactMatch) {
            return value.equals(pattern);
        } else {
            return value.toLowerCase().contains(pattern.toLowerCase());
        }
    }
}
