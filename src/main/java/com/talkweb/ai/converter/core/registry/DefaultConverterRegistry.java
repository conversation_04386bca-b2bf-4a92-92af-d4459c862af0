package com.talkweb.ai.converter.core.registry;

import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.DocumentConverter;
import com.talkweb.ai.converter.core.converter.ElementConverter;

import java.io.File;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import org.jsoup.nodes.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 转换器注册表默认实现
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class DefaultConverterRegistry implements ConverterRegistry {
    
    private static final Logger logger = LoggerFactory.getLogger(DefaultConverterRegistry.class);
    
    private final List<DocumentConverter> documentConverters = new CopyOnWriteArrayList<>();
    private final List<ElementConverter> elementConverters = new CopyOnWriteArrayList<>();
    
    @Override
    public ConverterRegistry registerConverter(DocumentConverter converter) {
        if (converter == null) {
            throw new IllegalArgumentException("转换器不能为空");
        }
        
        if (!documentConverters.contains(converter)) {
            documentConverters.add(converter);
            logger.info("注册文档转换器: {}", converter.getClass().getName());
        }
        
        return this;
    }
    
    @Override
    public ConverterRegistry registerConverter(ElementConverter converter) {
        if (converter == null) {
            throw new IllegalArgumentException("转换器不能为空");
        }
        
        if (!elementConverters.contains(converter)) {
            elementConverters.add(converter);
            
            // 按优先级排序
            elementConverters.sort(Comparator.comparingInt(ElementConverter::getPriority).reversed());
            
            logger.info("注册元素转换器: {}", converter.getClass().getName());
        }
        
        return this;
    }
    
    @Override
    public ConverterRegistry unregisterConverter(DocumentConverter converter) {
        if (converter != null && documentConverters.remove(converter)) {
            logger.info("注销文档转换器: {}", converter.getClass().getName());
        }
        
        return this;
    }
    
    @Override
    public ConverterRegistry unregisterConverter(ElementConverter converter) {
        if (converter != null && elementConverters.remove(converter)) {
            logger.info("注销元素转换器: {}", converter.getClass().getName());
        }
        
        return this;
    }
    
    @Override
    public DocumentConverter findConverter(File file, ConversionContext context) {
        if (file == null) {
            return null;
        }
        
        for (DocumentConverter converter : documentConverters) {
            if (converter.supports(file, context)) {
                logger.debug("找到适合的文档转换器: {} 用于文件: {}", 
                        converter.getClass().getName(), file.getName());
                return converter;
            }
        }
        
        logger.warn("找不到适合的文档转换器用于文件: {}", file.getName());
        return null;
    }
    
    @Override
    public ElementConverter findConverter(Element element, ConversionContext context) {
        if (element == null) {
            return null;
        }
        
        for (ElementConverter converter : elementConverters) {
            if (converter.supports(element, context)) {
                logger.debug("找到适合的元素转换器: {} 用于元素: {}", 
                        converter.getClass().getName(), element.tagName());
                return converter;
            }
        }
        
        logger.debug("找不到适合的元素转换器用于元素: {}", element.tagName());
        return null;
    }
    
    @Override
    public List<DocumentConverter> getDocumentConverters() {
        return Collections.unmodifiableList(documentConverters);
    }
    
    @Override
    public List<ElementConverter> getElementConverters() {
        return Collections.unmodifiableList(elementConverters);
    }
    
    @Override
    public Set<String> getSupportedExtensions() {
        Set<String> extensions = new HashSet<>();
        
        for (DocumentConverter converter : documentConverters) {
            extensions.addAll(converter.getSupportedExtensions());
        }
        
        return Collections.unmodifiableSet(extensions);
    }
    
    @Override
    public Set<String> getSupportedTags() {
        Set<String> tags = elementConverters.stream()
                .flatMap(converter -> converter.getSupportedTags().stream())
                .collect(Collectors.toSet());
        
        return Collections.unmodifiableSet(tags);
    }
    
    @Override
    public ConverterRegistry clear() {
        documentConverters.clear();
        elementConverters.clear();
        logger.info("清除所有注册的转换器");
        return this;
    }
}