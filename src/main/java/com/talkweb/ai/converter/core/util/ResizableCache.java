package com.talkweb.ai.converter.core.util;

/**
 * Interface for caches that support dynamic resizing
 */
public interface ResizableCache {
    
    /**
     * Resize the cache by the given factor
     * 
     * @param factor resize factor (0.5 = reduce to 50%, 2.0 = double size)
     */
    void resize(double factor);
    
    /**
     * Get current maximum size of the cache
     * 
     * @return current max size
     */
    int getMaxSize();
    
    /**
     * Set new maximum size for the cache
     * 
     * @param newMaxSize new maximum size
     */
    void setMaxSize(int newMaxSize);
}
