package com.talkweb.ai.converter.core.converter;

import java.util.Map;
import java.util.Optional;

/**
 * 通用转换上下文接口，定义了转换过程中需要的上下文信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public interface ConversionContext {
    
    /**
     * 获取上下文属性
     * 
     * @param key 属性键
     * @return 属性值的Optional包装
     */
    Optional<Object> getProperty(String key);
    
    /**
     * 获取上下文属性，如果不存在则返回默认值
     * 
     * @param key 属性键
     * @param defaultValue 默认值
     * @param <T> 属性类型
     * @return 属性值，如果不存在则返回默认值
     */
    <T> T getProperty(String key, T defaultValue);
    
    /**
     * 设置上下文属性
     * 
     * @param key 属性键
     * @param value 属性值
     * @return 当前上下文对象，用于链式调用
     */
    ConversionContext setProperty(String key, Object value);
    
    /**
     * 获取所有上下文属性
     * 
     * @return 不可变的属性映射
     */
    Map<String, Object> getProperties();
    
    /**
     * 获取缓存对象
     * 
     * @param key 缓存键
     * @return 缓存值的Optional包装
     */
    Optional<Object> getCache(String key);
    
    /**
     * 设置缓存对象
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @return 当前上下文对象，用于链式调用
     */
    ConversionContext setCache(String key, Object value);
    
    /**
     * 清除缓存
     * 
     * @return 当前上下文对象，用于链式调用
     */
    ConversionContext clearCache();
    
    /**
     * 获取转换模式
     * 
     * @return 转换模式
     */
    String getMode();
    
    /**
     * 获取转换选项
     * 
     * @return 转换选项
     */
    ConversionOptions getOptions();
    
    /**
     * 创建上下文构建器
     * 
     * @return 上下文构建器
     */
    static Builder builder() {
        return new DefaultConversionContext.Builder();
    }
    
    /**
     * 上下文构建器接口
     */
    interface Builder {
        /**
         * 设置转换模式
         * 
         * @param mode 转换模式
         * @return 当前构建器
         */
        Builder mode(String mode);
        
        /**
         * 设置转换选项
         * 
         * @param options 转换选项
         * @return 当前构建器
         */
        Builder options(ConversionOptions options);
        
        /**
         * 添加属性
         * 
         * @param key 属性键
         * @param value 属性值
         * @return 当前构建器
         */
        Builder property(String key, Object value);
        
        /**
         * 构建上下文对象
         * 
         * @return 上下文对象
         */
        ConversionContext build();
    }
}