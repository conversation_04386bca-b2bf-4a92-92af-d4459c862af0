package com.talkweb.ai.converter.core.registry;

import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.DocumentConverter;
import com.talkweb.ai.converter.core.converter.ElementConverter;

import java.io.File;
import java.util.List;
import java.util.Set;

import org.jsoup.nodes.Element;

/**
 * 转换器注册表接口，用于管理和查找转换器
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public interface ConverterRegistry {
    
    /**
     * 注册文档转换器
     * 
     * @param converter 文档转换器
     * @return 当前注册表实例
     */
    ConverterRegistry registerConverter(DocumentConverter converter);
    
    /**
     * 注册元素转换器
     * 
     * @param converter 元素转换器
     * @return 当前注册表实例
     */
    ConverterRegistry registerConverter(ElementConverter converter);
    
    /**
     * 注销文档转换器
     * 
     * @param converter 文档转换器
     * @return 当前注册表实例
     */
    ConverterRegistry unregisterConverter(DocumentConverter converter);
    
    /**
     * 注销元素转换器
     * 
     * @param converter 元素转换器
     * @return 当前注册表实例
     */
    ConverterRegistry unregisterConverter(ElementConverter converter);
    
    /**
     * 查找适合指定文件的转换器
     * 
     * @param file 文件
     * @param context 转换上下文
     * @return 转换器，如果找不到则返回null
     */
    DocumentConverter findConverter(File file, ConversionContext context);
    
    /**
     * 查找适合指定元素的转换器
     * 
     * @param element 元素
     * @param context 转换上下文
     * @return 转换器，如果找不到则返回null
     */
    ElementConverter findConverter(Element element, ConversionContext context);
    
    /**
     * 获取所有已注册的文档转换器
     * 
     * @return 文档转换器列表
     */
    List<DocumentConverter> getDocumentConverters();
    
    /**
     * 获取所有已注册的元素转换器
     * 
     * @return 元素转换器列表
     */
    List<ElementConverter> getElementConverters();
    
    /**
     * 获取支持的文件扩展名
     * 
     * @return 支持的文件扩展名集合
     */
    Set<String> getSupportedExtensions();
    
    /**
     * 获取支持的HTML标签
     * 
     * @return 支持的HTML标签集合
     */
    Set<String> getSupportedTags();
    
    /**
     * 清除所有注册的转换器
     * 
     * @return 当前注册表实例
     */
    ConverterRegistry clear();
}