package com.talkweb.ai.converter.core.isolation;

import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginState;

/**
 * 插件隔离异常
 */
public class PluginIsolationException extends PluginException {
    
    private final String pluginId;
    private final IsolationErrorType errorType;

    public PluginIsolationException(String message) {
        this(message, null, IsolationErrorType.UNKNOWN, null);
    }

    public PluginIsolationException(String message, Throwable cause) {
        this(message, null, IsolationErrorType.UNKNOWN, cause);
    }

    public PluginIsolationException(String message, String pluginId, IsolationErrorType errorType, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginId = pluginId;
        this.errorType = errorType;
    }

    public String getPluginId() {
        return pluginId;
    }

    public IsolationErrorType getErrorType() {
        return errorType;
    }

    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(errorType.getDescription()).append(": ").append(getMessage());
        
        if (pluginId != null) {
            sb.append(" (Plugin: ").append(pluginId).append(")");
        }
        
        return sb.toString();
    }

    /**
     * 隔离错误类型
     */
    public enum IsolationErrorType {
        CLASS_LOADER_CREATION_FAILED("Class loader creation failed"),
        SANDBOX_CREATION_FAILED("Sandbox creation failed"),
        SECURITY_MANAGER_ERROR("Security manager error"),
        ISOLATION_CONTEXT_ERROR("Isolation context error"),
        COMMUNICATION_BLOCKED("Communication blocked"),
        RESOURCE_ACCESS_DENIED("Resource access denied"),
        UNKNOWN("Unknown isolation error");

        private final String description;

        IsolationErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 工厂方法
    public static PluginIsolationException classLoaderCreationFailed(String pluginId, Throwable cause) {
        return new PluginIsolationException(
            "Failed to create isolated class loader for plugin: " + pluginId,
            pluginId,
            IsolationErrorType.CLASS_LOADER_CREATION_FAILED,
            cause
        );
    }

    public static PluginIsolationException sandboxCreationFailed(String pluginId, Throwable cause) {
        return new PluginIsolationException(
            "Failed to create sandbox for plugin: " + pluginId,
            pluginId,
            IsolationErrorType.SANDBOX_CREATION_FAILED,
            cause
        );
    }

    public static PluginIsolationException securityManagerError(String message, Throwable cause) {
        return new PluginIsolationException(
            "Security manager error: " + message,
            null,
            IsolationErrorType.SECURITY_MANAGER_ERROR,
            cause
        );
    }

    public static PluginIsolationException isolationContextError(String pluginId, String message) {
        return new PluginIsolationException(
            "Isolation context error: " + message,
            pluginId,
            IsolationErrorType.ISOLATION_CONTEXT_ERROR,
            null
        );
    }

    public static PluginIsolationException communicationBlocked(String fromPluginId, String toPluginId) {
        return new PluginIsolationException(
            "Communication blocked from plugin " + fromPluginId + " to plugin " + toPluginId,
            fromPluginId,
            IsolationErrorType.COMMUNICATION_BLOCKED,
            null
        );
    }

    public static PluginIsolationException resourceAccessDenied(String pluginId, String resource) {
        return new PluginIsolationException(
            "Resource access denied: " + resource,
            pluginId,
            IsolationErrorType.RESOURCE_ACCESS_DENIED,
            null
        );
    }
}
