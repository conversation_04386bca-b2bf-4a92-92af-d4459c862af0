package com.talkweb.ai.converter.core.security;

import java.io.File;
import java.net.URLClassLoader;
import java.nio.file.Path;
import java.security.Permission;
import java.util.Set;

/**
 * 插件沙箱环境
 * 为插件提供隔离的运行环境
 */
public interface PluginSandbox {

    /**
     * 获取沙箱ID
     * 
     * @return 沙箱ID
     */
    String getSandboxId();

    /**
     * 获取关联的插件ID
     * 
     * @return 插件ID
     */
    String getPluginId();

    /**
     * 获取沙箱类加载器
     * 
     * @return 类加载器
     */
    URLClassLoader getClassLoader();

    /**
     * 获取沙箱工作目录
     * 
     * @return 工作目录路径
     */
    Path getWorkingDirectory();

    /**
     * 获取沙箱临时目录
     * 
     * @return 临时目录路径
     */
    Path getTempDirectory();

    /**
     * 检查文件访问权限
     * 
     * @param file 文件
     * @param access 访问类型
     * @return 如果允许访问返回true
     */
    boolean checkFileAccess(File file, FileAccess access);

    /**
     * 检查网络访问权限
     * 
     * @param host 主机
     * @param port 端口
     * @param protocol 协议
     * @return 如果允许访问返回true
     */
    boolean checkNetworkAccess(String host, int port, String protocol);

    /**
     * 检查系统属性访问权限
     * 
     * @param property 系统属性名
     * @param access 访问类型
     * @return 如果允许访问返回true
     */
    boolean checkSystemPropertyAccess(String property, PropertyAccess access);

    /**
     * 获取允许的权限集合
     * 
     * @return 权限集合
     */
    Set<Permission> getAllowedPermissions();

    /**
     * 添加允许的权限
     * 
     * @param permission 权限
     */
    void addAllowedPermission(Permission permission);

    /**
     * 移除允许的权限
     * 
     * @param permission 权限
     */
    void removeAllowedPermission(Permission permission);

    /**
     * 获取资源使用限制
     * 
     * @return 资源限制
     */
    ResourceLimits getResourceLimits();

    /**
     * 设置资源使用限制
     * 
     * @param limits 资源限制
     */
    void setResourceLimits(ResourceLimits limits);

    /**
     * 获取当前资源使用情况
     * 
     * @return 资源使用情况
     */
    ResourceUsage getCurrentResourceUsage();

    /**
     * 检查是否超出资源限制
     * 
     * @return 如果超出限制返回true
     */
    boolean isResourceLimitExceeded();

    /**
     * 销毁沙箱环境
     */
    void destroy();

    /**
     * 检查沙箱是否已销毁
     * 
     * @return 如果已销毁返回true
     */
    boolean isDestroyed();

    /**
     * 文件访问类型
     */
    enum FileAccess {
        READ,
        WRITE,
        EXECUTE,
        DELETE
    }

    /**
     * 系统属性访问类型
     */
    enum PropertyAccess {
        READ,
        WRITE
    }

    /**
     * 资源限制
     */
    class ResourceLimits {
        private final long maxMemoryBytes;
        private final long maxDiskBytes;
        private final int maxThreads;
        private final long maxExecutionTimeMs;
        private final int maxFileHandles;

        public ResourceLimits(long maxMemoryBytes, long maxDiskBytes, int maxThreads, 
                            long maxExecutionTimeMs, int maxFileHandles) {
            this.maxMemoryBytes = maxMemoryBytes;
            this.maxDiskBytes = maxDiskBytes;
            this.maxThreads = maxThreads;
            this.maxExecutionTimeMs = maxExecutionTimeMs;
            this.maxFileHandles = maxFileHandles;
        }

        public long getMaxMemoryBytes() { return maxMemoryBytes; }
        public long getMaxDiskBytes() { return maxDiskBytes; }
        public int getMaxThreads() { return maxThreads; }
        public long getMaxExecutionTimeMs() { return maxExecutionTimeMs; }
        public int getMaxFileHandles() { return maxFileHandles; }
    }

    /**
     * 资源使用情况
     */
    class ResourceUsage {
        private final long usedMemoryBytes;
        private final long usedDiskBytes;
        private final int activeThreads;
        private final long executionTimeMs;
        private final int openFileHandles;

        public ResourceUsage(long usedMemoryBytes, long usedDiskBytes, int activeThreads, 
                           long executionTimeMs, int openFileHandles) {
            this.usedMemoryBytes = usedMemoryBytes;
            this.usedDiskBytes = usedDiskBytes;
            this.activeThreads = activeThreads;
            this.executionTimeMs = executionTimeMs;
            this.openFileHandles = openFileHandles;
        }

        public long getUsedMemoryBytes() { return usedMemoryBytes; }
        public long getUsedDiskBytes() { return usedDiskBytes; }
        public int getActiveThreads() { return activeThreads; }
        public long getExecutionTimeMs() { return executionTimeMs; }
        public int getOpenFileHandles() { return openFileHandles; }
    }
}
