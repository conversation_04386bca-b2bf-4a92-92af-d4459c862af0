package com.talkweb.ai.converter.core.util;

import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 转换错误处理器，用于统一处理转换过程中的异常
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ConversionErrorHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(ConversionErrorHandler.class);
    
    // 可恢复的异常类型
    private static final Set<Class<? extends Exception>> RECOVERABLE_EXCEPTIONS = new HashSet<>(Arrays.asList(
            IOException.class,
            IllegalArgumentException.class
    ));
    
    /**
     * 处理转换异常
     * 
     * @param e 异常
     * @param context 转换上下文
     * @return 转换结果
     */
    public static ConversionResult handleError(Exception e, ConversionContext context) {
        // 记录异常
        logError(e, context);
        
        // 检查是否可恢复
        boolean recoverable = isRecoverable(e);
        
        // 创建错误结果
        ConversionResult.Status status = recoverable ? 
                ConversionResult.Status.PARTIAL_SUCCESS : ConversionResult.Status.FAILED;
        
        return new ConversionResult(
                status,
                "", // 空内容
                String.format("转换失败: %s", e.getMessage())
        );
    }
    
    /**
     * 检查异常是否可恢复
     * 
     * @param e 异常
     * @return 如果可恢复返回true，否则返回false
     */
    public static boolean isRecoverable(Exception e) {
        if (e == null) {
            return false;
        }
        
        // 检查异常类型
        for (Class<? extends Exception> exceptionClass : RECOVERABLE_EXCEPTIONS) {
            if (exceptionClass.isInstance(e)) {
                return true;
            }
        }
        
        // 检查异常消息
        String message = e.getMessage();
        if (message != null) {
            message = message.toLowerCase();
            return message.contains("recoverable") || 
                   message.contains("retry") || 
                   message.contains("temporary");
        }
        
        return false;
    }
    
    /**
     * 记录异常日志
     * 
     * @param e 异常
     * @param context 转换上下文
     */
    private static void logError(Exception e, ConversionContext context) {
        if (e instanceof ConversionException) {
            logger.error("转换异常: {}", e.getMessage(), e);
        } else {
            logger.error("未预期的异常: {}", e.getMessage(), e);
        }
        
        // 记录上下文信息
        if (context != null) {
            logger.debug("转换上下文: mode={}, options={}", 
                    context.getMode(), 
                    context.getOptions().getAllOptions());
        }
    }
    
    /**
     * 创建错误处理器构建器
     * 
     * @return 错误处理器构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 错误处理器构建器
     */
    public static class Builder {
        private final Set<Class<? extends Exception>> recoverableExceptions = new HashSet<>(RECOVERABLE_EXCEPTIONS);
        
        /**
         * 添加可恢复的异常类型
         * 
         * @param exceptionClass 异常类
         * @return 当前构建器
         */
        public Builder addRecoverableException(Class<? extends Exception> exceptionClass) {
            recoverableExceptions.add(exceptionClass);
            return this;
        }
        
        /**
         * 移除可恢复的异常类型
         * 
         * @param exceptionClass 异常类
         * @return 当前构建器
         */
        public Builder removeRecoverableException(Class<? extends Exception> exceptionClass) {
            recoverableExceptions.remove(exceptionClass);
            return this;
        }
        
        /**
         * 构建错误处理器
         * 
         * @return 错误处理器
         */
        public ConversionErrorHandler build() {
            return new ConversionErrorHandler();
        }
    }
}