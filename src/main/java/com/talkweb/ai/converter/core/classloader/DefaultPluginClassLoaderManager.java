package com.talkweb.ai.converter.core.classloader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认插件类加载器管理器实现
 */
public class DefaultPluginClassLoaderManager implements PluginClassLoaderManager {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultPluginClassLoaderManager.class);
    
    private final Map<String, URLClassLoader> classLoaders = new ConcurrentHashMap<>();
    private final Map<String, ClassLoaderInfo> classLoaderInfos = new ConcurrentHashMap<>();
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private final ClassLoaderCache cache = new ClassLoaderCache();

    @Override
    public URLClassLoader createClassLoader(String pluginId, Path pluginPath) throws ClassLoaderException {
        log.debug("Creating class loader for plugin: {} at path: {}", pluginId, pluginPath);

        // 检查是否已存在
        if (classLoaders.containsKey(pluginId)) {
            throw ClassLoaderException.alreadyExists(pluginId);
        }

        // 尝试从缓存获取
        String cacheKey = generateCacheKey(pluginId, pluginPath);
        URLClassLoader cachedClassLoader = cache.get(cacheKey);
        if (cachedClassLoader != null) {
            classLoaders.put(pluginId, cachedClassLoader);
            classLoaderInfos.put(pluginId, new ClassLoaderInfo(pluginId, pluginPath, System.currentTimeMillis()));
            log.info("Reused cached class loader for plugin: {}", pluginId);
            return cachedClassLoader;
        }

        // 验证插件文件
        if (!Files.exists(pluginPath)) {
            throw ClassLoaderException.invalidJarFile(pluginId, pluginPath);
        }

        if (!pluginPath.toString().toLowerCase().endsWith(".jar")) {
            throw ClassLoaderException.invalidJarFile(pluginId, pluginPath);
        }

        try {
            // 创建类加载器
            URL pluginUrl = pluginPath.toUri().toURL();
            URLClassLoader classLoader = new URLClassLoader(
                new URL[]{pluginUrl},
                getClass().getClassLoader()
            );

            // 缓存类加载器
            classLoaders.put(pluginId, classLoader);
            classLoaderInfos.put(pluginId, new ClassLoaderInfo(pluginId, pluginPath, System.currentTimeMillis()));
            cache.put(cacheKey, classLoader);

            log.info("Successfully created class loader for plugin: {}", pluginId);
            return classLoader;

        } catch (Exception e) {
            throw ClassLoaderException.creationFailed(pluginId, pluginPath, e);
        }
    }

    @Override
    public Optional<URLClassLoader> getClassLoader(String pluginId) {
        return Optional.ofNullable(classLoaders.get(pluginId));
    }

    @Override
    public boolean removeClassLoader(String pluginId) {
        log.debug("Removing class loader for plugin: {}", pluginId);

        URLClassLoader classLoader = classLoaders.remove(pluginId);
        ClassLoaderInfo info = classLoaderInfos.remove(pluginId);

        if (classLoader != null) {
            try {
                // 从缓存中移除（如果存在）
                if (info != null) {
                    String cacheKey = generateCacheKey(pluginId, info.getPluginPath());
                    cache.remove(cacheKey);
                }

                classLoader.close();
                log.info("Successfully removed class loader for plugin: {}", pluginId);
                return true;
            } catch (IOException e) {
                log.warn("Failed to close class loader for plugin: {}", pluginId, e);
                throw new RuntimeException(ClassLoaderException.cleanupFailed(pluginId, e));
            }
        }

        return false;
    }

    @Override
    public void clearAllClassLoaders() {
        log.info("Clearing all class loaders ({} total)", classLoaders.size());

        for (String pluginId : classLoaders.keySet()) {
            try {
                removeClassLoader(pluginId);
            } catch (Exception e) {
                log.warn("Failed to remove class loader for plugin: {}", pluginId, e);
            }
        }

        classLoaders.clear();
        classLoaderInfos.clear();
        cache.clear();

        // 强制垃圾回收
        System.gc();

        log.info("All class loaders cleared");
    }

    @Override
    public boolean hasClassLoader(String pluginId) {
        return classLoaders.containsKey(pluginId);
    }

    @Override
    public ClassLoaderStatistics getStatistics() {
        int totalClassLoaders = classLoaders.size();
        int activeClassLoaders = (int) classLoaders.values().stream()
            .filter(cl -> !isClosed(cl))
            .count();

        long totalMemoryUsage = memoryBean.getHeapMemoryUsage().getUsed();

        int totalLoadedClasses = classLoaders.values().stream()
            .mapToInt(this::getLoadedClassCount)
            .sum();

        // 获取缓存统计信息
        ClassLoaderCache.CacheStatistics cacheStats = cache.getStatistics();

        return new ClassLoaderStatistics(totalClassLoaders, activeClassLoaders,
                                       totalMemoryUsage, totalLoadedClasses, cacheStats);
    }

    @Override
    public void forceGarbageCollection(String pluginId) {
        log.debug("Forcing garbage collection for plugin: {}", pluginId);
        
        URLClassLoader classLoader = classLoaders.get(pluginId);
        if (classLoader != null) {
            try {
                // 移除类加载器引用
                classLoaders.remove(pluginId);
                classLoaderInfos.remove(pluginId);
                
                // 关闭类加载器
                classLoader.close();
                
                // 强制垃圾回收
                System.gc();
                
                log.info("Forced garbage collection for plugin: {}", pluginId);
                
            } catch (IOException e) {
                log.warn("Failed to close class loader during garbage collection for plugin: {}", pluginId, e);
            }
        }
    }

    /**
     * 检查类加载器是否已关闭
     */
    private boolean isClosed(URLClassLoader classLoader) {
        try {
            // 尝试获取URLs，如果类加载器已关闭会抛出异常
            classLoader.getURLs();
            return false;
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 获取类加载器已加载的类数量
     */
    private int getLoadedClassCount(URLClassLoader classLoader) {
        try {
            // 这是一个近似值，实际实现可能需要更复杂的逻辑
            return classLoader.getURLs().length * 10; // 简化的估算
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String pluginId, Path pluginPath) {
        try {
            // 使用插件ID和文件的最后修改时间生成缓存键
            long lastModified = Files.getLastModifiedTime(pluginPath).toMillis();
            return pluginId + "_" + pluginPath.getFileName() + "_" + lastModified;
        } catch (Exception e) {
            // 如果无法获取修改时间，使用插件ID和路径
            return pluginId + "_" + pluginPath.getFileName();
        }
    }

    /**
     * 关闭管理器并清理资源
     */
    public void shutdown() {
        log.info("Shutting down class loader manager");
        clearAllClassLoaders();
        cache.shutdown();
        log.info("Class loader manager shutdown completed");
    }

    /**
     * 类加载器信息
     */
    private static class ClassLoaderInfo {
        private final String pluginId;
        private final Path pluginPath;
        private final long creationTime;

        public ClassLoaderInfo(String pluginId, Path pluginPath, long creationTime) {
            this.pluginId = pluginId;
            this.pluginPath = pluginPath;
            this.creationTime = creationTime;
        }

        public String getPluginId() {
            return pluginId;
        }

        public Path getPluginPath() {
            return pluginPath;
        }

        public long getCreationTime() {
            return creationTime;
        }
    }
}
