package com.talkweb.ai.converter.core.registry;

import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginMetadata;
import com.talkweb.ai.converter.core.PluginState;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 插件注册表接口
 * 统一管理插件元数据和状态
 */
public interface PluginRegistry {

    /**
     * 注册插件
     * 
     * @param plugin 插件实例
     * @throws PluginRegistryException 如果注册失败
     */
    void registerPlugin(Plugin plugin) throws PluginRegistryException;

    /**
     * 取消注册插件
     * 
     * @param pluginId 插件ID
     * @return 如果成功取消注册返回true
     */
    boolean unregisterPlugin(String pluginId);

    /**
     * 根据ID获取插件
     * 
     * @param pluginId 插件ID
     * @return 插件实例，如果不存在则返回空
     */
    Optional<Plugin> getPlugin(String pluginId);

    /**
     * 根据ID和版本获取插件
     * 
     * @param pluginId 插件ID
     * @param version 插件版本
     * @return 插件实例，如果不存在则返回空
     */
    Optional<Plugin> getPlugin(String pluginId, String version);

    /**
     * 获取所有已注册的插件
     * 
     * @return 插件集合
     */
    Collection<Plugin> getAllPlugins();

    /**
     * 根据状态获取插件
     * 
     * @param state 插件状态
     * @return 指定状态的插件列表
     */
    List<Plugin> getPluginsByState(PluginState state);

    /**
     * 检查插件是否已注册
     * 
     * @param pluginId 插件ID
     * @return 如果已注册返回true
     */
    boolean isPluginRegistered(String pluginId);

    /**
     * 获取插件数量
     * 
     * @return 已注册的插件数量
     */
    int getPluginCount();

    /**
     * 获取指定状态的插件数量
     * 
     * @param state 插件状态
     * @return 指定状态的插件数量
     */
    int getPluginCountByState(PluginState state);

    /**
     * 更新插件状态
     * 
     * @param pluginId 插件ID
     * @param newState 新状态
     * @return 如果更新成功返回true
     */
    boolean updatePluginState(String pluginId, PluginState newState);

    /**
     * 获取插件元数据
     * 
     * @param pluginId 插件ID
     * @return 插件元数据，如果不存在则返回空
     */
    Optional<PluginMetadata> getPluginMetadata(String pluginId);

    /**
     * 查找插件
     * 
     * @param criteria 查找条件
     * @return 匹配的插件列表
     */
    List<Plugin> findPlugins(PluginSearchCriteria criteria);

    /**
     * 清空注册表
     */
    void clear();

    /**
     * 获取注册表统计信息
     * 
     * @return 统计信息
     */
    RegistryStatistics getStatistics();

    /**
     * 插件搜索条件
     */
    class PluginSearchCriteria {
        private String namePattern;
        private String providerPattern;
        private String versionPattern;
        private PluginState state;
        private boolean exactMatch = false;

        public PluginSearchCriteria() {}

        public PluginSearchCriteria namePattern(String pattern) {
            this.namePattern = pattern;
            return this;
        }

        public PluginSearchCriteria providerPattern(String pattern) {
            this.providerPattern = pattern;
            return this;
        }

        public PluginSearchCriteria versionPattern(String pattern) {
            this.versionPattern = pattern;
            return this;
        }

        public PluginSearchCriteria state(PluginState state) {
            this.state = state;
            return this;
        }

        public PluginSearchCriteria exactMatch(boolean exactMatch) {
            this.exactMatch = exactMatch;
            return this;
        }

        // Getters
        public String getNamePattern() { return namePattern; }
        public String getProviderPattern() { return providerPattern; }
        public String getVersionPattern() { return versionPattern; }
        public PluginState getState() { return state; }
        public boolean isExactMatch() { return exactMatch; }
    }

    /**
     * 注册表统计信息
     */
    class RegistryStatistics {
        private final int totalPlugins;
        private final int runningPlugins;
        private final int stoppedPlugins;
        private final int failedPlugins;
        private final long registryCreationTime;

        public RegistryStatistics(int totalPlugins, int runningPlugins, int stoppedPlugins, 
                                int failedPlugins, long registryCreationTime) {
            this.totalPlugins = totalPlugins;
            this.runningPlugins = runningPlugins;
            this.stoppedPlugins = stoppedPlugins;
            this.failedPlugins = failedPlugins;
            this.registryCreationTime = registryCreationTime;
        }

        public int getTotalPlugins() { return totalPlugins; }
        public int getRunningPlugins() { return runningPlugins; }
        public int getStoppedPlugins() { return stoppedPlugins; }
        public int getFailedPlugins() { return failedPlugins; }
        public long getRegistryCreationTime() { return registryCreationTime; }
    }
}
