package com.talkweb.ai.converter.core;

import java.util.Optional;
import java.util.Properties;

/**
 * 插件上下文接口，提供插件运行时的环境信息和功能
 */
public interface PluginContext {

    /**
     * 获取插件的类加载器
     * @return 类加载器
     */
    ClassLoader getPluginClassLoader();

    /**
     * 获取插件的配置属性
     * @return 配置属性
     */
    Properties getConfiguration();

    /**
     * 获取插件管理器
     * @return 插件管理器实例
     */
    PluginManager getPluginManager();

    /**
     * 获取插件的工作目录
     * @return 工作目录路径
     */
    String getWorkDir();

    /**
     * 获取临时目录
     * @return 临时目录路径
     */
    String getTempDir();

    /**
     * 记录信息级别日志
     * @param message 日志消息
     * @param args 日志参数
     */
    void info(String message, Object... args);

    /**
     * 记录警告级别日志
     * @param message 日志消息
     * @param args 日志参数
     */
    void warn(String message, Object... args);

    /**
     * 记录错误级别日志
     * @param message 日志消息
     * @param throwable 异常
     * @param args 日志参数
     */
    void error(String message, Throwable throwable, Object... args);

    /**
     * 记录调试级别日志
     * @param message 日志消息
     * @param args 日志参数
     */
    void debug(String message, Object... args);

    /**
     * 在插件上下文中注册一个共享对象
     * @param key 对象的键
     * @param value 对象实例
     */
    void setSharedObject(String key, Object value);

    /**
     * 从插件上下文中获取一个共享对象
     * @param key 对象的键
     * @return Optional包含的对象实例
     */
    Optional<Object> getSharedObject(String key);
}
