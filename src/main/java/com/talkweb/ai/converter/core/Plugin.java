package com.talkweb.ai.converter.core;

/**
 * 插件接口，所有插件必须实现此接口
 */
public interface Plugin {

    /**
     * 获取插件元数据
     * @return 插件元数据对象
     */
    PluginMetadata getMetadata();

    /**
     * 获取插件当前状态
     * @return 插件状态
     */
    PluginState getState();

    /**
     * 初始化插件
     * @param context 插件上下文
     * @throws PluginException 如果初始化失败
     */
    void init(PluginContext context) throws PluginException;

    /**
     * 启动插件
     * @throws PluginException 如果启动失败
     */
    void start() throws PluginException;

    /**
     * 停止插件
     * @throws PluginException 如果停止失败
     */
    void stop() throws PluginException;

    /**
     * 销毁插件，释放资源
     * @throws PluginException 如果销毁失败
     */
    void destroy() throws PluginException;

}
