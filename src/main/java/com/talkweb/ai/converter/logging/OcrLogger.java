package com.talkweb.ai.converter.logging;

import com.talkweb.ai.converter.model.OcrResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * OCR专用日志记录器
 * 
 * 提供详细的OCR处理过程日志记录功能，包括：
 * - 处理过程跟踪
 * - 错误详情记录
 * - 性能分析日志
 * - 结构化日志输出
 * - 上下文信息管理
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class OcrLogger {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrLogger.class);
    private static final Logger performanceLogger = LoggerFactory.getLogger("OCR_PERFORMANCE");
    private static final Logger errorLogger = LoggerFactory.getLogger("OCR_ERROR");
    private static final Logger processLogger = LoggerFactory.getLogger("OCR_PROCESS");
    
    // MDC键常量
    private static final String MDC_REQUEST_ID = "requestId";
    private static final String MDC_IMAGE_TYPE = "imageType";
    private static final String MDC_IMAGE_SIZE = "imageSize";
    private static final String MDC_PROCESSING_STAGE = "processingStage";
    private static final String MDC_THREAD_NAME = "threadName";
    
    // 活跃的处理上下文
    private final Map<String, ProcessingContext> activeContexts = new ConcurrentHashMap<>();
    
    /**
     * 开始OCR处理日志记录
     */
    public String startProcessing(File imageFile) {
        String requestId = generateRequestId();
        
        ProcessingContext context = new ProcessingContext(
            requestId,
            imageFile.getName(),
            "file",
            imageFile.length(),
            LocalDateTime.now()
        );
        
        activeContexts.put(requestId, context);
        setupMDC(context);
        
        processLogger.info("OCR processing started for file: {} (size: {} bytes)", 
                          imageFile.getName(), imageFile.length());
        
        return requestId;
    }
    
    /**
     * 开始OCR处理日志记录（BufferedImage）
     */
    public String startProcessing(BufferedImage image, String imageName) {
        String requestId = generateRequestId();
        
        int imageSize = image.getWidth() * image.getHeight() * 4; // 估算字节大小
        ProcessingContext context = new ProcessingContext(
            requestId,
            imageName != null ? imageName : "buffered_image",
            "buffered",
            imageSize,
            LocalDateTime.now()
        );
        
        activeContexts.put(requestId, context);
        setupMDC(context);
        
        processLogger.info("OCR processing started for BufferedImage: {} ({}x{}, estimated size: {} bytes)", 
                          context.imageName, image.getWidth(), image.getHeight(), imageSize);
        
        return requestId;
    }
    
    /**
     * 记录处理阶段
     */
    public void logProcessingStage(String requestId, String stage, String details) {
        ProcessingContext context = activeContexts.get(requestId);
        if (context != null) {
            context.addStage(stage, details);
            MDC.put(MDC_PROCESSING_STAGE, stage);
            
            processLogger.debug("Processing stage [{}]: {}", stage, details);
        }
    }
    
    /**
     * 记录性能信息
     */
    public void logPerformance(String requestId, String operation, long durationMs, Map<String, Object> metrics) {
        ProcessingContext context = activeContexts.get(requestId);
        if (context != null) {
            setupMDC(context);
            
            StringBuilder metricsStr = new StringBuilder();
            if (metrics != null && !metrics.isEmpty()) {
                metrics.forEach((key, value) -> 
                    metricsStr.append(key).append("=").append(value).append(", "));
                metricsStr.setLength(metricsStr.length() - 2); // 移除最后的", "
            }
            
            performanceLogger.info("Performance [{}]: duration={}ms, metrics=[{}]", 
                                 operation, durationMs, metricsStr.toString());
        }
    }
    
    /**
     * 记录错误详情
     */
    public void logError(String requestId, String errorType, String errorMessage, Throwable throwable, 
                        Map<String, Object> errorContext) {
        ProcessingContext context = activeContexts.get(requestId);
        if (context != null) {
            context.addError(errorType, errorMessage, throwable);
            setupMDC(context);
            
            StringBuilder contextStr = new StringBuilder();
            if (errorContext != null && !errorContext.isEmpty()) {
                errorContext.forEach((key, value) -> 
                    contextStr.append(key).append("=").append(value).append(", "));
                contextStr.setLength(contextStr.length() - 2);
            }
            
            if (throwable != null) {
                errorLogger.error("OCR Error [{}]: {} | Context: [{}]", 
                                errorType, errorMessage, contextStr.toString(), throwable);
            } else {
                errorLogger.error("OCR Error [{}]: {} | Context: [{}]", 
                                errorType, errorMessage, contextStr.toString());
            }
        }
    }
    
    /**
     * 记录处理完成
     */
    public void logProcessingComplete(String requestId, OcrResult result) {
        ProcessingContext context = activeContexts.get(requestId);
        if (context != null) {
            context.setCompleted(result);
            setupMDC(context);
            
            long totalDuration = context.getTotalDurationMs();
            
            if (result.isSuccess()) {
                processLogger.info("OCR processing completed successfully: confidence={:.2f}%, " +
                                 "text_length={}, duration={}ms, stages={}", 
                                 result.getConfidence(), 
                                 result.getText() != null ? result.getText().length() : 0,
                                 totalDuration,
                                 context.getStageCount());
                
                // 记录详细性能信息
                logDetailedPerformance(context, result);
            } else {
                processLogger.warn("OCR processing failed: status={}, error={}, duration={}ms", 
                                 result.getStatus(), result.getErrorMessage(), totalDuration);
            }
            
            // 清理上下文
            activeContexts.remove(requestId);
            clearMDC();
        }
    }
    
    /**
     * 记录缓存操作
     */
    public void logCacheOperation(String requestId, String operation, boolean hit, String cacheKey) {
        ProcessingContext context = activeContexts.get(requestId);
        if (context != null) {
            setupMDC(context);
            
            if (hit) {
                processLogger.debug("Cache HIT [{}]: key={}", operation, cacheKey);
            } else {
                processLogger.debug("Cache MISS [{}]: key={}", operation, cacheKey);
            }
        }
    }
    
    /**
     * 记录配置信息
     */
    public void logConfiguration(String requestId, Map<String, Object> config) {
        ProcessingContext context = activeContexts.get(requestId);
        if (context != null) {
            setupMDC(context);
            
            StringBuilder configStr = new StringBuilder();
            config.forEach((key, value) -> 
                configStr.append(key).append("=").append(value).append(", "));
            if (configStr.length() > 2) {
                configStr.setLength(configStr.length() - 2);
            }
            
            processLogger.debug("OCR Configuration: [{}]", configStr.toString());
        }
    }
    
    /**
     * 记录图像预处理信息
     */
    public void logImagePreprocessing(String requestId, String operation, 
                                    int originalWidth, int originalHeight,
                                    int processedWidth, int processedHeight) {
        ProcessingContext context = activeContexts.get(requestId);
        if (context != null) {
            setupMDC(context);
            
            processLogger.debug("Image preprocessing [{}]: {}x{} -> {}x{}", 
                              operation, originalWidth, originalHeight, 
                              processedWidth, processedHeight);
        }
    }
    
    /**
     * 获取处理上下文
     */
    public ProcessingContext getContext(String requestId) {
        return activeContexts.get(requestId);
    }
    
    /**
     * 获取活跃上下文数量
     */
    public int getActiveContextCount() {
        return activeContexts.size();
    }
    
    // 私有辅助方法
    
    private void setupMDC(ProcessingContext context) {
        MDC.put(MDC_REQUEST_ID, context.requestId);
        MDC.put(MDC_IMAGE_TYPE, context.imageType);
        MDC.put(MDC_IMAGE_SIZE, String.valueOf(context.imageSize));
        MDC.put(MDC_THREAD_NAME, Thread.currentThread().getName());
    }
    
    private void clearMDC() {
        MDC.remove(MDC_REQUEST_ID);
        MDC.remove(MDC_IMAGE_TYPE);
        MDC.remove(MDC_IMAGE_SIZE);
        MDC.remove(MDC_PROCESSING_STAGE);
        MDC.remove(MDC_THREAD_NAME);
    }
    
    private void logDetailedPerformance(ProcessingContext context, OcrResult result) {
        Map<String, Object> perfMetrics = Map.of(
            "confidence", result.getConfidence(),
            "text_length", result.getText() != null ? result.getText().length() : 0,
            "word_count", result.getWords() != null ? result.getWords().size() : 0,
            "processing_time_ms", result.getProcessingTimeMs(),
            "total_stages", context.getStageCount(),
            "image_size_bytes", context.imageSize,
            "success", result.isSuccess()
        );
        
        logPerformance(context.requestId, "ocr_complete", context.getTotalDurationMs(), perfMetrics);
    }
    
    private String generateRequestId() {
        return "ocr_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * 处理上下文类
     */
    public static class ProcessingContext {
        private final String requestId;
        private final String imageName;
        private final String imageType;
        private final long imageSize;
        private final LocalDateTime startTime;
        private LocalDateTime endTime;
        private OcrResult result;
        
        private final Map<String, StageInfo> stages = new ConcurrentHashMap<>();
        private final Map<String, ErrorInfo> errors = new ConcurrentHashMap<>();
        
        public ProcessingContext(String requestId, String imageName, String imageType, 
                               long imageSize, LocalDateTime startTime) {
            this.requestId = requestId;
            this.imageName = imageName;
            this.imageType = imageType;
            this.imageSize = imageSize;
            this.startTime = startTime;
        }
        
        public void addStage(String stage, String details) {
            stages.put(stage, new StageInfo(stage, details, LocalDateTime.now()));
        }
        
        public void addError(String errorType, String errorMessage, Throwable throwable) {
            errors.put(errorType, new ErrorInfo(errorType, errorMessage, throwable, LocalDateTime.now()));
        }
        
        public void setCompleted(OcrResult result) {
            this.result = result;
            this.endTime = LocalDateTime.now();
        }
        
        public long getTotalDurationMs() {
            LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
            return java.time.Duration.between(startTime, end).toMillis();
        }
        
        public int getStageCount() {
            return stages.size();
        }
        
        public boolean hasErrors() {
            return !errors.isEmpty();
        }
        
        // Getters
        public String getRequestId() { return requestId; }
        public String getImageName() { return imageName; }
        public String getImageType() { return imageType; }
        public long getImageSize() { return imageSize; }
        public LocalDateTime getStartTime() { return startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public OcrResult getResult() { return result; }
        public Map<String, StageInfo> getStages() { return new ConcurrentHashMap<>(stages); }
        public Map<String, ErrorInfo> getErrors() { return new ConcurrentHashMap<>(errors); }
        
        public static class StageInfo {
            private final String stage;
            private final String details;
            private final LocalDateTime timestamp;
            
            public StageInfo(String stage, String details, LocalDateTime timestamp) {
                this.stage = stage;
                this.details = details;
                this.timestamp = timestamp;
            }
            
            public String getStage() { return stage; }
            public String getDetails() { return details; }
            public LocalDateTime getTimestamp() { return timestamp; }
        }
        
        public static class ErrorInfo {
            private final String errorType;
            private final String errorMessage;
            private final Throwable throwable;
            private final LocalDateTime timestamp;
            
            public ErrorInfo(String errorType, String errorMessage, Throwable throwable, LocalDateTime timestamp) {
                this.errorType = errorType;
                this.errorMessage = errorMessage;
                this.throwable = throwable;
                this.timestamp = timestamp;
            }
            
            public String getErrorType() { return errorType; }
            public String getErrorMessage() { return errorMessage; }
            public Throwable getThrowable() { return throwable; }
            public LocalDateTime getTimestamp() { return timestamp; }
        }
    }
}
