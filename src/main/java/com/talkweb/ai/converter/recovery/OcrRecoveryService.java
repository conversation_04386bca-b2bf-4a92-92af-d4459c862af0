package com.talkweb.ai.converter.recovery;

import com.talkweb.ai.converter.logging.OcrLogger;
import com.talkweb.ai.converter.model.OcrResult;
import com.talkweb.ai.converter.service.OcrService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * OCR恢复服务
 * 
 * 提供OCR处理的错误恢复和重试机制
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Service
public class OcrRecoveryService {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrRecoveryService.class);
    
    @Autowired
    private OcrErrorHandler errorHandler;
    
    @Autowired
    private OcrService ocrService;
    
    @Autowired
    private OcrLogger ocrLogger;
    
    /**
     * 带恢复机制的OCR处理（BufferedImage）
     */
    public CompletableFuture<OcrResult> processWithRecovery(BufferedImage image) {
        return processWithRecovery(image, 1);
    }
    
    /**
     * 带恢复机制的OCR处理（File）
     */
    public CompletableFuture<OcrResult> processWithRecovery(File file) {
        return processWithRecovery(file, 1);
    }
    
    /**
     * 内部重试处理方法（BufferedImage）
     */
    private CompletableFuture<OcrResult> processWithRecovery(BufferedImage image, int attemptCount) {
        String requestId = "recovery_" + System.currentTimeMillis() + "_" + attemptCount;
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 记录处理开始
                if (attemptCount == 1) {
                    ocrLogger.startProcessing(image, "recovery_image");
                }
                
                ocrLogger.logProcessingStage(requestId, "attempt_" + attemptCount, 
                    "开始第 " + attemptCount + " 次处理尝试");
                
                // 执行OCR处理
                OcrResult result = ocrService.recognizeText(image);
                
                // 处理成功
                ocrLogger.logProcessingComplete(requestId, result);
                return result;
                
            } catch (Exception e) {
                logger.warn("OCR processing failed on attempt {}: {}", attemptCount, e.getMessage());
                
                // 处理错误
                OcrErrorResult errorResult = errorHandler.handleError(requestId, e, image, attemptCount);
                
                if (errorResult.isRetry()) {
                    // 重试处理
                    try {
                        Thread.sleep(errorResult.getRetryDelayMs());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                    
                    // 递归重试
                    return processWithRecovery(image, attemptCount + 1).join();
                    
                } else if (errorResult.isFallback()) {
                    // 返回降级结果
                    logger.info("使用降级处理结果: {}", errorResult.getMessage());
                    return errorResult.getFallbackResult();
                    
                } else if (errorResult.isSkip()) {
                    // 跳过处理，返回空结果
                    logger.info("跳过处理: {}", errorResult.getMessage());
                    return OcrResult.failure("处理被跳过: " + errorResult.getMessage());
                    
                } else {
                    // 失败终止
                    logger.error("OCR处理最终失败: {}", errorResult.getMessage());
                    throw new RuntimeException("OCR处理失败: " + errorResult.getMessage(), e);
                }
            }
        });
    }
    
    /**
     * 内部重试处理方法（File）
     */
    private CompletableFuture<OcrResult> processWithRecovery(File file, int attemptCount) {
        String requestId = "recovery_file_" + System.currentTimeMillis() + "_" + attemptCount;
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 记录处理开始
                if (attemptCount == 1) {
                    ocrLogger.startProcessing(file);
                }
                
                ocrLogger.logProcessingStage(requestId, "attempt_" + attemptCount, 
                    "开始第 " + attemptCount + " 次文件处理尝试");
                
                // 执行OCR处理
                OcrResult result = ocrService.recognizeText(file);
                
                // 处理成功
                ocrLogger.logProcessingComplete(requestId, result);
                return result;
                
            } catch (Exception e) {
                logger.warn("OCR file processing failed on attempt {}: {}", attemptCount, e.getMessage());
                
                // 处理错误
                OcrErrorResult errorResult = errorHandler.handleError(requestId, e, file, attemptCount);
                
                if (errorResult.isRetry()) {
                    // 重试处理
                    try {
                        Thread.sleep(errorResult.getRetryDelayMs());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                    
                    // 递归重试
                    return processWithRecovery(file, attemptCount + 1).join();
                    
                } else if (errorResult.isFallback()) {
                    // 返回降级结果
                    logger.info("使用降级处理结果: {}", errorResult.getMessage());
                    return errorResult.getFallbackResult();
                    
                } else if (errorResult.isSkip()) {
                    // 跳过处理，返回空结果
                    logger.info("跳过处理: {}", errorResult.getMessage());
                    return OcrResult.failure("处理被跳过: " + errorResult.getMessage());
                    
                } else {
                    // 失败终止
                    logger.error("OCR处理最终失败: {}", errorResult.getMessage());
                    throw new RuntimeException("OCR处理失败: " + errorResult.getMessage(), e);
                }
            }
        });
    }
    
    /**
     * 带超时的恢复处理
     */
    public CompletableFuture<OcrResult> processWithRecoveryAndTimeout(BufferedImage image, long timeoutSeconds) {
        CompletableFuture<OcrResult> future = processWithRecovery(image);
        
        return future.orTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .exceptionally(throwable -> {
                    if (throwable instanceof java.util.concurrent.TimeoutException) {
                        logger.warn("OCR processing timed out after {} seconds", timeoutSeconds);
                        return OcrResult.failure("处理超时: " + timeoutSeconds + " 秒");
                    } else {
                        logger.error("OCR processing failed with exception", throwable);
                        return OcrResult.failure("处理异常: " + throwable.getMessage());
                    }
                });
    }
    
    /**
     * 带超时的文件恢复处理
     */
    public CompletableFuture<OcrResult> processWithRecoveryAndTimeout(File file, long timeoutSeconds) {
        CompletableFuture<OcrResult> future = processWithRecovery(file);
        
        return future.orTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .exceptionally(throwable -> {
                    if (throwable instanceof java.util.concurrent.TimeoutException) {
                        logger.warn("OCR file processing timed out after {} seconds", timeoutSeconds);
                        return OcrResult.failure("文件处理超时: " + timeoutSeconds + " 秒");
                    } else {
                        logger.error("OCR file processing failed with exception", throwable);
                        return OcrResult.failure("文件处理异常: " + throwable.getMessage());
                    }
                });
    }
    
    /**
     * 批量处理带恢复机制
     */
    public CompletableFuture<OcrResult[]> batchProcessWithRecovery(BufferedImage[] images) {
        CompletableFuture<OcrResult>[] futures = new CompletableFuture[images.length];
        
        for (int i = 0; i < images.length; i++) {
            futures[i] = processWithRecovery(images[i]);
        }
        
        return CompletableFuture.allOf(futures)
                .thenApply(v -> {
                    OcrResult[] results = new OcrResult[futures.length];
                    for (int i = 0; i < futures.length; i++) {
                        try {
                            results[i] = futures[i].join();
                        } catch (Exception e) {
                            logger.error("批量处理中的图像 {} 失败", i, e);
                            results[i] = OcrResult.failure("批量处理失败: " + e.getMessage());
                        }
                    }
                    return results;
                });
    }
    
    /**
     * 获取错误统计信息
     */
    public String getErrorStatisticsReport() {
        var errorStats = errorHandler.getErrorStatistics();
        
        StringBuilder report = new StringBuilder();
        report.append("OCR错误统计报告:\n");
        report.append("==================\n");
        
        if (errorStats.isEmpty()) {
            report.append("暂无错误记录\n");
        } else {
            errorStats.forEach((errorType, count) -> {
                report.append(String.format("%-20s: %d 次\n", errorType, count));
            });
        }
        
        return report.toString();
    }
    
    /**
     * 重置错误统计
     */
    public void resetErrorStatistics() {
        errorHandler.resetErrorStatistics();
        logger.info("错误统计已重置");
    }
    
    /**
     * 检查系统健康状态
     */
    public boolean isSystemHealthy() {
        var errorStats = errorHandler.getErrorStatistics();
        
        // 计算总错误数
        int totalErrors = errorStats.values().stream().mapToInt(Integer::intValue).sum();
        
        // 如果总错误数超过阈值，认为系统不健康
        return totalErrors < 100; // 可配置的阈值
    }
    
    /**
     * 获取恢复建议
     */
    public String getRecoveryAdvice(String errorType) {
        try {
            OcrErrorHandler.ErrorType type = OcrErrorHandler.ErrorType.valueOf(errorType.toUpperCase());
            return errorHandler.getRecoveryAdvice(type);
        } catch (IllegalArgumentException e) {
            return "未知错误类型，请检查系统日志";
        }
    }
}
