package com.talkweb.ai.converter.recovery;

import com.talkweb.ai.converter.logging.OcrLogger;
import com.talkweb.ai.converter.metrics.OcrMetrics;
import com.talkweb.ai.converter.model.OcrResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;
import java.io.File;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * OCR错误处理器
 * 
 * 提供全面的错误处理和恢复机制，包括：
 * - 错误分类和处理策略
 * - 自动重试机制
 * - 降级处理
 * - 错误统计和分析
 * - 恢复策略
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class OcrErrorHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrErrorHandler.class);
    
    @Autowired
    private OcrLogger ocrLogger;
    
    @Autowired
    private OcrMetrics ocrMetrics;
    
    // 错误统计
    private final Map<String, AtomicInteger> errorCounts = new ConcurrentHashMap<>();
    private final Map<String, LocalDateTime> lastErrorTimes = new ConcurrentHashMap<>();
    
    // 重试配置
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;
    private static final long ERROR_COOLDOWN_MS = 60000; // 1分钟冷却期
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        TESSERACT_ERROR("tesseract_error", "Tesseract OCR引擎错误", true),
        TIMEOUT_ERROR("timeout_error", "处理超时错误", true),
        MEMORY_ERROR("memory_error", "内存不足错误", false),
        IO_ERROR("io_error", "文件IO错误", true),
        IMAGE_FORMAT_ERROR("image_format_error", "图像格式错误", false),
        CONFIGURATION_ERROR("configuration_error", "配置错误", false),
        THREAD_POOL_ERROR("thread_pool_error", "线程池错误", true),
        CACHE_ERROR("cache_error", "缓存错误", true),
        UNKNOWN_ERROR("unknown_error", "未知错误", true);
        
        private final String code;
        private final String description;
        private final boolean retryable;
        
        ErrorType(String code, String description, boolean retryable) {
            this.code = code;
            this.description = description;
            this.retryable = retryable;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
        public boolean isRetryable() { return retryable; }
    }
    
    /**
     * 错误恢复策略枚举
     */
    public enum RecoveryStrategy {
        RETRY("重试处理"),
        FALLBACK("降级处理"),
        SKIP("跳过处理"),
        FAIL("失败终止");
        
        private final String description;
        
        RecoveryStrategy(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    /**
     * 处理OCR错误并返回恢复策略
     */
    public OcrErrorResult handleError(String requestId, Throwable error, BufferedImage image, int attemptCount) {
        ErrorType errorType = classifyError(error);
        
        // 记录错误
        recordError(errorType, error);
        
        // 记录详细错误日志
        Map<String, Object> errorContext = Map.of(
            "errorType", errorType.getCode(),
            "attemptCount", attemptCount,
            "imageSize", image.getWidth() + "x" + image.getHeight(),
            "errorClass", error.getClass().getSimpleName()
        );
        
        if (ocrLogger != null) {
            ocrLogger.logError(requestId, errorType.getCode(), error.getMessage(), error, errorContext);
        }
        
        // 确定恢复策略
        RecoveryStrategy strategy = determineRecoveryStrategy(errorType, attemptCount);
        
        // 执行恢复策略
        return executeRecoveryStrategy(strategy, errorType, error, image, attemptCount);
    }
    
    /**
     * 处理文件OCR错误
     */
    public OcrErrorResult handleError(String requestId, Throwable error, File file, int attemptCount) {
        ErrorType errorType = classifyError(error);
        
        // 记录错误
        recordError(errorType, error);
        
        // 记录详细错误日志
        Map<String, Object> errorContext = Map.of(
            "errorType", errorType.getCode(),
            "attemptCount", attemptCount,
            "fileName", file.getName(),
            "fileSize", file.length(),
            "errorClass", error.getClass().getSimpleName()
        );
        
        if (ocrLogger != null) {
            ocrLogger.logError(requestId, errorType.getCode(), error.getMessage(), error, errorContext);
        }
        
        // 确定恢复策略
        RecoveryStrategy strategy = determineRecoveryStrategy(errorType, attemptCount);
        
        // 执行恢复策略
        return executeRecoveryStrategy(strategy, errorType, error, null, attemptCount);
    }
    
    /**
     * 检查是否应该重试
     */
    public boolean shouldRetry(ErrorType errorType, int attemptCount) {
        if (!errorType.isRetryable()) {
            return false;
        }

        if (attemptCount >= MAX_RETRY_ATTEMPTS) {
            return false;
        }

        return true;
    }
    
    /**
     * 获取重试延迟时间
     */
    public long getRetryDelay(int attemptCount) {
        // 指数退避策略
        return RETRY_DELAY_MS * (long) Math.pow(2, attemptCount - 1);
    }
    
    /**
     * 获取错误统计信息
     */
    public Map<String, Integer> getErrorStatistics() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        errorCounts.forEach((key, value) -> stats.put(key, value.get()));
        return stats;
    }
    
    /**
     * 重置错误统计
     */
    public void resetErrorStatistics() {
        errorCounts.clear();
        lastErrorTimes.clear();
        logger.info("Error statistics reset");
    }
    
    /**
     * 获取错误恢复建议
     */
    public String getRecoveryAdvice(ErrorType errorType) {
        switch (errorType) {
            case TESSERACT_ERROR:
                return "检查Tesseract安装和配置，尝试调整OCR参数";
            case TIMEOUT_ERROR:
                return "增加超时时间或优化图像预处理";
            case MEMORY_ERROR:
                return "增加JVM内存或减少并发处理数量";
            case IO_ERROR:
                return "检查文件权限和磁盘空间";
            case IMAGE_FORMAT_ERROR:
                return "检查图像格式支持或进行格式转换";
            case CONFIGURATION_ERROR:
                return "检查OCR配置参数";
            case THREAD_POOL_ERROR:
                return "检查线程池配置和系统资源";
            case CACHE_ERROR:
                return "清理缓存或检查缓存配置";
            default:
                return "检查系统日志获取更多信息";
        }
    }
    
    // 私有辅助方法
    
    /**
     * 分类错误类型
     */
    private ErrorType classifyError(Throwable error) {
        String errorMessage = error.getMessage();
        String errorClass = error.getClass().getSimpleName();

        // 首先根据异常类型分类
        if (errorClass.contains("Timeout")) {
            return ErrorType.TIMEOUT_ERROR;
        }

        if (errorClass.contains("OutOfMemory")) {
            return ErrorType.MEMORY_ERROR;
        }

        if (errorMessage != null) {
            String lowerMessage = errorMessage.toLowerCase();

            // 优先检查超时错误
            if (lowerMessage.contains("timeout") || lowerMessage.contains("time out") || lowerMessage.contains("timed out")) {
                return ErrorType.TIMEOUT_ERROR;
            }

            if (lowerMessage.contains("tesseract") || lowerMessage.contains("tess4j")) {
                return ErrorType.TESSERACT_ERROR;
            }

            if (lowerMessage.contains("memory") || lowerMessage.contains("heap")) {
                return ErrorType.MEMORY_ERROR;
            }

            if (lowerMessage.contains("image") || lowerMessage.contains("format")) {
                return ErrorType.IMAGE_FORMAT_ERROR;
            }

            if (lowerMessage.contains("config") || lowerMessage.contains("parameter")) {
                return ErrorType.CONFIGURATION_ERROR;
            }

            if (lowerMessage.contains("thread") || lowerMessage.contains("pool")) {
                return ErrorType.THREAD_POOL_ERROR;
            }

            if (lowerMessage.contains("cache")) {
                return ErrorType.CACHE_ERROR;
            }

            // 最后检查IO错误，避免与其他错误冲突
            if (lowerMessage.contains("file") || lowerMessage.contains("io")) {
                return ErrorType.IO_ERROR;
            }
        }

        // 根据异常类型分类
        if (errorClass.contains("IO") || errorClass.contains("File")) {
            return ErrorType.IO_ERROR;
        }

        return ErrorType.UNKNOWN_ERROR;
    }
    
    /**
     * 记录错误
     */
    private void recordError(ErrorType errorType, Throwable error) {
        String errorKey = errorType.getCode();
        
        // 增加错误计数
        errorCounts.computeIfAbsent(errorKey, k -> new AtomicInteger(0)).incrementAndGet();
        
        // 记录最后错误时间
        lastErrorTimes.put(errorKey, LocalDateTime.now());
        
        // 记录到指标系统
        if (ocrMetrics != null) {
            // 这里可以记录到指标系统
        }
        
        logger.warn("OCR error recorded: type={}, message={}", errorType.getCode(), error.getMessage());
    }
    
    /**
     * 确定恢复策略
     */
    private RecoveryStrategy determineRecoveryStrategy(ErrorType errorType, int attemptCount) {
        // 如果可以重试且未超过最大重试次数
        if (shouldRetry(errorType, attemptCount)) {
            return RecoveryStrategy.RETRY;
        }
        
        // 根据错误类型确定策略
        switch (errorType) {
            case MEMORY_ERROR:
            case CONFIGURATION_ERROR:
                return RecoveryStrategy.FAIL;
            
            case IMAGE_FORMAT_ERROR:
                return RecoveryStrategy.FALLBACK;
            
            case CACHE_ERROR:
            case IO_ERROR:
                return RecoveryStrategy.SKIP;
            
            default:
                return RecoveryStrategy.FAIL;
        }
    }
    
    /**
     * 执行恢复策略
     */
    private OcrErrorResult executeRecoveryStrategy(RecoveryStrategy strategy, ErrorType errorType, 
                                                  Throwable error, BufferedImage image, int attemptCount) {
        switch (strategy) {
            case RETRY:
                long delay = getRetryDelay(attemptCount);
                return OcrErrorResult.retry(delay, getRecoveryAdvice(errorType));
            
            case FALLBACK:
                OcrResult fallbackResult = createFallbackResult(errorType, error);
                return OcrErrorResult.fallback(fallbackResult, getRecoveryAdvice(errorType));
            
            case SKIP:
                return OcrErrorResult.skip(getRecoveryAdvice(errorType));
            
            case FAIL:
            default:
                return OcrErrorResult.fail(error.getMessage(), getRecoveryAdvice(errorType));
        }
    }
    
    /**
     * 创建降级结果
     */
    private OcrResult createFallbackResult(ErrorType errorType, Throwable error) {
        String fallbackText = "OCR处理失败: " + errorType.getDescription();
        return OcrResult.failure(fallbackText + " - " + error.getMessage());
    }
}
