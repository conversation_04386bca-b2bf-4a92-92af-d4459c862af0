package com.talkweb.ai.converter.monitoring;

import com.talkweb.ai.converter.logging.OcrLogger;
import com.talkweb.ai.converter.metrics.OcrMetrics;
import com.talkweb.ai.converter.service.OcrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * OCR监控控制器
 * 
 * 提供OCR系统的实时监控API，包括：
 * - 系统状态监控
 * - 性能指标查询
 * - 实时统计数据
 * - 健康检查
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@RestController
@RequestMapping("/api/ocr/monitoring")
@CrossOrigin(origins = "*")
public class OcrMonitoringController {

    @Autowired
    private OcrService ocrService;
    
    @Autowired
    private OcrMetrics ocrMetrics;
    
    @Autowired
    private OcrLogger ocrLogger;

    /**
     * 获取OCR系统总体状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            // 基本状态信息
            status.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            status.put("available", ocrService.isAvailable());
            status.put("activeContexts", ocrLogger.getActiveContextCount());
            
            // 性能指标摘要
            OcrMetrics.MetricsSummary metrics = ocrMetrics.getMetricsSummary();
            status.put("metrics", Map.of(
                "totalProcessed", metrics.getTotalProcessed(),
                "totalSuccessful", metrics.getTotalSuccessful(),
                "totalErrors", metrics.getTotalErrors(),
                "successRate", metrics.getSuccessRate(),
                "errorRate", metrics.getErrorRate(),
                "cacheHitRate", metrics.getCacheHitRate(),
                "avgProcessingTime", metrics.getAvgProcessingTimeMs(),
                "throughput", metrics.getThroughput()
            ));
            
            // 线程池状态
            if (ocrService.isAvailable()) {
                try {
                    var threadPoolStats = ocrService.getThreadPoolStats();
                    status.put("threadPool", Map.of(
                        "activeThreads", threadPoolStats.getActiveThreads(),
                        "queueSize", threadPoolStats.getQueueSize()
                    ));
                } catch (Exception e) {
                    status.put("threadPool", Map.of("error", "Unable to get thread pool stats"));
                }

                try {
                    // 缓存状态
                    var cacheStats = ocrService.getCacheStats();
                    status.put("cache", Map.of(
                        "maxSize", cacheStats.getMaxSize(),
                        "hitRate", cacheStats.getHitRate()
                    ));
                } catch (Exception e) {
                    status.put("cache", Map.of("error", "Unable to get cache stats"));
                }
            }
            
            // 系统健康状态
            String healthStatus = determineHealthStatus(metrics);
            status.put("health", healthStatus);
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            status.put("error", "Failed to retrieve system status: " + e.getMessage());
            status.put("health", "ERROR");
            return ResponseEntity.internalServerError().body(status);
        }
    }

    /**
     * 获取详细的性能指标
     */
    @GetMapping("/metrics")
    public ResponseEntity<OcrMetrics.MetricsSummary> getMetrics() {
        try {
            OcrMetrics.MetricsSummary metrics = ocrMetrics.getMetricsSummary();
            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取实时统计数据
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getRealTimeStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 当前时间戳
            stats.put("timestamp", System.currentTimeMillis());
            
            // 实时指标
            stats.put("successRate", ocrMetrics.getSuccessRate());
            stats.put("errorRate", ocrMetrics.getErrorRate());
            stats.put("cacheHitRate", ocrMetrics.getCacheHitRate());
            stats.put("throughput", ocrMetrics.getThroughput());
            
            // 活跃状态
            stats.put("activeProcessing", ocrLogger.getActiveContextCount());
            stats.put("systemAvailable", ocrService.isAvailable());
            
            if (ocrService.isAvailable()) {
                try {
                    var threadPoolStats = ocrService.getThreadPoolStats();
                    stats.put("activeThreads", threadPoolStats.getActiveThreads());
                    stats.put("queueSize", threadPoolStats.getQueueSize());
                } catch (Exception e) {
                    // 忽略线程池统计错误
                }

                try {
                    var cacheStats = ocrService.getCacheStats();
                    stats.put("cacheUtilization", 0.5); // 默认值
                } catch (Exception e) {
                    // 忽略缓存统计错误
                }
            }
            
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            stats.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(stats);
        }
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            boolean isHealthy = ocrService.isAvailable();
            
            health.put("status", isHealthy ? "UP" : "DOWN");
            health.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            if (isHealthy) {
                // 详细健康检查
                OcrMetrics.MetricsSummary metrics = ocrMetrics.getMetricsSummary();
                
                // 检查错误率
                boolean lowErrorRate = metrics.getErrorRate() < 0.1; // 错误率低于10%
                
                // 检查响应时间
                boolean goodResponseTime = metrics.getAvgProcessingTimeMs() < 10000; // 平均处理时间低于10秒
                
                // 检查线程池状态
                boolean threadPoolHealthy = true;
                try {
                    var threadPoolStats = ocrService.getThreadPoolStats();
                    threadPoolHealthy = threadPoolStats.getActiveThreads() >= 0; // 简单检查
                } catch (Exception e) {
                    threadPoolHealthy = false;
                }
                
                health.put("checks", Map.of(
                    "lowErrorRate", lowErrorRate,
                    "goodResponseTime", goodResponseTime,
                    "threadPoolHealthy", threadPoolHealthy,
                    "serviceAvailable", true
                ));
                
                boolean overallHealthy = lowErrorRate && goodResponseTime && threadPoolHealthy;
                health.put("overall", overallHealthy ? "HEALTHY" : "DEGRADED");
                
                return ResponseEntity.ok(health);
            } else {
                health.put("checks", Map.of("serviceAvailable", false));
                health.put("overall", "UNHEALTHY");
                return ResponseEntity.status(503).body(health);
            }
            
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("overall", "ERROR");
            return ResponseEntity.status(503).body(health);
        }
    }

    /**
     * 获取系统配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfiguration() {
        try {
            if (ocrService.isAvailable()) {
                Map<String, Object> config = ocrService.getConfigInfo();
                return ResponseEntity.ok(config);
            } else {
                return ResponseEntity.status(503).body(Map.of("error", "OCR service not available"));
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 重置性能指标
     */
    @PostMapping("/metrics/reset")
    public ResponseEntity<Map<String, String>> resetMetrics() {
        try {
            ocrService.clearPerformanceMetrics();
            return ResponseEntity.ok(Map.of(
                "message", "Performance metrics reset successfully",
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 清空缓存
     */
    @PostMapping("/cache/clear")
    public ResponseEntity<Map<String, String>> clearCache() {
        try {
            ocrService.clearCache();
            return ResponseEntity.ok(Map.of(
                "message", "Cache cleared successfully",
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 获取性能报告
     */
    @GetMapping("/report")
    public ResponseEntity<Map<String, Object>> getPerformanceReport() {
        try {
            String report = ocrService.generatePerformanceReport();
            
            Map<String, Object> response = new HashMap<>();
            response.put("report", report);
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            response.put("metrics", ocrMetrics.getMetricsSummary());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    // 私有辅助方法

    /**
     * 确定系统健康状态
     */
    private String determineHealthStatus(OcrMetrics.MetricsSummary metrics) {
        if (!ocrService.isAvailable()) {
            return "DOWN";
        }
        
        // 检查错误率
        if (metrics.getErrorRate() > 0.2) { // 错误率超过20%
            return "CRITICAL";
        }
        
        if (metrics.getErrorRate() > 0.1) { // 错误率超过10%
            return "WARNING";
        }
        
        // 检查平均处理时间
        if (metrics.getAvgProcessingTimeMs() > 15000) { // 超过15秒
            return "WARNING";
        }
        
        // 检查缓存命中率
        if (metrics.getCacheHitRate() < 0.3) { // 缓存命中率低于30%
            return "WARNING";
        }
        
        return "HEALTHY";
    }
}
