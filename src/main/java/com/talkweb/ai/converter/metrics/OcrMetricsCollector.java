package com.talkweb.ai.converter.metrics;

import com.talkweb.ai.converter.service.OcrCacheManager;
import com.talkweb.ai.converter.service.OcrService;
import com.talkweb.ai.converter.service.OcrThreadPoolManager;
import com.talkweb.ai.converter.util.PerformanceMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * OCR指标收集器
 * 
 * 定期收集OCR系统的各种运行指标，包括：
 * - 服务状态监控
 * - 性能指标收集
 * - 资源使用情况统计
 * - 健康状态检查
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class OcrMetricsCollector {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrMetricsCollector.class);
    
    private final OcrMetrics ocrMetrics;
    private final OcrService ocrService;
    private final OcrThreadPoolManager threadPoolManager;
    private final OcrCacheManager cacheManager;
    private final PerformanceMonitor performanceMonitor;
    
    private final AtomicBoolean collecting = new AtomicBoolean(false);
    private LocalDateTime lastCollectionTime;
    
    public OcrMetricsCollector(OcrMetrics ocrMetrics,
                              OcrService ocrService,
                              OcrThreadPoolManager threadPoolManager,
                              OcrCacheManager cacheManager,
                              PerformanceMonitor performanceMonitor) {
        this.ocrMetrics = ocrMetrics;
        this.ocrService = ocrService;
        this.threadPoolManager = threadPoolManager;
        this.cacheManager = cacheManager;
        this.performanceMonitor = performanceMonitor;
    }
    
    @PostConstruct
    public void init() {
        collecting.set(true);
        logger.info("OCR metrics collector initialized and started");
    }
    
    @PreDestroy
    public void shutdown() {
        collecting.set(false);
        logger.info("OCR metrics collector stopped");
    }
    
    /**
     * 每30秒收集一次基本指标
     */
    @Scheduled(fixedRate = 30000)
    public void collectBasicMetrics() {
        if (!collecting.get()) {
            return;
        }
        
        try {
            // 收集线程池指标
            collectThreadPoolMetrics();
            
            // 收集缓存指标
            collectCacheMetrics();
            
            // 更新最后收集时间
            lastCollectionTime = LocalDateTime.now();
            
            logger.debug("Basic metrics collected at {}", 
                        lastCollectionTime.format(DateTimeFormatter.ISO_LOCAL_TIME));
            
        } catch (Exception e) {
            logger.error("Error collecting basic metrics", e);
        }
    }
    
    /**
     * 每5分钟收集一次详细指标
     */
    @Scheduled(fixedRate = 300000)
    public void collectDetailedMetrics() {
        if (!collecting.get()) {
            return;
        }
        
        try {
            // 收集性能指标
            collectPerformanceMetrics();
            
            // 生成指标报告
            generateMetricsReport();
            
            logger.debug("Detailed metrics collected and reported");
            
        } catch (Exception e) {
            logger.error("Error collecting detailed metrics", e);
        }
    }
    
    /**
     * 每小时进行一次健康检查
     */
    @Scheduled(fixedRate = 3600000)
    public void performHealthCheck() {
        if (!collecting.get()) {
            return;
        }
        
        try {
            HealthCheckResult healthCheck = performSystemHealthCheck();
            
            if (healthCheck.isHealthy()) {
                logger.info("OCR system health check passed: {}", healthCheck.getSummary());
            } else {
                logger.warn("OCR system health check failed: {}", healthCheck.getSummary());
                // 这里可以触发告警
            }
            
        } catch (Exception e) {
            logger.error("Error performing health check", e);
        }
    }
    
    /**
     * 收集线程池指标
     */
    private void collectThreadPoolMetrics() {
        if (threadPoolManager != null) {
            OcrThreadPoolManager.ThreadPoolStats stats = threadPoolManager.getStats();
            if (stats != null) {
                ocrMetrics.updateThreadPoolStats(stats.getActiveThreads(), stats.getQueueSize());
                
                logger.debug("Thread pool metrics: active={}, queue={}, completed={}", 
                           stats.getActiveThreads(), stats.getQueueSize(), stats.getTotalTasksCompleted());
            }
        }
    }
    
    /**
     * 收集缓存指标
     */
    private void collectCacheMetrics() {
        if (cacheManager != null) {
            OcrCacheManager.CacheStats stats = cacheManager.getStats();
            if (stats != null) {
                // 更新缓存命中率指标
                long totalRequests = stats.getHits() + stats.getMisses();
                if (totalRequests > 0) {
                    double hitRate = (double) stats.getHits() / totalRequests;
                    logger.debug("Cache metrics: hits={}, misses={}, hitRate={:.2f}%, size={}", 
                               stats.getHits(), stats.getMisses(), hitRate * 100, stats.getCurrentSize());
                }
            }
        }
    }
    
    /**
     * 收集性能指标
     */
    private void collectPerformanceMetrics() {
        if (performanceMonitor != null) {
            var allMetrics = performanceMonitor.getAllMetrics();
            
            for (var entry : allMetrics.entrySet()) {
                String operationName = entry.getKey();
                PerformanceMonitor.PerformanceMetric metric = entry.getValue();
                
                logger.debug("Performance metric [{}]: count={}, avgTime={:.2f}ms, throughput={:.2f}ops/s",
                           operationName, metric.getCount(), metric.getAverageTime(), metric.getThroughput());
            }
        }
    }
    
    /**
     * 生成指标报告
     */
    private void generateMetricsReport() {
        OcrMetrics.MetricsSummary summary = ocrMetrics.getMetricsSummary();
        
        logger.info("=== OCR Metrics Report ===");
        logger.info("Total Processed: {}", summary.getTotalProcessed());
        logger.info("Success Rate: {:.2f}%", summary.getSuccessRate() * 100);
        logger.info("Error Rate: {:.2f}%", summary.getErrorRate() * 100);
        logger.info("Cache Hit Rate: {:.2f}%", summary.getCacheHitRate() * 100);
        logger.info("Throughput: {:.2f} ops/sec", summary.getThroughput());
        logger.info("Avg Processing Time: {:.2f} ms", summary.getAvgProcessingTimeMs());
        logger.info("Active Threads: {}", summary.getActiveThreads());
        logger.info("Queue Size: {}", summary.getQueueSize());
        logger.info("========================");
    }
    
    /**
     * 执行系统健康检查
     */
    private HealthCheckResult performSystemHealthCheck() {
        HealthCheckResult.Builder builder = new HealthCheckResult.Builder();
        
        // 检查OCR服务可用性
        if (ocrService != null && ocrService.isAvailable()) {
            builder.addCheck("ocr_service", true, "OCR service is available");
        } else {
            builder.addCheck("ocr_service", false, "OCR service is not available");
        }
        
        // 检查线程池状态
        if (threadPoolManager != null) {
            OcrThreadPoolManager.ThreadPoolStats stats = threadPoolManager.getStats();
            if (stats != null) {
                boolean healthy = stats.getActiveThreads() < stats.getMaxPoolSize() * 0.9;
                builder.addCheck("thread_pool", healthy, 
                    String.format("Thread pool usage: %d/%d", stats.getActiveThreads(), stats.getMaxPoolSize()));
            }
        }
        
        // 检查缓存状态
        if (cacheManager != null) {
            OcrCacheManager.CacheStats stats = cacheManager.getStats();
            if (stats != null) {
                boolean healthy = stats.getCurrentSize() < stats.getMaxSize() * 0.9;
                builder.addCheck("cache", healthy,
                    String.format("Cache usage: %d/%d", stats.getCurrentSize(), stats.getMaxSize()));
            }
        }
        
        // 检查错误率
        double errorRate = ocrMetrics.getErrorRate();
        boolean errorRateHealthy = errorRate < 0.1; // 错误率低于10%
        builder.addCheck("error_rate", errorRateHealthy,
            String.format("Error rate: %.2f%%", errorRate * 100));
        
        // 检查内存使用
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        double memoryUsagePercent = (double) usedMemory / totalMemory;
        boolean memoryHealthy = memoryUsagePercent < 0.8; // 内存使用率低于80%
        builder.addCheck("memory", memoryHealthy,
            String.format("Memory usage: %.1f%%", memoryUsagePercent * 100));
        
        return builder.build();
    }
    
    /**
     * 获取收集器状态
     */
    public CollectorStatus getStatus() {
        return new CollectorStatus(
            collecting.get(),
            lastCollectionTime,
            ocrMetrics.getMetricsSummary()
        );
    }
    
    /**
     * 手动触发指标收集
     */
    public void triggerCollection() {
        logger.info("Manual metrics collection triggered");
        collectBasicMetrics();
        collectDetailedMetrics();
    }
    
    /**
     * 健康检查结果类
     */
    public static class HealthCheckResult {
        private final boolean healthy;
        private final String summary;
        private final java.util.Map<String, CheckResult> checks;
        
        private HealthCheckResult(boolean healthy, String summary, java.util.Map<String, CheckResult> checks) {
            this.healthy = healthy;
            this.summary = summary;
            this.checks = checks;
        }
        
        public boolean isHealthy() { return healthy; }
        public String getSummary() { return summary; }
        public java.util.Map<String, CheckResult> getChecks() { return checks; }
        
        public static class Builder {
            private final java.util.Map<String, CheckResult> checks = new java.util.HashMap<>();
            
            public Builder addCheck(String name, boolean passed, String message) {
                checks.put(name, new CheckResult(passed, message));
                return this;
            }
            
            public HealthCheckResult build() {
                boolean allHealthy = checks.values().stream().allMatch(CheckResult::isPassed);
                long passedCount = checks.values().stream().mapToLong(c -> c.isPassed() ? 1 : 0).sum();
                String summary = String.format("%d/%d checks passed", passedCount, checks.size());
                
                return new HealthCheckResult(allHealthy, summary, new java.util.HashMap<>(checks));
            }
        }
        
        public static class CheckResult {
            private final boolean passed;
            private final String message;
            
            public CheckResult(boolean passed, String message) {
                this.passed = passed;
                this.message = message;
            }
            
            public boolean isPassed() { return passed; }
            public String getMessage() { return message; }
        }
    }
    
    /**
     * 收集器状态类
     */
    public static class CollectorStatus {
        private final boolean collecting;
        private final LocalDateTime lastCollectionTime;
        private final OcrMetrics.MetricsSummary metricsSummary;
        
        public CollectorStatus(boolean collecting, LocalDateTime lastCollectionTime, 
                             OcrMetrics.MetricsSummary metricsSummary) {
            this.collecting = collecting;
            this.lastCollectionTime = lastCollectionTime;
            this.metricsSummary = metricsSummary;
        }
        
        public boolean isCollecting() { return collecting; }
        public LocalDateTime getLastCollectionTime() { return lastCollectionTime; }
        public OcrMetrics.MetricsSummary getMetricsSummary() { return metricsSummary; }
    }
}
