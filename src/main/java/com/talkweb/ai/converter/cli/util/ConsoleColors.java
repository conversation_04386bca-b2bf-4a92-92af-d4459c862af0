package com.talkweb.ai.converter.cli.util;

/**
 * 控制台颜色工具类
 */
public class ConsoleColors {
    // 重置
    public static final String RESET = "\033[0m";

    // 常规颜色
    public static final String BLACK = "\033[0;30m";
    public static final String RED = "\033[0;31m";
    public static final String GREEN = "\033[0;32m";
    public static final String YELLOW = "\033[0;33m";
    public static final String BLUE = "\033[0;34m";
    public static final String PURPLE = "\033[0;35m";
    public static final String CYAN = "\033[0;36m";
    public static final String WHITE = "\033[0;37m";

    // 粗体
    public static final String BLACK_BOLD = "\033[1;30m";
    public static final String RED_BOLD = "\033[1;31m";
    public static final String GREEN_BOLD = "\033[1;32m";
    public static final String YELLOW_BOLD = "\033[1;33m";
    public static final String BLUE_BOLD = "\033[1;34m";
    public static final String PURPLE_BOLD = "\033[1;35m";
    public static final String CYAN_BOLD = "\033[1;36m";
    public static final String WHITE_BOLD = "\033[1;37m";

    // 下划线
    public static final String BLACK_UNDERLINED = "\033[4;30m";
    public static final String RED_UNDERLINED = "\033[4;31m";
    public static final String GREEN_UNDERLINED = "\033[4;32m";
    public static final String YELLOW_UNDERLINED = "\033[4;33m";
    public static final String BLUE_UNDERLINED = "\033[4;34m";
    public static final String PURPLE_UNDERLINED = "\033[4;35m";
    public static final String CYAN_UNDERLINED = "\033[4;36m";
    public static final String WHITE_UNDERLINED = "\033[4;37m";

    // 背景色
    public static final String BLACK_BACKGROUND = "\033[40m";
    public static final String RED_BACKGROUND = "\033[41m";
    public static final String GREEN_BACKGROUND = "\033[42m";
    public static final String YELLOW_BACKGROUND = "\033[43m";
    public static final String BLUE_BACKGROUND = "\033[44m";
    public static final String PURPLE_BACKGROUND = "\033[45m";
    public static final String CYAN_BACKGROUND = "\033[46m";
    public static final String WHITE_BACKGROUND = "\033[47m";

    /**
     * 是否启用彩色输出
     */
    private static boolean enabled = true;

    /**
     * 启用彩色输出
     */
    public static void enable() {
        enabled = true;
    }

    /**
     * 禁用彩色输出
     */
    public static void disable() {
        enabled = false;
    }

    /**
     * 使用指定颜色包装文本
     * @param text 文本
     * @param color 颜色代码
     * @return 包装后的文本
     */
    public static String wrap(String text, String color) {
        if (!enabled) {
            return text;
        }
        return color + text + RESET;
    }

    /**
     * 输出成功信息
     * @param text 文本
     * @return 格式化后的文本
     */
    public static String success(String text) {
        return wrap(text, GREEN_BOLD);
    }

    /**
     * 输出错误信息
     * @param text 文本
     * @return 格式化后的文本
     */
    public static String error(String text) {
        return wrap(text, RED_BOLD);
    }

    /**
     * 输出警告信息
     * @param text 文本
     * @return 格式化后的文本
     */
    public static String warning(String text) {
        return wrap(text, YELLOW_BOLD);
    }

    /**
     * 输出信息
     * @param text 文本
     * @return 格式化后的文本
     */
    public static String info(String text) {
        return wrap(text, CYAN);
    }
}