package com.talkweb.ai.converter.cli;

import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginManager;
import org.springframework.stereotype.Component;
import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.ParentCommand;
import picocli.CommandLine.Parameters;

import java.nio.file.Path;
import java.util.concurrent.Callable;

@Component
@Command(
    name = "plugin",
    description = "Manage document converter plugins",
    mixinStandardHelpOptions = true,
    subcommands = {
        PluginListCommand.class,
        PluginInfoCommand.class,
        PluginInstallCommand.class,
        PluginUninstallCommand.class
    }
)
public class PluginCommand implements Callable<Integer> {

    @ParentCommand
    private DocConverterCommand parent;

    @Override
    public Integer call() {
        // Default behavior when no subcommand is provided
        CommandLine.usage(this, System.out);
        return 0;
    }

    public Path getPluginsDir() {
        return parent.getPluginsDir();
    }
}

@Component
@Command(
    name = "list",
    description = "List all installed plugins"
)
class PluginListCommand implements Callable<Integer> {

    @ParentCommand
    private PluginCommand parent;

    private final PluginManager pluginManager;

    public PluginListCommand(PluginManager pluginManager) {
        this.pluginManager = pluginManager;
    }

    @Option(
        names = {"-v", "--verbose"},
        description = "Show detailed plugin information"
    )
    private boolean verbose;

    @Override
    public Integer call() {
        System.out.println("Installed plugins:");
        for (Plugin plugin : pluginManager.getPlugins()) {
            System.out.printf("  - %s (%s)%n", plugin.getMetadata().getId(), plugin.getMetadata().getVersion());
            if (verbose) {
                System.out.printf("    Description: %s%n", plugin.getMetadata().getDescription());
                System.out.printf("    Status: %s%n", pluginManager.getPluginState(plugin.getMetadata().getId()));
            }
        }

        if (verbose) {
            System.out.println("\nPlugin directory: " + parent.getPluginsDir().toAbsolutePath());
        }

        return 0;
    }
}

@Component
@Command(
    name = "info",
    description = "Show information about a specific plugin"
)
class PluginInfoCommand implements Callable<Integer> {

    @ParentCommand
    private PluginCommand parent;

    private final PluginManager pluginManager;

    public PluginInfoCommand(PluginManager pluginManager) {
        this.pluginManager = pluginManager;
    }

    @Parameters(
        index = "0",
        description = "Plugin ID"
    )
    private String pluginId;

    @Override
    public Integer call() {
        return pluginManager.getPlugin(pluginId).map(plugin -> {
            System.out.println("Plugin: " + plugin.getMetadata().getId());
            System.out.println("  Version: " + plugin.getMetadata().getVersion());
            System.out.println("  Description: " + plugin.getMetadata().getDescription());
           // System.out.println("  Author: " + plugin.getMetadata().getAuthor());
            System.out.println("  Status: " + pluginManager.getPluginState(pluginId));
            return 0;
        }).orElseGet(() -> {
            System.err.println("Plugin not found: " + pluginId);
            return 1;
        });
    }
}

@Component
@Command(
    name = "install",
    description = "Install a new plugin"
)
class PluginInstallCommand implements Callable<Integer> {

    @ParentCommand
    private PluginCommand parent;

    private final PluginManager pluginManager;

    public PluginInstallCommand(PluginManager pluginManager) {
        this.pluginManager = pluginManager;
    }

    @Parameters(
        index = "0",
        description = "Plugin JAR file or URL"
    )
    private String source;

    @Option(
        names = {"-f", "--force"},
        description = "Force installation even if the plugin already exists"
    )
    private boolean force;

    @Override
    public Integer call() {
        System.out.println("Installing plugin from: " + source);
        try {
            Path sourcePath = Path.of(source);
            pluginManager.installPlugin(sourcePath, force);
            System.out.println("Plugin installed successfully!");
            System.out.println("If the application is running, the plugin should be active now.");
            return 0;
        } catch (Exception e) {
            System.err.println("Failed to install plugin: " + e.getMessage());
            return 1;
        }
    }
}

@Component
@Command(
    name = "uninstall",
    description = "Uninstall a plugin"
)
class PluginUninstallCommand implements Callable<Integer> {

    @ParentCommand
    private PluginCommand parent;

    private final PluginManager pluginManager;

    public PluginUninstallCommand(PluginManager pluginManager) {
        this.pluginManager = pluginManager;
    }

    @Parameters(
        index = "0",
        description = "Plugin ID"
    )
    private String pluginId;

    @Option(
        names = {"-f", "--force"},
        description = "Force uninstallation without confirmation"
    )
    private boolean force;

    @Override
    public Integer call() {
        System.out.println("Uninstalling plugin: " + pluginId);
        try {
            if (pluginManager.uninstallPlugin(pluginId, force)) {
                System.out.println("Plugin uninstalled successfully!");
                System.out.println("If the application is running, the plugin has been deactivated.");
                return 0;
            } else {
                System.err.println("Plugin not found or could not be uninstalled: " + pluginId);
                return 1;
            }
        } catch (Exception e) {
            System.err.println("Failed to uninstall plugin: " + e.getMessage());
            return 1;
        }
    }
}
