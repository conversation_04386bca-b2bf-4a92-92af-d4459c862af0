package com.talkweb.ai.converter.cli;

import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;
import picocli.CommandLine.ScopeType;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.Callable;

import org.springframework.stereotype.Component;

@Command(
    name = "doc-converter",
    description = "Document to Markdown Converter",
    mixinStandardHelpOptions = true,
    version = "1.0.0",
    subcommands = {
        ConvertCommand.class,
        PluginCommand.class,
        ServerCommand.class,
        CommandLine.HelpCommand.class
    }
)
@Component
public class DocConverterCommand implements Callable<Integer> {

    @Option(
        names = {"-c", "--config"},
        description = "Path to configuration file",
        scope = ScopeType.INHERIT
    )
    private Path configFile = Paths.get("config", "application.yaml");

    @Option(
        names = {"-v", "--verbose"},
        description = "Verbose mode"
    )
    private boolean verbose;

    @Option(
        names = {"--plugins-dir"},
        description = "Directory containing plugins",
        scope = ScopeType.INHERIT
    )
    private Path pluginsDir = Paths.get("plugins");

    @Option(
        names = {"--temp-dir"},
        description = "Directory for temporary files",
        scope = ScopeType.INHERIT
    )
    private Path tempDir = Paths.get("temp");

    @Override
    public Integer call() {
        // Default behavior when no subcommand is provided
        CommandLine.usage(this, System.out);
        return 0;
    }

    // Getters for subcommands to access common options
    public Path getConfigFile() {
        return configFile;
    }

    public boolean isVerbose() {
        return verbose;
    }

    public Path getPluginsDir() {
        return pluginsDir;
    }

    public Path getTempDir() {
        return tempDir;
    }
}
