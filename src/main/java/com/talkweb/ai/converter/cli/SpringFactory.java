package com.talkweb.ai.converter.cli;

import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import picocli.CommandLine;

@Component
public class SpringFactory implements CommandLine.IFactory {

    private final ApplicationContext applicationContext;

    public SpringFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public <K> K create(Class<K> cls) throws Exception {
        try {
            return applicationContext.getBean(cls);
        } catch (Exception e) {
            // If not a Spring bean, try to instantiate it directly
            return cls.getDeclaredConstructor().newInstance();
        }
    }
}
