package com.talkweb.ai.converter.service;

import com.talkweb.ai.converter.config.OcrConfiguration;
import com.talkweb.ai.converter.model.OcrResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.awt.image.BufferedImage;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * OCR缓存管理器
 * 
 * 提供高级的OCR结果缓存功能，包括智能缓存键生成、LRU淘汰策略、
 * 内存监控和自动清理等特性。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class OcrCacheManager {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrCacheManager.class);
    
    private final OcrConfiguration ocrConfig;
    
    // 缓存存储
    private final ConcurrentHashMap<String, CacheEntry> cache = new ConcurrentHashMap<>();
    private final ConcurrentLinkedQueue<String> accessOrder = new ConcurrentLinkedQueue<>();
    
    // 统计信息
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong cacheEvictions = new AtomicLong(0);
    
    // 清理任务
    private ScheduledExecutorService cleanupExecutor;
    
    // 缓存配置
    private static final long DEFAULT_TTL_MS = 60 * 60 * 1000; // 1小时
    private static final int CLEANUP_INTERVAL_MINUTES = 10;
    private static final double MEMORY_PRESSURE_THRESHOLD = 0.8;
    
    public OcrCacheManager(OcrConfiguration ocrConfig) {
        this.ocrConfig = ocrConfig;
    }
    
    @PostConstruct
    public void initialize() {
        if (!ocrConfig.isImageCacheEnabled()) {
            logger.info("OCR cache is disabled");
            return;
        }
        
        logger.info("Initializing OCR Cache Manager");
        logger.info("Cache size limit: {}, TTL: {}ms", ocrConfig.getCacheSize(), DEFAULT_TTL_MS);
        
        // 启动清理任务
        cleanupExecutor = Executors.newScheduledThreadPool(1, r -> {
            Thread t = new Thread(r, "OCR-Cache-Cleanup");
            t.setDaemon(true);
            return t;
        });
        
        cleanupExecutor.scheduleAtFixedRate(
            this::performCleanup, 
            CLEANUP_INTERVAL_MINUTES, 
            CLEANUP_INTERVAL_MINUTES, 
            TimeUnit.MINUTES
        );
        
        logger.info("OCR Cache Manager initialized successfully");
    }
    
    @PreDestroy
    public void shutdown() {
        if (cleanupExecutor != null) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        cache.clear();
        logger.info("OCR Cache Manager shutdown completed");
    }
    
    /**
     * 获取缓存的OCR结果
     */
    public OcrResult get(String cacheKey) {
        if (!ocrConfig.isImageCacheEnabled()) {
            return null;
        }
        
        CacheEntry entry = cache.get(cacheKey);
        if (entry == null) {
            cacheMisses.incrementAndGet();
            return null;
        }
        
        if (entry.isExpired()) {
            cache.remove(cacheKey);
            cacheMisses.incrementAndGet();
            return null;
        }
        
        // 更新访问顺序
        updateAccessOrder(cacheKey);
        cacheHits.incrementAndGet();
        
        logger.debug("Cache hit for key: {}", cacheKey);
        return entry.getResult();
    }
    
    /**
     * 缓存OCR结果
     */
    public void put(String cacheKey, OcrResult result) {
        if (!ocrConfig.isImageCacheEnabled() || result == null) {
            return;
        }
        
        // 检查内存压力
        if (isMemoryPressureHigh()) {
            logger.debug("High memory pressure, performing aggressive cleanup");
            performAggressiveCleanup();
        }
        
        // 检查缓存大小限制
        if (cache.size() >= ocrConfig.getCacheSize()) {
            evictLeastRecentlyUsed();
        }
        
        CacheEntry entry = new CacheEntry(result, System.currentTimeMillis() + DEFAULT_TTL_MS);
        cache.put(cacheKey, entry);
        updateAccessOrder(cacheKey);
        
        logger.debug("Cached OCR result for key: {}", cacheKey);
    }
    
    /**
     * 生成智能缓存键
     */
    public String generateCacheKey(BufferedImage image, OcrConfiguration config) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            
            // 图像特征
            md.update(intToBytes(image.getWidth()));
            md.update(intToBytes(image.getHeight()));
            md.update(intToBytes(image.getType()));
            
            // 图像内容哈希（采样）
            String imageHash = calculateImageHash(image);
            md.update(imageHash.getBytes());
            
            // OCR配置
            md.update(config.getLanguageString().getBytes());
            md.update(intToBytes(config.getPageSegmentationMode()));
            md.update(intToBytes(config.getOcrEngineMode()));
            md.update(intToBytes(config.getConfidenceThreshold()));
            
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            
            return "ocr_" + sb.toString();
            
        } catch (NoSuchAlgorithmException e) {
            logger.warn("MD5 algorithm not available, using simple cache key", e);
            return generateSimpleCacheKey(image, config);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getStats() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        long total = hits + misses;
        double hitRate = total > 0 ? (double) hits / total : 0.0;
        
        return new CacheStats(
            cache.size(),
            ocrConfig.getCacheSize(),
            hits,
            misses,
            hitRate,
            cacheEvictions.get(),
            calculateMemoryUsage()
        );
    }
    
    /**
     * 清空缓存
     */
    public void clear() {
        cache.clear();
        accessOrder.clear();
        logger.info("OCR cache cleared");
    }
    
    // 私有方法
    
    private void updateAccessOrder(String key) {
        // 简化的LRU实现：移除旧的访问记录，添加新的
        accessOrder.remove(key);
        accessOrder.offer(key);
    }
    
    private void evictLeastRecentlyUsed() {
        String lruKey = accessOrder.poll();
        if (lruKey != null && cache.remove(lruKey) != null) {
            cacheEvictions.incrementAndGet();
            logger.debug("Evicted LRU cache entry: {}", lruKey);
        }
    }
    
    private void performCleanup() {
        logger.debug("Performing scheduled cache cleanup");
        
        int beforeSize = cache.size();
        long currentTime = System.currentTimeMillis();
        
        // 清理过期条目
        cache.entrySet().removeIf(entry -> {
            if (entry.getValue().isExpired(currentTime)) {
                accessOrder.remove(entry.getKey());
                return true;
            }
            return false;
        });
        
        int removedCount = beforeSize - cache.size();
        if (removedCount > 0) {
            logger.debug("Cleaned up {} expired cache entries", removedCount);
        }
        
        // 记录统计信息
        logCacheStats();
    }
    
    private void performAggressiveCleanup() {
        // 在内存压力高时，清理一半的缓存
        int targetSize = cache.size() / 2;
        while (cache.size() > targetSize) {
            evictLeastRecentlyUsed();
        }
        logger.debug("Performed aggressive cleanup, cache size reduced to: {}", cache.size());
    }
    
    private boolean isMemoryPressureHigh() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        double memoryUsage = (double) (totalMemory - freeMemory) / totalMemory;
        
        return memoryUsage > MEMORY_PRESSURE_THRESHOLD;
    }
    
    private String calculateImageHash(BufferedImage image) {
        // 简化的图像哈希：采样关键像素点
        int width = image.getWidth();
        int height = image.getHeight();
        int sampleSize = Math.min(100, width * height / 100); // 采样1%的像素
        
        StringBuilder hash = new StringBuilder();
        for (int i = 0; i < sampleSize; i++) {
            int x = (i * width) / sampleSize;
            int y = (i * height) / sampleSize;
            if (x < width && y < height) {
                hash.append(Integer.toHexString(image.getRGB(x, y)));
            }
        }
        
        return hash.toString();
    }
    
    private String generateSimpleCacheKey(BufferedImage image, OcrConfiguration config) {
        return String.format("ocr_%dx%d_%s_%d_%d_%d",
            image.getWidth(), image.getHeight(),
            config.getLanguageString(),
            config.getPageSegmentationMode(),
            config.getOcrEngineMode(),
            config.getConfidenceThreshold());
    }
    
    private byte[] intToBytes(int value) {
        return new byte[] {
            (byte) (value >>> 24),
            (byte) (value >>> 16),
            (byte) (value >>> 8),
            (byte) value
        };
    }
    
    private long calculateMemoryUsage() {
        // 估算缓存内存使用量
        return cache.size() * 1024; // 简化估算，每个条目约1KB
    }
    
    private void logCacheStats() {
        CacheStats stats = getStats();
        logger.debug("Cache stats: {}", stats);
    }
    
    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final OcrResult result;
        private final long expirationTime;
        
        public CacheEntry(OcrResult result, long expirationTime) {
            this.result = result;
            this.expirationTime = expirationTime;
        }
        
        public OcrResult getResult() {
            return result;
        }
        
        public boolean isExpired() {
            return isExpired(System.currentTimeMillis());
        }
        
        public boolean isExpired(long currentTime) {
            return currentTime > expirationTime;
        }
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private final int currentSize;
        private final int maxSize;
        private final long hits;
        private final long misses;
        private final double hitRate;
        private final long evictions;
        private final long memoryUsage;
        
        public CacheStats(int currentSize, int maxSize, long hits, long misses, 
                         double hitRate, long evictions, long memoryUsage) {
            this.currentSize = currentSize;
            this.maxSize = maxSize;
            this.hits = hits;
            this.misses = misses;
            this.hitRate = hitRate;
            this.evictions = evictions;
            this.memoryUsage = memoryUsage;
        }
        
        // Getters
        public int getCurrentSize() { return currentSize; }
        public int getMaxSize() { return maxSize; }
        public long getHits() { return hits; }
        public long getMisses() { return misses; }
        public double getHitRate() { return hitRate; }
        public long getEvictions() { return evictions; }
        public long getMemoryUsage() { return memoryUsage; }
        
        @Override
        public String toString() {
            return String.format("CacheStats{size=%d/%d, hits=%d, misses=%d, hitRate=%.2f%%, evictions=%d, memory=%dKB}",
                               currentSize, maxSize, hits, misses, hitRate * 100, evictions, memoryUsage / 1024);
        }
    }
}
