package com.talkweb.ai.converter.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * Service for generating AI-powered document summaries
 *
 * Real implementation using Spring AI OpenAiChatModel and OpenAI integration.
 *
 * <AUTHOR> Assistant
 * @version 2.0
 */
@Service
@ConditionalOnProperty(name = "ai.enabled", havingValue = "true", matchIfMissing = false)
public class DocumentSummaryService {

    private static final Logger logger = LoggerFactory.getLogger(DocumentSummaryService.class);
    private static final int MAX_CHUNK_SIZE = 8000; // Maximum characters per chunk for AI processing

    private final ChatModel chatModel;

    public DocumentSummaryService(ChatModel chatModel) {
        this.chatModel = chatModel;
        logger.info("DocumentSummaryService initialized with real AI integration");
    }

    /**
     * Generates a summary of the given document content using OpenAI
     *
     * @param content the document content to summarize
     * @param maxLength maximum length of the summary
     * @return the generated summary
     */
    public String generateSummary(String content, int maxLength) {
        if (content == null || content.trim().isEmpty()) {
            return "No content to summarize.";
        }

        try {
            logger.debug("Generating AI summary for content of length: {}", content.length());

            // Split content into chunks if too large
            if (content.length() > MAX_CHUNK_SIZE) {
                return generateSummaryForLargeContent(content, maxLength);
            }

            // Create prompt for summary generation
            String prompt = String.format(
                "Please provide a concise summary of the following document content. " +
                "The summary should be approximately %d characters or less and capture the main points:\n\n%s",
                maxLength, content
            );

            // Call OpenAI API
            String summary = chatModel.call(prompt);

            // Ensure summary doesn't exceed maxLength
            if (summary.length() > maxLength) {
                summary = summary.substring(0, maxLength - 3) + "...";
            }

            logger.debug("Generated AI summary of length: {}", summary.length());
            return summary;

        } catch (Exception e) {
            logger.error("Failed to generate AI summary", e);
            return "Failed to generate summary: " + e.getMessage();
        }
    }

    /**
     * Generates summary for large content by splitting into chunks
     */
    private String generateSummaryForLargeContent(String content, int maxLength) {
        try {
            // Split content into chunks
            String[] chunks = splitIntoChunks(content);
            StringBuilder combinedSummary = new StringBuilder();

            // Generate summary for each chunk
            for (String chunk : chunks) {
                String prompt = String.format(
                    "Please provide a brief summary of this document section (max 200 characters):\n\n%s",
                    chunk
                );
                String chunkSummary = chatModel.call(prompt);
                combinedSummary.append(chunkSummary).append(" ");
            }

            // If combined summary is still too long, summarize the summaries
            String finalSummary = combinedSummary.toString().trim();
            if (finalSummary.length() > maxLength) {
                String prompt = String.format(
                    "Please create a final summary from these section summaries (max %d characters):\n\n%s",
                    maxLength, finalSummary
                );
                finalSummary = chatModel.call(prompt);
            }

            // Ensure final summary doesn't exceed maxLength
            if (finalSummary.length() > maxLength) {
                finalSummary = finalSummary.substring(0, maxLength - 3) + "...";
            }

            return finalSummary;
        } catch (Exception e) {
            logger.error("Failed to generate summary for large content", e);
            return "Failed to generate summary for large content: " + e.getMessage();
        }
    }

    /**
     * Splits content into manageable chunks
     */
    private String[] splitIntoChunks(String content) {
        if (content.length() <= MAX_CHUNK_SIZE) {
            return new String[]{content};
        }

        int numChunks = (int) Math.ceil((double) content.length() / MAX_CHUNK_SIZE);
        String[] chunks = new String[numChunks];

        for (int i = 0; i < numChunks; i++) {
            int start = i * MAX_CHUNK_SIZE;
            int end = Math.min(start + MAX_CHUNK_SIZE, content.length());
            chunks[i] = content.substring(start, end);
        }

        return chunks;
    }



    /**
     * Analyzes the document content and provides insights using AI
     *
     * @param content the document content
     * @return the analysis insights
     */
    public String analyzeContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "No content to analyze.";
        }

        try {
            logger.debug("Analyzing content of length: {}", content.length());

            // Truncate content if too long for analysis
            String analysisContent = content.length() > MAX_CHUNK_SIZE ?
                content.substring(0, MAX_CHUNK_SIZE) + "..." : content;

            // Create prompt for content analysis
            String prompt = String.format(
                "Please analyze the following document content and provide insights including:\n" +
                "1. Document type and structure\n" +
                "2. Main topics and themes\n" +
                "3. Writing style and tone\n" +
                "4. Key information and data points\n" +
                "5. Overall assessment\n\n" +
                "Document content:\n%s",
                analysisContent
            );

            // Call OpenAI API
            String analysis = chatModel.call(prompt);

            logger.debug("Generated AI analysis of length: {}", analysis.length());
            return analysis;

        } catch (Exception e) {
            logger.error("Failed to analyze content", e);
            return "Failed to analyze content: " + e.getMessage();
        }
    }

    /**
     * Generates a summary asynchronously
     * 
     * @param content the document content to summarize
     * @param maxLength maximum length of the summary
     * @return CompletableFuture with the generated summary
     */
    public CompletableFuture<String> generateSummaryAsync(String content, int maxLength) {
        return CompletableFuture.supplyAsync(() -> generateSummary(content, maxLength));
    }

    /**
     * Extracts key points from the document content using AI
     *
     * @param content the document content
     * @param maxPoints maximum number of key points
     * @return the extracted key points
     */
    public String extractKeyPoints(String content, int maxPoints) {
        if (content == null || content.trim().isEmpty()) {
            return "No content to analyze.";
        }

        try {
            logger.debug("Extracting key points from content of length: {}", content.length());

            // Truncate content if too long for analysis
            String analysisContent = content.length() > MAX_CHUNK_SIZE ?
                content.substring(0, MAX_CHUNK_SIZE) + "..." : content;

            // Create prompt for key points extraction
            String prompt = String.format(
                "Please extract the top %d key points from the following document content. " +
                "Format the response as a numbered list with clear, concise points:\n\n%s",
                maxPoints, analysisContent
            );

            // Call OpenAI API
            String keyPoints = chatModel.call(prompt);

            logger.debug("Extracted AI key points of length: {}", keyPoints.length());
            return keyPoints;

        } catch (Exception e) {
            logger.error("Failed to extract key points", e);
            return "Failed to extract key points: " + e.getMessage();
        }
    }
}
