package com.talkweb.ai.converter.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * A service that watches a directory for file changes using native WatchService API with fallback to polling.
 * Features enhanced exception handling, thread safety, and dynamic polling intervals.
 */
public class FileWatcherService {
    private static final Logger log = LoggerFactory.getLogger(FileWatcherService.class);

    // Configuration constants
    private static final long DEFAULT_POLL_INTERVAL_MS = 500;
    private static final long MIN_POLL_INTERVAL_MS = 100;
    private static final long MAX_POLL_INTERVAL_MS = 5000;
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;

    private final Path watchPath;
    private volatile ExecutorService executorService;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private volatile CountDownLatch startLatch;
    private final ConcurrentHashMap<Path, Long> lastKnownFiles = new ConcurrentHashMap<>();
    private final AtomicLong currentPollInterval = new AtomicLong(DEFAULT_POLL_INTERVAL_MS);

    // WatchService for native file watching
    private volatile WatchService watchService;
    private volatile boolean useNativeWatcher = true;

    // Thread-safe listener collections
    private final List<Consumer<Path>> fileCreatedListeners = new CopyOnWriteArrayList<>();
    private final List<Consumer<Path>> fileModifiedListeners = new CopyOnWriteArrayList<>();
    private final List<Consumer<Path>> fileDeletedListeners = new CopyOnWriteArrayList<>();

    public FileWatcherService(Path watchPath) throws IOException {
        this.watchPath = watchPath;

        if (!Files.exists(watchPath)) {
            watchPath.toFile().mkdirs();
            log.info("Watch path created: {}", watchPath);
           // throw new IllegalArgumentException("Path does not exist: " + watchPath);
        }
        if (!Files.isDirectory(watchPath)) {
            throw new IllegalArgumentException("Path must be a directory: " + watchPath);
        }

        // Try to initialize native watcher
        initializeWatcher();
    }

    public void addFileCreatedListener(Consumer<Path> listener) {
        fileCreatedListeners.add(listener);
    }

    public void addFileModifiedListener(Consumer<Path> listener) {
        fileModifiedListeners.add(listener);
    }

    public void addFileDeletedListener(Consumer<Path> listener) {
        fileDeletedListeners.add(listener);
    }

    public void start() {
        if (running.getAndSet(true)) {
            log.debug("File watcher is already running for path: {}", watchPath);
            return;
        }

        try {
            // Create new executor and latch for this start cycle
            this.startLatch = new CountDownLatch(1);
            this.executorService = Executors.newSingleThreadExecutor(r -> {
                Thread t = new Thread(r, "file-watcher-thread");
                t.setDaemon(true);
                t.setUncaughtExceptionHandler((thread, ex) -> {
                    log.error("Uncaught exception in file watcher thread", ex);
                    running.set(false);
                });
                return t;
            });

            // Initialize the baseline state with retry mechanism
            initializeBaselineState();

            executorService.submit(() -> {
                try {
                    // Signal that the watcher thread has started and is ready to process events
                    startLatch.countDown();

                    if (useNativeWatcher && watchService != null) {
                        processNativeEvents();
                    } else {
                        processPollingEvents();
                    }
                } catch (Exception e) {
                    log.error("Fatal error in file watcher thread", e);
                    running.set(false);
                }
            });

            log.info("File watcher started for path: {} (mode: {})", watchPath,
                    useNativeWatcher ? "native" : "polling");
        } catch (Exception e) {
            log.error("Failed to start file watcher for path: {}", watchPath, e);
            running.set(false);
            throw new RuntimeException("Failed to start file watcher", e);
        }
    }

    /**
     * For testing purposes. Waits until the watcher thread has started.
     */
    boolean awaitStarted(long timeout, TimeUnit unit) throws InterruptedException {
        CountDownLatch latch = startLatch;
        return latch != null && latch.await(timeout, unit);
    }

    public void stop() {
        if (!running.getAndSet(false)) {
            log.debug("File watcher is already stopped for path: {}", watchPath);
            return;
        }

        // First, signal shutdown to the processing thread
        log.debug("Stopping file watcher for path: {}", watchPath);

        // Shutdown executor and wait for processing thread to finish
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.warn("File watcher executor did not terminate in time, forcing shutdown");
                    List<Runnable> pendingTasks = executorService.shutdownNow();
                    if (!pendingTasks.isEmpty()) {
                        log.warn("Cancelled {} pending tasks", pendingTasks.size());
                    }
                    // Give a bit more time for forced shutdown
                    if (!executorService.awaitTermination(2, TimeUnit.SECONDS)) {
                        log.warn("File watcher executor still did not terminate after forced shutdown");
                    }
                }
            } catch (InterruptedException e) {
                log.warn("Interrupted while waiting for file watcher executor to stop");
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
            executorService = null;
        }

        // Now it's safe to close the watch service
        closeWatchService();

        // Clear state
        lastKnownFiles.clear();
        log.info("File watcher stopped for path: {}", watchPath);
    }

    /**
     * Initialize native WatchService or fall back to polling
     */
    private void initializeWatcher() {
        try {
            this.watchService = FileSystems.getDefault().newWatchService();
            watchPath.register(watchService,
                StandardWatchEventKinds.ENTRY_CREATE,
                StandardWatchEventKinds.ENTRY_DELETE,
                StandardWatchEventKinds.ENTRY_MODIFY);
            this.useNativeWatcher = true;
            log.info("Native file watcher initialized for path: {}", watchPath);
        } catch (IOException e) {
            log.warn("Failed to initialize native file watcher, falling back to polling: {}", e.getMessage());
            this.useNativeWatcher = false;
            closeWatchService();
        }
    }

    /**
     * Initialize baseline file state with retry mechanism
     */
    private void initializeBaselineState() {
        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                Map<Path, Long> initialState = getCurrentFileState();
                lastKnownFiles.clear();
                lastKnownFiles.putAll(initialState);
                log.debug("Initialized baseline state with {} files", initialState.size());
                return;
            } catch (IOException e) {
                attempts++;
                log.warn("Failed to establish initial file state (attempt {}/{}): {}",
                        attempts, MAX_RETRY_ATTEMPTS, e.getMessage());
                if (attempts >= MAX_RETRY_ATTEMPTS) {
                    throw new RuntimeException("Failed to initialize baseline state after " + MAX_RETRY_ATTEMPTS + " attempts", e);
                }
                try {
                    Thread.sleep(RETRY_DELAY_MS);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while retrying baseline state initialization", ie);
                }
            }
        }
    }

    /**
     * Process events using native WatchService
     */
    private void processNativeEvents() {
        log.debug("Starting native event processing");
        while (running.get() && watchService != null) {
            try {
                WatchKey key = watchService.poll(currentPollInterval.get(), TimeUnit.MILLISECONDS);
                if (key == null) {
                    continue; // Timeout, check running status
                }

                for (WatchEvent<?> event : key.pollEvents()) {
                    handleNativeEvent(event);
                }

                boolean valid = key.reset();
                if (!valid) {
                    log.warn("Watch key is no longer valid, attempting to re-register");
                    reinitializeWatcher();
                    break;
                }
            } catch (InterruptedException e) {
                if (running.get()) {
                    log.warn("Native event processing interrupted, switching to polling mode");
                    useNativeWatcher = false;
                    processPollingEvents();
                } else {
                    log.info("Native event processing interrupted during shutdown");
                }
                Thread.currentThread().interrupt();
                break;
            } catch (java.nio.file.ClosedWatchServiceException e) {
                // This is expected during shutdown, don't log as error
                log.debug("Watch service closed during shutdown");
                break;
            } catch (Exception e) {
                if (running.get()) {
                    log.error("Error in native event processing", e);
                    // Fall back to polling on persistent errors
                    useNativeWatcher = false;
                    processPollingEvents();
                } else {
                    log.debug("Error in native event processing during shutdown: {}", e.getMessage());
                }
                break;
            }
        }
        log.debug("Native event processing stopped");
    }

    /**
     * Process events using polling mechanism (fallback)
     */
    private void processPollingEvents() {
        log.debug("Starting polling event processing");
        long consecutiveErrors = 0;

        while (running.get()) {
            try {
                Map<Path, Long> currentState = getCurrentFileState();
                detectAndNotifyChanges(new HashMap<>(lastKnownFiles), currentState);
                lastKnownFiles.clear();
                lastKnownFiles.putAll(currentState);

                // Reset error counter on success
                consecutiveErrors = 0;
                adjustPollInterval(false);

                Thread.sleep(currentPollInterval.get());
            } catch (IOException e) {
                if (running.get()) {
                    consecutiveErrors++;
                    log.error("Error during file polling (consecutive errors: {})", consecutiveErrors, e);
                    adjustPollInterval(true);

                    if (consecutiveErrors >= MAX_RETRY_ATTEMPTS) {
                        log.error("Too many consecutive polling errors, stopping watcher");
                        running.set(false);
                        break;
                    }

                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.debug("IO error during shutdown: {}", e.getMessage());
                    break;
                }
            } catch (InterruptedException e) {
                if (running.get()) {
                    log.info("Polling interrupted during operation");
                } else {
                    log.debug("Polling interrupted during shutdown");
                }
                Thread.currentThread().interrupt();
                break;
            }
        }
        log.debug("Polling event processing stopped");
    }

    /**
     * Handle native WatchService events
     */
    private void handleNativeEvent(WatchEvent<?> event) {
        WatchEvent.Kind<?> kind = event.kind();

        if (kind == StandardWatchEventKinds.OVERFLOW) {
            log.warn("Watch event overflow detected, may have missed some events");
            return;
        }

        @SuppressWarnings("unchecked")
        WatchEvent<Path> pathEvent = (WatchEvent<Path>) event;
        Path fileName = pathEvent.context();
        Path fullPath = watchPath.resolve(fileName);

        try {
            if (kind == StandardWatchEventKinds.ENTRY_CREATE) {
                // Verify file actually exists (handle race conditions)
                if (Files.exists(fullPath) && Files.isRegularFile(fullPath)) {
                    long timestamp = Files.getLastModifiedTime(fullPath).toMillis();
                    lastKnownFiles.put(fullPath, timestamp);
                    notifyListeners(fileCreatedListeners, fullPath);
                }
            } else if (kind == StandardWatchEventKinds.ENTRY_MODIFY) {
                if (Files.exists(fullPath) && Files.isRegularFile(fullPath)) {
                    long timestamp = Files.getLastModifiedTime(fullPath).toMillis();
                    Long previousTimestamp = lastKnownFiles.put(fullPath, timestamp);
                    // Only notify if timestamp actually changed
                    if (previousTimestamp == null || timestamp != previousTimestamp.longValue()) {
                        notifyListeners(fileModifiedListeners, fullPath);
                    }
                }
            } else if (kind == StandardWatchEventKinds.ENTRY_DELETE) {
                lastKnownFiles.remove(fullPath);
                notifyListeners(fileDeletedListeners, fullPath);
            }
        } catch (IOException e) {
            log.error("Error handling native event for path: {}", fullPath, e);
        }
    }

    /**
     * Detect and notify changes between two file states
     */
    private void detectAndNotifyChanges(Map<Path, Long> previousState, Map<Path, Long> currentState) {
        // Detect creations and modifications
        for (Map.Entry<Path, Long> entry : currentState.entrySet()) {
            Path path = entry.getKey();
            Long currentTimestamp = entry.getValue();

            if (currentTimestamp == -1L) {
                continue; // Skip files with read errors
            }

            if (!previousState.containsKey(path)) {
                notifyListeners(fileCreatedListeners, path);
            } else {
                Long previousTimestamp = previousState.get(path);
                if (previousTimestamp != -1L && !currentTimestamp.equals(previousTimestamp)) {
                    notifyListeners(fileModifiedListeners, path);
                }
            }
        }

        // Detect deletions
        for (Path path : previousState.keySet()) {
            if (!currentState.containsKey(path)) {
                notifyListeners(fileDeletedListeners, path);
            }
        }
    }

    /**
     * Adjust polling interval based on system load and error conditions
     */
    private void adjustPollInterval(boolean hasError) {
        long current = currentPollInterval.get();
        if (hasError) {
            // Increase interval on errors to reduce system load
            long newInterval = Math.min(current * 2, MAX_POLL_INTERVAL_MS);
            currentPollInterval.set(newInterval);
            log.debug("Increased poll interval to {} ms due to error", newInterval);
        } else {
            // Gradually decrease interval on success
            long newInterval = Math.max(current - 50, MIN_POLL_INTERVAL_MS);
            if (newInterval != current) {
                currentPollInterval.set(newInterval);
                log.debug("Decreased poll interval to {} ms", newInterval);
            }
        }
    }

    /**
     * Reinitialize the watcher after a failure
     */
    private void reinitializeWatcher() {
        log.info("Reinitializing file watcher");
        closeWatchService();
        initializeWatcher();
        if (!useNativeWatcher) {
            log.warn("Failed to reinitialize native watcher, continuing with polling");
        }
    }

    /**
     * Restart the entire watcher (used by exception handler)
     */
    private void restartWatcher() {
        try {
            log.info("Restarting file watcher after fatal error");
            stop();
            Thread.sleep(RETRY_DELAY_MS);
            start();
        } catch (Exception e) {
            log.error("Failed to restart file watcher", e);
        }
    }

    /**
     * Close the WatchService safely
     */
    private void closeWatchService() {
        if (watchService != null) {
            try {
                watchService.close();
            } catch (IOException e) {
                log.warn("Error closing watch service", e);
            } finally {
                watchService = null;
            }
        }
    }

    /**
     * Get current file state with enhanced error handling
     */
    private Map<Path, Long> getCurrentFileState() throws IOException {
        if (!Files.exists(watchPath)) {
            log.debug("Watch path no longer exists: {}", watchPath);
            return new HashMap<>();
        }

        try (var stream = Files.list(watchPath)) {
            return stream
                .filter(path -> {
                    try {
                        return Files.isRegularFile(path);
                    } catch (Exception e) {
                        log.debug("Error checking if path is regular file: {}", path, e);
                        return false;
                    }
                })
                .collect(Collectors.toMap(
                    path -> path,
                    path -> {
                        try {
                            return Files.getLastModifiedTime(path).toMillis();
                        } catch (IOException e) {
                            log.debug("Error getting last modified time for path: {}", path, e);
                            return -1L;
                        }
                    }
                ));
        } catch (IOException e) {
            log.error("Error listing files in watch path: {}", watchPath, e);
            throw e;
        }
    }

    /**
     * Notify listeners with enhanced exception isolation
     */
    private void notifyListeners(List<Consumer<Path>> listeners, Path path) {
        if (listeners.isEmpty()) {
            return;
        }

        for (Consumer<Path> listener : listeners) {
            try {
                listener.accept(path);
            } catch (Exception e) {
                log.error("Error in file watcher listener for path {}: {}", path, e.getMessage(), e);
                // Continue with other listeners even if one fails
            }
        }
    }

    /**
     * Get current polling interval for monitoring
     */
    public long getCurrentPollInterval() {
        return currentPollInterval.get();
    }

    /**
     * Check if the watcher is currently running
     */
    public boolean isRunning() {
        return running.get();
    }

    /**
     * Check if using native watcher or polling
     */
    public boolean isUsingNativeWatcher() {
        return useNativeWatcher && watchService != null;
    }

    /**
     * Get the number of files currently being tracked
     */
    public int getTrackedFileCount() {
        return lastKnownFiles.size();
    }
}
