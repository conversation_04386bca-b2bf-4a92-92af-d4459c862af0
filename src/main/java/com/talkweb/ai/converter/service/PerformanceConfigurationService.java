package com.talkweb.ai.converter.service;

import com.talkweb.ai.converter.core.performance.PerformanceOptimizer;
import com.talkweb.ai.converter.core.performance.StreamingProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

// import javax.annotation.PostConstruct;
// import javax.annotation.PreDestroy;
import java.io.File;

/**
 * Service for managing performance configurations and optimizations
 * 
 * This service provides centralized performance management including:
 * - Dynamic performance optimization
 * - Memory monitoring and management
 * - Streaming processor configuration
 * - Performance metrics collection
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Service
public class PerformanceConfigurationService {
    
    private static final Logger logger = LoggerFactory.getLogger(PerformanceConfigurationService.class);
    
    private PerformanceOptimizer performanceOptimizer;
    private boolean optimizationEnabled = true;
    
    // @PostConstruct
    public void initialize() {
        logger.info("Initializing Performance Configuration Service");
        
        this.performanceOptimizer = new PerformanceOptimizer();
        
        // Log initial system information
        logSystemInformation();
        
        logger.info("Performance Configuration Service initialized successfully");
    }
    
    // @PreDestroy
    public void shutdown() {
        logger.info("Shutting down Performance Configuration Service");
        
        if (performanceOptimizer != null) {
            performanceOptimizer.shutdown();
        }
        
        logger.info("Performance Configuration Service shut down");
    }
    
    /**
     * Gets optimized conversion settings for a given file
     * 
     * @param inputFile the file to be converted
     * @return optimization settings
     */
    public PerformanceOptimizer.ConversionOptimizationSettings getOptimizationSettings(File inputFile) {
        if (!optimizationEnabled || inputFile == null || !inputFile.exists()) {
            return new PerformanceOptimizer.ConversionOptimizationSettings();
        }
        
        long fileSize = inputFile.length();
        return performanceOptimizer.optimizeForLargeFile(fileSize);
    }
    
    /**
     * Creates an optimized streaming processor for the given file
     * 
     * @param inputFile the file to be processed
     * @return configured streaming processor
     */
    public StreamingProcessor createOptimizedStreamingProcessor(File inputFile) {
        if (inputFile == null || !inputFile.exists()) {
            return new StreamingProcessor();
        }
        
        long fileSize = inputFile.length();
        int optimalBufferSize = StreamingProcessor.estimateOptimalBufferSize(fileSize);
        boolean useDirectBuffers = fileSize > 50 * 1024 * 1024; // Use direct buffers for files > 50MB
        
        logger.debug("Creating streaming processor for file {} (size: {} bytes) with buffer size: {} bytes, direct buffers: {}", 
                inputFile.getName(), fileSize, optimalBufferSize, useDirectBuffers);
        
        return new StreamingProcessor(optimalBufferSize, useDirectBuffers);
    }
    
    /**
     * Determines if streaming processing should be used for the given file
     * 
     * @param inputFile the file to be processed
     * @return true if streaming is recommended
     */
    public boolean shouldUseStreamingProcessing(File inputFile) {
        if (!optimizationEnabled || inputFile == null || !inputFile.exists()) {
            return false;
        }
        
        return StreamingProcessor.shouldUseStreaming(inputFile.length());
    }
    
    /**
     * Gets current performance metrics
     * 
     * @return performance metrics
     */
    public PerformanceOptimizer.PerformanceMetrics getPerformanceMetrics() {
        return performanceOptimizer != null ? performanceOptimizer.getMetrics() : null;
    }
    
    /**
     * Enables or disables performance optimization
     * 
     * @param enabled true to enable optimization
     */
    public void setOptimizationEnabled(boolean enabled) {
        this.optimizationEnabled = enabled;
        
        if (performanceOptimizer != null) {
            performanceOptimizer.setMonitoringEnabled(enabled);
        }
        
        logger.info("Performance optimization {}", enabled ? "enabled" : "disabled");
    }
    
    /**
     * Checks if optimization is enabled
     * 
     * @return true if optimization is enabled
     */
    public boolean isOptimizationEnabled() {
        return optimizationEnabled;
    }
    
    /**
     * Gets memory usage recommendations for conversion
     * 
     * @param fileSize the size of the file to convert
     * @return memory recommendations
     */
    public MemoryRecommendations getMemoryRecommendations(long fileSize) {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long availableMemory = maxMemory - usedMemory;
        
        MemoryRecommendations recommendations = new MemoryRecommendations();
        recommendations.setCurrentMemoryUsage(usedMemory);
        recommendations.setAvailableMemory(availableMemory);
        recommendations.setMaxMemory(maxMemory);
        
        // Calculate recommendations based on file size and available memory
        if (fileSize > availableMemory * 0.5) {
            recommendations.setRecommendation("Use streaming processing - file is large relative to available memory");
            recommendations.setUseStreaming(true);
            recommendations.setRecommendedChunkSize(StreamingProcessor.estimateOptimalBufferSize(fileSize));
        } else if (fileSize > availableMemory * 0.2) {
            recommendations.setRecommendation("Consider streaming processing for better memory efficiency");
            recommendations.setUseStreaming(true);
            recommendations.setRecommendedChunkSize(StreamingProcessor.estimateOptimalBufferSize(fileSize));
        } else {
            recommendations.setRecommendation("In-memory processing is suitable");
            recommendations.setUseStreaming(false);
        }
        
        return recommendations;
    }
    
    /**
     * Logs current system information
     */
    private void logSystemInformation() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        
        logger.info("System Information:");
        logger.info("  Max Memory: {} MB", maxMemory / (1024 * 1024));
        logger.info("  Total Memory: {} MB", totalMemory / (1024 * 1024));
        logger.info("  Free Memory: {} MB", freeMemory / (1024 * 1024));
        logger.info("  Used Memory: {} MB", (totalMemory - freeMemory) / (1024 * 1024));
        logger.info("  Available Processors: {}", runtime.availableProcessors());
    }
    
    /**
     * Memory recommendations holder
     */
    public static class MemoryRecommendations {
        private long currentMemoryUsage;
        private long availableMemory;
        private long maxMemory;
        private String recommendation;
        private boolean useStreaming;
        private int recommendedChunkSize;
        
        // Getters and setters
        public long getCurrentMemoryUsage() { return currentMemoryUsage; }
        public void setCurrentMemoryUsage(long currentMemoryUsage) { this.currentMemoryUsage = currentMemoryUsage; }
        
        public long getAvailableMemory() { return availableMemory; }
        public void setAvailableMemory(long availableMemory) { this.availableMemory = availableMemory; }
        
        public long getMaxMemory() { return maxMemory; }
        public void setMaxMemory(long maxMemory) { this.maxMemory = maxMemory; }
        
        public String getRecommendation() { return recommendation; }
        public void setRecommendation(String recommendation) { this.recommendation = recommendation; }
        
        public boolean isUseStreaming() { return useStreaming; }
        public void setUseStreaming(boolean useStreaming) { this.useStreaming = useStreaming; }
        
        public int getRecommendedChunkSize() { return recommendedChunkSize; }
        public void setRecommendedChunkSize(int recommendedChunkSize) { this.recommendedChunkSize = recommendedChunkSize; }
        
        @Override
        public String toString() {
            return String.format("MemoryRecommendations{used=%d MB, available=%d MB, max=%d MB, streaming=%s, recommendation='%s'}",
                    currentMemoryUsage / (1024 * 1024), availableMemory / (1024 * 1024), maxMemory / (1024 * 1024), 
                    useStreaming, recommendation);
        }
    }
}
