package com.talkweb.ai.converter.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Simple document chunk representation
 */
class DocumentChunk {
    private final String content;
    private final Map<String, Object> metadata;
    private final float[] embedding;

    public DocumentChunk(String content, Map<String, Object> metadata, float[] embedding) {
        this.content = content;
        this.metadata = metadata;
        this.embedding = embedding;
    }

    public String getContent() { return content; }
    public Map<String, Object> getMetadata() { return metadata; }
    public float[] getEmbedding() { return embedding; }
}

/**
 * Service for generating document embeddings and vector representations
 *
 * Real implementation using Spring AI EmbeddingModel and OpenAI integration.
 *
 * <AUTHOR> Assistant
 * @version 2.0
 */
@Service
@ConditionalOnProperty(name = "ai.enabled", havingValue = "true", matchIfMissing = false)
public class DocumentEmbeddingService {

    private static final Logger logger = LoggerFactory.getLogger(DocumentEmbeddingService.class);
    private static final int MAX_CHUNK_SIZE = 8000; // Maximum characters per chunk for embedding

    private final EmbeddingModel embeddingModel;

    public DocumentEmbeddingService(EmbeddingModel embeddingModel) {
        this.embeddingModel = embeddingModel;
        logger.info("DocumentEmbeddingService initialized with real AI embedding integration");
    }

    /**
     * Generates embeddings for a document using OpenAI embedding model
     *
     * @param content the document content
     * @param metadata additional metadata for the document
     * @return list of document chunks with real embeddings
     */
    public List<DocumentChunk> generateEmbeddings(String content, Map<String, Object> metadata) {
        if (content == null || content.trim().isEmpty()) {
            logger.warn("Cannot generate embeddings for empty content");
            return new ArrayList<>();
        }

        try {
            logger.debug("Generating AI embeddings for content of length: {}", content.length());

            // Split content into chunks
            List<String> chunks = splitIntoChunks(content, MAX_CHUNK_SIZE);
            List<DocumentChunk> documents = new ArrayList<>();

            for (int i = 0; i < chunks.size(); i++) {
                String chunk = chunks.get(i);

                // Create document chunk with metadata
                Map<String, Object> chunkMetadata = new java.util.HashMap<>(metadata);
                chunkMetadata.put("chunk_index", i);
                chunkMetadata.put("total_chunks", chunks.size());
                chunkMetadata.put("chunk_size", chunk.length());

                // Generate real embedding using OpenAI
                float[] embedding = generateRealEmbedding(chunk);

                DocumentChunk document = new DocumentChunk(chunk, chunkMetadata, embedding);
                documents.add(document);

                logger.debug("Generated AI embedding of size {} for chunk {}", embedding.length, i);
            }

            logger.info("Generated AI embeddings for {} chunks", documents.size());
            return documents;

        } catch (Exception e) {
            logger.error("Failed to generate AI embeddings", e);
            return new ArrayList<>();
        }
    }

    /**
     * Generates a real embedding vector using OpenAI
     */
    private float[] generateRealEmbedding(String text) {
        try {
            // Create embedding request
            EmbeddingRequest request = new EmbeddingRequest(List.of(text), null);

            // Get embedding response from OpenAI
            EmbeddingResponse response = embeddingModel.call(request);

            // Extract the embedding vector
            if (response.getResults() != null && !response.getResults().isEmpty()) {
                return response.getResults().get(0).getOutput();
            } else {
                logger.warn("No embedding result returned for text");
                return new float[0];
            }
        } catch (Exception e) {
            logger.error("Failed to generate real embedding for text", e);
            // Fallback to empty array
            return new float[0];
        }
    }

    /**
     * Generates embeddings asynchronously
     *
     * @param content the document content
     * @param metadata additional metadata for the document
     * @return CompletableFuture with the list of document chunks with embeddings
     */
    public CompletableFuture<List<DocumentChunk>> generateEmbeddingsAsync(String content, Map<String, Object> metadata) {
        return CompletableFuture.supplyAsync(() -> generateEmbeddings(content, metadata));
    }

    /**
     * Generates a single embedding for a text using OpenAI
     *
     * @param text the text to embed
     * @return the embedding vector
     */
    public float[] generateSingleEmbedding(String text) {
        if (text == null || text.trim().isEmpty()) {
            logger.warn("Cannot generate embedding for empty text");
            return new float[0];
        }

        try {
            logger.debug("Generating single AI embedding for text of length: {}", text.length());

            // Generate real embedding using OpenAI
            float[] embedding = generateRealEmbedding(text);
            logger.debug("Generated single AI embedding of size: {}", embedding.length);
            return embedding;

        } catch (Exception e) {
            logger.error("Failed to generate single AI embedding", e);
            return new float[0];
        }
    }

    /**
     * Calculates cosine similarity between two embedding vectors
     * 
     * @param embedding1 first embedding vector
     * @param embedding2 second embedding vector
     * @return cosine similarity score (0-1)
     */
    public double calculateSimilarity(float[] embedding1, float[] embedding2) {
        if (embedding1.length != embedding2.length || embedding1.length == 0) {
            return 0.0;
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < embedding1.length; i++) {
            dotProduct += embedding1[i] * embedding2[i];
            norm1 += embedding1[i] * embedding1[i];
            norm2 += embedding2[i] * embedding2[i];
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * Splits content into chunks of specified maximum size
     * 
     * @param content the content to split
     * @param maxSize maximum size per chunk
     * @return list of content chunks
     */
    private List<String> splitIntoChunks(String content, int maxSize) {
        List<String> chunks = new ArrayList<>();
        
        if (content.length() <= maxSize) {
            chunks.add(content);
            return chunks;
        }

        // Split by paragraphs first, then by sentences if needed
        String[] paragraphs = content.split("\n\n");
        StringBuilder currentChunk = new StringBuilder();

        for (String paragraph : paragraphs) {
            if (currentChunk.length() + paragraph.length() + 2 <= maxSize) {
                if (currentChunk.length() > 0) {
                    currentChunk.append("\n\n");
                }
                currentChunk.append(paragraph);
            } else {
                // Save current chunk if not empty
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString());
                    currentChunk = new StringBuilder();
                }

                // If paragraph is too long, split by sentences
                if (paragraph.length() > maxSize) {
                    String[] sentences = paragraph.split("\\. ");
                    for (String sentence : sentences) {
                        if (currentChunk.length() + sentence.length() + 2 <= maxSize) {
                            if (currentChunk.length() > 0) {
                                currentChunk.append(". ");
                            }
                            currentChunk.append(sentence);
                        } else {
                            if (currentChunk.length() > 0) {
                                chunks.add(currentChunk.toString());
                                currentChunk = new StringBuilder();
                            }
                            currentChunk.append(sentence);
                        }
                    }
                } else {
                    currentChunk.append(paragraph);
                }
            }
        }

        // Add the last chunk if not empty
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString());
        }

        return chunks;
    }
}
