package com.talkweb.ai.converter.config;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Objects;

/**
 * 配置工具类，提供便捷的配置加载方法
 */
public final class Configurations {

    private static final ConfigurationLoader JSON_LOADER = new JsonConfigurationLoader();
    private static final ConfigurationLoader YAML_LOADER = new YamlConfigurationLoader();

    private Configurations() {
        // 工具类，防止实例化
    }

    /**
     * 创建一个新的空配置
     */
    public static AppConfiguration empty() {
        return new MapConfiguration();
    }

    /**
     * 创建一个新的配置构建器
     */
    public static MapConfiguration.Builder builder() {
        return MapConfiguration.builder();
    }

    /**
     * 从文件加载配置，自动检测格式（.json 或 .yaml/.yml）
     */
    public static AppConfiguration load(Path file) throws IOException, ConfigurationException {
        Objects.requireNonNull(file, "File cannot be null");

        String fileName = file.getFileName().toString().toLowerCase();

        if (fileName.endsWith(".json")) {
            return JSON_LOADER.load(file);
        } else if (fileName.endsWith(".yaml") || fileName.endsWith(".yml")) {
            return YAML_LOADER.load(file);
        } else {
            throw new ConfigurationException("Unsupported configuration format: " + fileName);
        }
    }

    /**
     * 从类路径资源加载配置，自动检测格式
     */
    public static AppConfiguration loadFromClasspath(String resource) throws IOException, ConfigurationException {
        Objects.requireNonNull(resource, "Resource cannot be null");

        if (resource.toLowerCase().endsWith(".json")) {
            return JSON_LOADER.loadFromClasspath(resource);
        } else if (resource.toLowerCase().endsWith(".yaml") || resource.toLowerCase().endsWith(".yml")) {
            return YAML_LOADER.loadFromClasspath(resource);
        } else {
            // 尝试自动检测格式
            try (InputStream input = Thread.currentThread().getContextClassLoader().getResourceAsStream(resource)) {
                if (input == null) {
                    throw new ConfigurationException("Resource not found: " + resource);
                }

                // 读取前几个字节检测文件类型
                byte[] header = new byte[512];
                int read = input.read(header);
                if (read == -1) {
                    throw new ConfigurationException("Empty configuration file: " + resource);
                }

                String content = new String(header, 0, read).trim();
                if (content.startsWith("{") || content.startsWith("[")) {
                    // 可能是JSON
                    try (InputStream jsonInput = Thread.currentThread().getContextClassLoader().getResourceAsStream(resource)) {
                        return JSON_LOADER.load(jsonInput);
                    }
                } else if (content.startsWith("---") || content.startsWith("#")) {
                    // 可能是YAML
                    try (InputStream yamlInput = Thread.currentThread().getContextClassLoader().getResourceAsStream(resource)) {
                        return YAML_LOADER.load(yamlInput);
                    }
                } else {
                    // 默认尝试作为YAML加载
                    try (InputStream yamlInput = Thread.currentThread().getContextClassLoader().getResourceAsStream(resource)) {
                        return YAML_LOADER.load(yamlInput);
                    } catch (Exception e) {
                        // 如果YAML加载失败，尝试作为JSON
                        try (InputStream jsonInput = Thread.currentThread().getContextClassLoader().getResourceAsStream(resource)) {
                            return JSON_LOADER.load(jsonInput);
                        }
                    }
                }
            }
        }
    }

    /**
     * 从JSON字符串加载配置
     */
    public static AppConfiguration fromJson(String json) throws IOException, ConfigurationException {
        return JSON_LOADER.fromJson(json);
    }

    /**
     * 从YAML字符串加载配置
     */
    public static AppConfiguration fromYaml(String yaml) throws IOException, ConfigurationException {
        return YAML_LOADER.fromYaml(yaml);
    }

    /**
     * 将配置保存到文件，根据文件扩展名自动选择格式
     */
    public static void save(AppConfiguration config, Path file) throws IOException {
        Objects.requireNonNull(file, "File cannot be null");

        String fileName = file.getFileName().toString().toLowerCase();

        // 确保目录存在
        Files.createDirectories(file.getParent());

        if (fileName.endsWith(".json")) {
            ((JsonConfigurationLoader) JSON_LOADER).save(config, Files.newOutputStream(file));
        } else if (fileName.endsWith(".yaml") || fileName.endsWith(".yml")) {
            ((YamlConfigurationLoader) YAML_LOADER).save(config, file);
        } else {
            throw new IOException("Unsupported configuration format: " + fileName);
        }
    }

    /**
     * 将配置转换为JSON字符串
     */
    public static String toJson(AppConfiguration config) throws IOException {
        return ((JsonConfigurationLoader) JSON_LOADER).toJson(config);
    }

    /**
     * 将配置转换为YAML字符串
     */
    public static String toYaml(AppConfiguration config) throws IOException {
        return ((YamlConfigurationLoader) YAML_LOADER).toYaml(config);
    }
}
