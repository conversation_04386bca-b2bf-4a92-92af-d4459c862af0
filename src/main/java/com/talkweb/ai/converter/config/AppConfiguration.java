package com.talkweb.ai.converter.config;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 应用配置接口
 * 重命名为AppConfiguration避免与Spring的Configuration冲突
 */
public interface AppConfiguration {
    String getString(String key, String defaultValue);
    int getInt(String key, int defaultValue);
    long getLong(String key, long defaultValue);
    double getDouble(String key, double defaultValue);
    boolean getBoolean(String key, boolean defaultValue);
    List<String> getStringList(String key);
    Object getRawValue(String key);
    Iterable<String> getKeys();
    Optional<AppConfiguration> getConfig(String key);
    List<AppConfiguration> getConfigList(String key);
    Map<String, Object> toMap();
    ConfigValueType getValueType(String key);
}
