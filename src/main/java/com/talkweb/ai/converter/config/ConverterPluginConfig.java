
package com.talkweb.ai.converter.config;

import com.talkweb.ai.converter.core.impl.*;
import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginMetadata;
import com.talkweb.ai.converter.core.adapter.ConverterPluginAdapterFactory;
import com.talkweb.ai.converter.util.RtfToMarkdownConverter;
import com.talkweb.ai.converter.util.OdtToMarkdownConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ConverterPluginConfig {

    @Bean
    public Plugin htmlConverterPlugin() {
        HtmlToMarkdownConverter converter = new HtmlToMarkdownConverter();
        PluginMetadata metadata = PluginMetadata.builder()
                .id("html-converter")
                .name("HTML to Markdown Converter")
                .version("3.0.0")
                .description("Converts HTML files (.html, .htm) to Markdown format with enhanced structure preservation")
                .className(HtmlToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
    }

    @Bean
    public Plugin pdfConverterPlugin() {
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter();
        PluginMetadata metadata = PluginMetadata.builder()
                .id("pdf-converter")
                .name("PDF to Markdown Converter")
                .version("3.0.0")
                .description("Converts PDF files to Markdown format with structure preservation and page splitting")
                .className(PdfToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
    }

    @Bean
    public Plugin excelConverterPlugin() {
        ExcelToMarkdownConverter converter = new ExcelToMarkdownConverter();
        PluginMetadata metadata = PluginMetadata.builder()
                .id("excel-converter")
                .name("Excel to Markdown Converter")
                .version("3.0.0")
                .description("Converts Excel files (.xls, .xlsx, .xlsm) to Markdown tables with structure preservation")
                .className(ExcelToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
    }

    @Bean
    public Plugin wordConverterPlugin() {
        WordToMarkdownConverter converter = new WordToMarkdownConverter();
        PluginMetadata metadata = PluginMetadata.builder()
                .id("word-converter")
                .name("Word to Markdown Converter")
                .version("3.0.0")
                .description("Converts Word files (.doc, .docx) to Markdown format with comprehensive element support")
                .className(WordToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
    }

    @Bean
    public Plugin pptConverterPlugin() {
        PptToMarkdownConverter converter = new PptToMarkdownConverter();
        PluginMetadata metadata = PluginMetadata.builder()
                .id("ppt-converter")
                .name("PowerPoint to Markdown Converter")
                .version("3.0.0")
                .description("Converts PowerPoint files (.ppt, .pptx, .pptm) to Markdown format with comprehensive slide content extraction")
                .className(PptToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
    }

    @Bean
    public Plugin rtfConverterPlugin() {
        com.talkweb.ai.converter.core.impl.RtfToMarkdownConverter converter =
            new com.talkweb.ai.converter.core.impl.RtfToMarkdownConverter();
        PluginMetadata metadata = PluginMetadata.builder()
                .id("rtf-converter")
                .name("RTF to Markdown Converter")
                .version("3.0.0")
                .description("Converts RTF files to Markdown format with comprehensive content extraction and structure preservation")
                .className(com.talkweb.ai.converter.core.impl.RtfToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
    }

    @Bean
    public Plugin odtConverterPlugin() {
        com.talkweb.ai.converter.core.impl.OdtToMarkdownConverter converter =
            new com.talkweb.ai.converter.core.impl.OdtToMarkdownConverter();
        PluginMetadata metadata = PluginMetadata.builder()
                .id("odt-converter")
                .name("ODT to Markdown Converter")
                .version("3.0.0")
                .description("Converts ODT files to Markdown format with comprehensive content extraction and structure preservation")
                .className(com.talkweb.ai.converter.core.impl.OdtToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
    }

    @Bean
    public Plugin imageConverterPlugin(ImageToMarkdownConverter imageConverter) {
        PluginMetadata metadata = PluginMetadata.builder()
                .id("image-converter")
                .name("Image to Markdown Converter")
                .version("1.0.0")
                .description("Converts image files (.png, .jpg, .jpeg, .tiff, .bmp, .gif) to Markdown format using OCR technology")
                .className(ImageToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(imageConverter, metadata);
    }
}
