package com.talkweb.ai.converter.config;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;
import java.util.LinkedHashMap;

public class YamlConfigurationLoader implements ConfigurationLoader {

    public String toYaml(AppConfiguration config) throws IOException {
        if (config == null) {
            return "";
        }
        return yamlMapper.writeValueAsString(config.toMap());
    }

    private final com.fasterxml.jackson.dataformat.yaml.YAMLMapper yamlMapper = new com.fasterxml.jackson.dataformat.yaml.YAMLMapper();

    @Override
    public AppConfiguration from<PERSON>son(String json) throws IOException, ConfigurationException {
        throw new UnsupportedOperationException("JSON parsing not supported by YAML loader");
    }

    @Override
    public AppConfiguration fromYaml(String yaml) throws IOException, ConfigurationException {
        if (yaml == null || yaml.trim().isEmpty()) {
            return new MapConfiguration(new LinkedHashMap<>());
        }
        try {
            byte[] bytes = yaml.getBytes(StandardCharsets.UTF_8);
            try (InputStream input = new ByteArrayInputStream(bytes)) {
                return load(input);
            }
        } catch (Exception e) {
            throw new ConfigurationException("Failed to load YAML from string", e);
        }
    }

    @Override
    public AppConfiguration load(InputStream input) throws IOException, ConfigurationException {
        try {
            if (input == null) {
                throw new ConfigurationException("Input stream cannot be null");
            }
            byte[] bytes = input.readAllBytes();
            if (new String(bytes, java.nio.charset.StandardCharsets.UTF_8).trim().isEmpty()) {
                return new MapConfiguration(new LinkedHashMap<>());
            }

            try (ByteArrayInputStream byteInput = new ByteArrayInputStream(bytes)) {
                // 先尝试解析为Map
                try {
                    Map<String, Object> map = yamlMapper.readValue(
                            byteInput,
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {}
                    );
                    return new MapConfiguration(map);
                } catch (com.fasterxml.jackson.databind.exc.MismatchedInputException e) {
                    // 如果不是Map格式，尝试解析为List
                    byteInput.reset();
                    try {
                        java.util.List<Object> list = yamlMapper.readValue(
                                byteInput,
                                new com.fasterxml.jackson.core.type.TypeReference<java.util.List<Object>>() {}
                        );
                        // 将List转换为Map，使用"_array"作为键
                        Map<String, Object> map = new LinkedHashMap<>();
                        map.put("_array", list);
                        return new MapConfiguration(map);
                    } catch (Exception inner_e) {
                        throw new ConfigurationException("Failed to parse YAML. It is not a valid map or list.", inner_e);
                    }
                }
            }
        } catch (IOException e) {
            throw new ConfigurationException("Failed to load YAML configuration", e);
        }
    }

    public AppConfiguration load(Path path) throws IOException, ConfigurationException {
        try (InputStream input = Files.newInputStream(path)) {
            return load(input);
        }
    }

    public void save(AppConfiguration config, Path path) throws IOException {
        if (config == null || path == null) {
            throw new IllegalArgumentException("Config and path cannot be null");
        }

        try (OutputStream output = Files.newOutputStream(path)) {
            yamlMapper.writeValue(output, config.toMap());
        }
    }
}
