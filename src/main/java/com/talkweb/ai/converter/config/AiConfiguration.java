package com.talkweb.ai.converter.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * AI Configuration for document processing and analysis
 *
 * Uses Spring AI auto-configuration for OpenAI integration.
 * Configuration is handled through application.properties.
 *
 * <AUTHOR> Assistant
 * @version 2.0
 */
@Configuration
@ConditionalOnProperty(name = "ai.enabled", havingValue = "true", matchIfMissing = false)
public class AiConfiguration {
    // Spring AI auto-configuration will handle ChatModel and EmbeddingModel beans
    // based on application.properties configuration
}
