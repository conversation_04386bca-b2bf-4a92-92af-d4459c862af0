package com.talkweb.ai.converter.config;

import com.talkweb.ai.converter.core.PluginManager;
import com.talkweb.ai.converter.core.impl.DefaultPluginManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;

/**
 * 插件系统配置类
 */
@Configuration
public class PluginConfig implements com.talkweb.ai.converter.core.PluginConfig {

    @Value("${app.plugins.dir:plugins}")
    private String pluginsDir;

    @Value("${app.temp.dir:temp}")
    private String tempDir;

    @Value("${app.config.dir:config}")
    private String configDir;

    @Value("${app.poll.interval:5000}")
    private long pollInterval;

    @Value("${app.debounce.time:500}")
    private long debounceTime;

    @Value("${app.mode:development}")
    private String mode;

    @Override
    public long getPollInterval() {
        return pollInterval;
    }

    @Override
    public long getDebounceTime() {
        return debounceTime;
    }

    @Override
    public String getMode() {
        return mode;
    }

    @Override
    public List<String> getBlacklist() {
        return Collections.emptyList();
    }

    @Override
    public List<String> getWhitelist() {
        return Collections.emptyList();
    }

    @Override
    public Path getPluginsDir() {
        return Paths.get(pluginsDir);
    }

    /**
     * 创建插件管理器Bean
     * @return 插件管理器实例
     */
    @Bean(destroyMethod = "shutdown")
    public PluginManager pluginManager() {
        return new DefaultPluginManager(this);
    }

    public void setPluginsDir(String pluginsDir) {
        this.pluginsDir = pluginsDir;
    }

    public String getTempDir() {
        return tempDir;
    }

    public void setTempDir(String tempDir) {
        this.tempDir = tempDir;
    }

    public String getConfigDir() {
        return configDir;
    }

    public void setConfigDir(String configDir) {
        this.configDir = configDir;
    }

    public void setPollInterval(long pollInterval) {
        this.pollInterval = pollInterval;
    }

    public void setDebounceTime(long debounceTime) {
        this.debounceTime = debounceTime;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }
}
