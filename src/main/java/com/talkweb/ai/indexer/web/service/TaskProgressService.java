package com.talkweb.ai.indexer.web.service;

import com.talkweb.ai.indexer.web.dto.TaskProgressResponse;
import com.talkweb.ai.indexer.web.model.TaskStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Service for tracking and managing task progress
 */
@Service
@Profile("server")
public class TaskProgressService {

    private static final Logger logger = LoggerFactory.getLogger(TaskProgressService.class);

    private TaskNotificationService notificationService;

    // In-memory storage for task progress (could be replaced with Redis in production)
    private final Map<String, TaskProgressInfo> taskProgress = new ConcurrentHashMap<>();

    /**
     * Set notification service (to avoid circular dependency)
     */
    @Autowired(required = false)
    public void setNotificationService(TaskNotificationService notificationService) {
        this.notificationService = notificationService;
    }
    
    // Progress listeners for real-time updates
    private final Map<String, List<Consumer<TaskProgressResponse>>> progressListeners = new ConcurrentHashMap<>();

    /**
     * Update task progress
     */
    public void updateProgress(String taskId, TaskStatus status, Integer progress, String message) {
        TaskProgressInfo progressInfo = taskProgress.computeIfAbsent(taskId, k -> new TaskProgressInfo(taskId));
        
        progressInfo.setStatus(status);
        progressInfo.setProgress(progress);
        progressInfo.setMessage(message);
        progressInfo.setLastUpdated(LocalDateTime.now());
        
        // Calculate estimated time remaining
        if (status == TaskStatus.PROCESSING && progress != null && progress > 0) {
            long elapsedMs = java.time.Duration.between(progressInfo.getStartTime(), LocalDateTime.now()).toMillis();
            long estimatedTotalMs = (elapsedMs * 100) / progress;
            long remainingMs = estimatedTotalMs - elapsedMs;
            progressInfo.setEstimatedTimeRemainingMs(Math.max(0, remainingMs));
        }
        
        logger.debug("Updated progress for task {}: {}% - {}", taskId, progress, message);

        // Notify listeners
        notifyProgressListeners(taskId, createProgressResponse(progressInfo));

        // Send WebSocket notification
        if (notificationService != null) {
            notificationService.notifyTaskProgress(taskId, status, progress, message);
        }
    }

    /**
     * Update task progress with current step
     */
    public void updateProgress(String taskId, TaskStatus status, Integer progress, String message, String currentStep) {
        TaskProgressInfo progressInfo = taskProgress.computeIfAbsent(taskId, k -> new TaskProgressInfo(taskId));
        progressInfo.setCurrentStep(currentStep);
        
        updateProgress(taskId, status, progress, message);
    }

    /**
     * Mark task as started
     */
    public void markTaskStarted(String taskId) {
        TaskProgressInfo progressInfo = taskProgress.computeIfAbsent(taskId, k -> new TaskProgressInfo(taskId));
        progressInfo.setStartTime(LocalDateTime.now());

        updateProgress(taskId, TaskStatus.PROCESSING, 0, "Task started");

        // Send notification
        if (notificationService != null) {
            notificationService.notifyTaskStarted(taskId);
        }
    }

    /**
     * Mark task as completed
     */
    public void markTaskCompleted(String taskId) {
        updateProgress(taskId, TaskStatus.COMPLETED, 100, "Task completed successfully");

        // Send notification
        if (notificationService != null) {
            notificationService.notifyTaskCompleted(taskId);
        }

        // Clean up progress info after a delay (could be done by a scheduled task)
        scheduleProgressCleanup(taskId);
    }

    /**
     * Mark task as failed
     */
    public void markTaskFailed(String taskId, String errorMessage) {
        TaskProgressInfo progressInfo = taskProgress.get(taskId);
        if (progressInfo != null) {
            progressInfo.setErrorMessage(errorMessage);
        }

        updateProgress(taskId, TaskStatus.FAILED, null, "Task failed: " + errorMessage);

        // Send notification
        if (notificationService != null) {
            notificationService.notifyTaskFailed(taskId, errorMessage);
        }

        scheduleProgressCleanup(taskId);
    }

    /**
     * Mark task as cancelled
     */
    public void markTaskCancelled(String taskId) {
        updateProgress(taskId, TaskStatus.CANCELLED, null, "Task was cancelled");

        // Send notification
        if (notificationService != null) {
            notificationService.notifyTaskCancelled(taskId);
        }

        scheduleProgressCleanup(taskId);
    }

    /**
     * Get current task progress
     */
    public TaskProgressResponse getProgress(String taskId) {
        TaskProgressInfo progressInfo = taskProgress.get(taskId);
        
        if (progressInfo == null) {
            return TaskProgressResponse.pending(taskId);
        }
        
        return createProgressResponse(progressInfo);
    }

    /**
     * Add progress listener for real-time updates
     */
    public void addProgressListener(String taskId, Consumer<TaskProgressResponse> listener) {
        progressListeners.computeIfAbsent(taskId, k -> new CopyOnWriteArrayList<>()).add(listener);
        logger.debug("Added progress listener for task {}", taskId);
    }

    /**
     * Remove progress listener
     */
    public void removeProgressListener(String taskId, Consumer<TaskProgressResponse> listener) {
        List<Consumer<TaskProgressResponse>> listeners = progressListeners.get(taskId);
        if (listeners != null) {
            listeners.remove(listener);
            if (listeners.isEmpty()) {
                progressListeners.remove(taskId);
            }
        }
        logger.debug("Removed progress listener for task {}", taskId);
    }

    /**
     * Remove all progress listeners for a task
     */
    public void removeAllProgressListeners(String taskId) {
        progressListeners.remove(taskId);
        logger.debug("Removed all progress listeners for task {}", taskId);
    }

    /**
     * Clean up progress data for completed/failed/cancelled tasks
     */
    public void cleanupProgress(String taskId) {
        taskProgress.remove(taskId);
        progressListeners.remove(taskId);
        logger.debug("Cleaned up progress data for task {}", taskId);
    }

    /**
     * Get all active task progress
     */
    public Map<String, TaskProgressResponse> getAllActiveProgress() {
        Map<String, TaskProgressResponse> result = new ConcurrentHashMap<>();
        
        taskProgress.forEach((taskId, progressInfo) -> {
            if (!progressInfo.getStatus().isTerminal()) {
                result.put(taskId, createProgressResponse(progressInfo));
            }
        });
        
        return result;
    }

    /**
     * Check for stuck tasks and mark them as failed
     */
    public void checkForStuckTasks(int timeoutMinutes) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        
        taskProgress.entrySet().removeIf(entry -> {
            TaskProgressInfo progressInfo = entry.getValue();
            String taskId = entry.getKey();
            
            if (progressInfo.getStatus() == TaskStatus.PROCESSING && 
                progressInfo.getLastUpdated().isBefore(cutoffTime)) {
                
                logger.warn("Task {} appears to be stuck, marking as failed", taskId);
                markTaskFailed(taskId, "Task timeout - no progress updates received");
                return true;
            }
            
            return false;
        });
    }

    // Helper methods
    private TaskProgressResponse createProgressResponse(TaskProgressInfo progressInfo) {
        TaskProgressResponse response = new TaskProgressResponse(
            progressInfo.getTaskId(),
            progressInfo.getStatus(),
            progressInfo.getProgress(),
            progressInfo.getMessage()
        );
        
        response.setCurrentStep(progressInfo.getCurrentStep());
        response.setLastUpdated(progressInfo.getLastUpdated());
        response.setEstimatedTimeRemainingMs(progressInfo.getEstimatedTimeRemainingMs());
        response.setErrorMessage(progressInfo.getErrorMessage());
        
        return response;
    }

    private void notifyProgressListeners(String taskId, TaskProgressResponse progressResponse) {
        List<Consumer<TaskProgressResponse>> listeners = progressListeners.get(taskId);
        if (listeners != null && !listeners.isEmpty()) {
            listeners.forEach(listener -> {
                try {
                    listener.accept(progressResponse);
                } catch (Exception e) {
                    logger.error("Error notifying progress listener for task {}: {}", taskId, e.getMessage());
                }
            });
        }
    }

    private void scheduleProgressCleanup(String taskId) {
        // In a real application, this could be done with a scheduled executor
        // For now, we'll just clean up immediately for terminal states
        // In production, you might want to keep the data for a while for debugging
        
        // Clean up after 5 minutes for terminal states
        java.util.concurrent.CompletableFuture.delayedExecutor(5, java.util.concurrent.TimeUnit.MINUTES)
            .execute(() -> cleanupProgress(taskId));
    }

    /**
     * Inner class to hold progress information
     */
    private static class TaskProgressInfo {
        private final String taskId;
        private TaskStatus status = TaskStatus.PENDING;
        private Integer progress = 0;
        private String message;
        private String currentStep;
        private String errorMessage;
        private LocalDateTime startTime;
        private LocalDateTime lastUpdated;
        private Long estimatedTimeRemainingMs;

        public TaskProgressInfo(String taskId) {
            this.taskId = taskId;
            this.lastUpdated = LocalDateTime.now();
        }

        // Getters and setters
        public String getTaskId() { return taskId; }
        
        public TaskStatus getStatus() { return status; }
        public void setStatus(TaskStatus status) { this.status = status; }
        
        public Integer getProgress() { return progress; }
        public void setProgress(Integer progress) { this.progress = progress; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getCurrentStep() { return currentStep; }
        public void setCurrentStep(String currentStep) { this.currentStep = currentStep; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getLastUpdated() { return lastUpdated; }
        public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }
        
        public Long getEstimatedTimeRemainingMs() { return estimatedTimeRemainingMs; }
        public void setEstimatedTimeRemainingMs(Long estimatedTimeRemainingMs) { 
            this.estimatedTimeRemainingMs = estimatedTimeRemainingMs; 
        }
    }
}
