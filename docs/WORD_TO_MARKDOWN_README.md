# Word to Markdown Converter

一个高性能、功能完整的 Word 文档到 Markdown 格式转换器，支持各种 Word 版本格式，保证文档结构和内容的精确转换。

## 功能特性

### 🚀 核心功能
- **多格式支持**: 支持 `.doc` 和 `.docx` 格式
- **高精度转换**: 保持文档结构和内容的完整性
- **高性能处理**: 优化的内存管理和流式处理
- **可配置选项**: 灵活的转换配置和模式选择

### 📝 支持的元素
- **文本格式**: 粗体、斜体、下划线、代码格式
- **文档结构**: 标题（H1-H6）、段落、列表
- **表格**: 完整的表格结构转换
- **图片**: 图片提取和链接生成
- **链接**: 超链接保持
- **脚注**: 脚注转换为文末引用

### ⚙️ 转换模式
- **STRICT 模式**: 严格转换，遇到错误时快速失败
- **LOOSE 模式**: 宽松转换，跳过不支持的元素继续处理

## 快速开始

### 基本使用

```java
import com.talkweb.ai.converter.util.WordToMarkdownConverter;

// 简单转换
File wordFile = new File("document.docx");
String markdown = WordToMarkdownConverter.convert(wordFile);
System.out.println(markdown);
```

### 高级配置

```java
import com.talkweb.ai.converter.util.*;

// 自定义转换选项
WordConversionOptions options = new WordConversionOptions()
    .setExtractImages(true)
    .setImageOutputDir("images/")
    .setPreserveTableFormatting(true)
    .setConvertFootnotes(true)
    .setPreserveTextFormatting(true);

// 使用严格模式转换
String markdown = WordToMarkdownConverter.convert(
    wordFile, 
    WordConversionMode.STRICT, 
    options
);
```

### 预设配置

```java
// 高保真转换（保留所有格式）
WordConversionOptions highFidelity = WordConversionOptions.highFidelity();
String markdown = WordToMarkdownConverter.convert(wordFile, highFidelity);

// 基础转换（快速，最小格式）
WordConversionOptions basic = WordConversionOptions.basic();
String markdown = WordToMarkdownConverter.convert(wordFile, basic);
```

## 配置选项

### WordConversionOptions 详细配置

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `extractImages` | boolean | true | 是否提取图片到文件 |
| `imageOutputDir` | String | "images/" | 图片输出目录 |
| `preserveTableFormatting` | boolean | true | 是否保持表格格式 |
| `convertFootnotes` | boolean | true | 是否转换脚注 |
| `includePageBreaks` | boolean | false | 是否包含分页符 |
| `includeComments` | boolean | false | 是否包含评论 |
| `preserveHyperlinks` | boolean | true | 是否保持超链接 |
| `preferredImageFormat` | ImageFormat | PNG | 首选图片格式 |
| `maxImageWidth` | int | 800 | 最大图片宽度 |
| `maxImageHeight` | int | 600 | 最大图片高度 |
| `generateImageAltText` | boolean | true | 是否生成图片 alt 文本 |
| `preserveListFormatting` | boolean | true | 是否保持列表格式 |
| `convertHeadersFooters` | boolean | false | 是否转换页眉页脚 |
| `preserveTextFormatting` | boolean | true | 是否保持文本格式 |
| `skipEmptyParagraphs` | boolean | true | 是否跳过空段落 |

### 图片格式选项

```java
public enum ImageFormat {
    PNG,        // PNG 格式
    JPEG,       // JPEG 格式  
    GIF,        // GIF 格式
    BMP,        // BMP 格式
    ORIGINAL    // 保持原始格式
}
```

## 架构设计

### 核心组件

```
WordToMarkdownConverter (主转换器)
├── WordConversionContext (转换上下文)
├── WordConversionOptions (配置选项)
└── converter/ (元素转换器)
    ├── WordElementConverter (转换器接口)
    ├── WordParagraphConverter (段落转换器)
    ├── WordTableConverter (表格转换器)
    └── WordImageConverter (图片转换器)
```

### 转换流程

1. **文件格式检测**: 自动识别 .doc 或 .docx 格式
2. **文档解析**: 使用 Apache POI 解析 Word 文档
3. **元素转换**: 使用专门的转换器处理不同元素
4. **格式优化**: 清理和优化输出的 Markdown
5. **资源处理**: 提取图片等资源文件

## 性能优化

### 内存管理
- 流式处理大文档，避免全部加载到内存
- 使用 MarkdownBuilder 进行高效字符串构建
- 及时释放 POI 对象资源

### 缓存策略
- 样式信息缓存
- 图片处理缓存
- 重复内容检测

### 错误恢复
- 部分转换失败时的降级策略
- 损坏文档的处理
- 不支持元素的跳过机制

## 示例代码

### 完整示例

```java
public class WordToMarkdownExample {
    public static void main(String[] args) throws IOException {
        File wordFile = new File("sample.docx");
        
        // 基础转换
        String basicMarkdown = WordToMarkdownConverter.convert(wordFile);
        
        // 高保真转换
        WordConversionOptions options = WordConversionOptions.highFidelity()
            .setImageOutputDir("extracted_images/")
            .setPreferredImageFormat(WordConversionOptions.ImageFormat.PNG);
            
        String highFidelityMarkdown = WordToMarkdownConverter.convert(
            wordFile, 
            WordConversionMode.LOOSE, 
            options
        );
        
        // 保存结果
        Files.write(Paths.get("output.md"), highFidelityMarkdown.getBytes());
    }
}
```

### 批量转换

```java
public void convertMultipleFiles(List<File> wordFiles) {
    WordConversionOptions options = WordConversionOptions.basic();
    
    for (File file : wordFiles) {
        try {
            String markdown = WordToMarkdownConverter.convert(file, options);
            String outputName = file.getName().replaceAll("\\.(docx?)", ".md");
            Files.write(Paths.get(outputName), markdown.getBytes());
            
        } catch (IOException e) {
            logger.error("Failed to convert: " + file.getName(), e);
        }
    }
}
```

## 依赖要求

### Maven 依赖

```xml
<dependencies>
    <!-- Apache POI for Word document processing -->
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>5.4.1</version>
    </dependency>
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-scratchpad</artifactId>
        <version>5.4.1</version>
    </dependency>
    
    <!-- Logging -->
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>2.0.9</version>
    </dependency>
</dependencies>
```

### 系统要求
- Java 17 或更高版本
- 内存: 建议 512MB 以上（取决于文档大小）
- 磁盘空间: 用于图片提取的临时空间

## 最佳实践

### 1. 选择合适的转换模式
- 对于生产环境，推荐使用 LOOSE 模式以确保稳定性
- 对于质量要求极高的场景，可以使用 STRICT 模式

### 2. 优化图片处理
```java
WordConversionOptions options = new WordConversionOptions()
    .setExtractImages(true)
    .setPreferredImageFormat(WordConversionOptions.ImageFormat.PNG)
    .setMaxImageWidth(1200)
    .setMaxImageHeight(800);
```

### 3. 处理大文档
```java
// 对于大文档，建议关闭一些非必要功能
WordConversionOptions options = new WordConversionOptions()
    .setExtractImages(false)
    .setConvertFootnotes(false)
    .setIncludeComments(false);
```

### 4. 错误处理
```java
try {
    String markdown = WordToMarkdownConverter.convert(file, WordConversionMode.STRICT);
} catch (Exception e) {
    // 降级到宽松模式
    String markdown = WordToMarkdownConverter.convert(file, WordConversionMode.LOOSE);
}
```

## 限制和注意事项

### 当前限制
1. **复杂表格**: 合并单元格的复杂表格可能转换不完美
2. **图表**: 嵌入的图表会被转换为图片
3. **宏和脚本**: 不支持 VBA 宏转换
4. **复杂布局**: 多栏布局可能丢失格式

### 兼容性
- ✅ Word 2007+ (.docx)
- ✅ Word 97-2003 (.doc)
- ✅ 中文文档支持
- ✅ 跨平台支持 (Windows, macOS, Linux)

## 故障排除

### 常见问题

**Q: 转换后的 Markdown 格式不正确？**
A: 检查原始 Word 文档的样式设置，确保使用标准的标题样式。

**Q: 图片没有被提取？**
A: 确保设置了 `extractImages(true)` 并且有写入权限到图片输出目录。

**Q: 表格格式丢失？**
A: 启用 `preserveTableFormatting(true)` 选项，或考虑使用 HTML 表格格式。

**Q: 内存不足错误？**
A: 对于大文档，尝试关闭图片提取或使用基础转换选项。

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 更新日志

### v1.0.0 (2024-06-22)
- ✨ 初始版本发布
- ✅ 支持 .doc 和 .docx 格式
- ✅ 基础文本格式转换
- ✅ 表格转换支持
- ✅ 图片提取功能
- ✅ 可配置转换选项
- ✅ 严格和宽松转换模式
