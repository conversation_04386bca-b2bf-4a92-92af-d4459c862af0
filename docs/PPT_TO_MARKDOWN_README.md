# PowerPoint to Markdown Converter

## 概述

PowerPoint to Markdown Converter 是一个高性能、高兼容性的 PPT/PPTX 文档转换器，能够将 Microsoft PowerPoint 演示文稿转换为 Markdown 格式，同时保持文档结构和内容的完整性。

## 主要特性

### 🔧 全面的格式兼容性
- **PPT 格式支持**: 完全支持 Microsoft Office 97-2003 (.ppt) 格式
- **PPTX 格式支持**: 完全支持 Microsoft Office 2007+ (.pptx) 格式  
- **PPTM 格式支持**: 支持带宏的 PowerPoint 文件 (.pptm) 格式
- **版本兼容性**: 兼容不同版本的 Office 文档格式

### 📄 内容提取能力
- **文本内容**: 提取幻灯片标题、正文文本、文本框内容
- **表格处理**: 完整提取表格数据并转换为 Markdown 表格格式
- **图片提取**: 支持图片提取、调整大小和格式转换
- **形状内容**: 提取各种形状中的文本内容
- **演讲者备注**: 可选择性包含演讲者备注内容
- **元数据信息**: 提取演示文稿的元数据信息

### 🎯 结构保持
- **幻灯片层次**: 保持幻灯片的逻辑顺序和层次结构
- **标题层级**: 自动识别并转换标题层级
- **列表格式**: 智能转换项目符号和编号列表
- **文本格式**: 保持粗体、斜体等基本文本格式

### ⚡ 性能优化
- **内存管理**: 优化内存使用，支持大型演示文稿
- **缓存机制**: 智能缓存提高处理效率
- **流式处理**: 支持流式处理模式
- **并发安全**: 线程安全的实现

### 🛠️ 配置选项
- **转换模式**: 支持严格模式和宽松模式
- **内容选择**: 可配置提取哪些类型的内容
- **图片处理**: 可配置图片提取、大小调整和格式
- **错误处理**: 灵活的错误处理策略

## 快速开始

### 基本使用

```java
import com.talkweb.ai.converter.util.PptToMarkdownConverter;
import java.io.File;

// 简单转换
File pptFile = new File("presentation.pptx");
String markdown = PptToMarkdownConverter.convert(pptFile);
System.out.println(markdown);
```

### 使用自定义配置

```java
import com.talkweb.ai.converter.util.ppt.PptConversionConfig;

// 创建自定义配置
PptConversionConfig config = new PptConversionConfig()
    .setIncludeMetadata(true)
    .setIncludeSpeakerNotes(true)
    .setExtractImages(true)
    .setExtractTables(true)
    .setImageFormat("png")
    .setMaxImageWidth(800);

// 使用配置进行转换
String markdown = PptToMarkdownConverter.convert(pptFile, config);
```

### 作为插件使用

```java
import com.talkweb.ai.converter.core.ConversionResult;

// 创建转换器实例
PptToMarkdownConverter converter = new PptToMarkdownConverter();

// 启动插件
converter.start();

// 执行转换
ConversionResult result = converter.convert(pptFile);

// 处理结果
if (result.getStatus() == ConversionResult.Status.SUCCESS) {
    System.out.println("转换成功!");
    System.out.println("输出文件: " + result.getOutputPath());
    System.out.println("内容: " + result.getContent());
}

// 停止插件
converter.stop();
```

## 配置选项详解

### 内容提取配置

```java
PptConversionConfig config = new PptConversionConfig()
    // 是否包含演示文稿元数据
    .setIncludeMetadata(true)
    
    // 是否包含演讲者备注
    .setIncludeSpeakerNotes(false)
    
    // 是否提取图片
    .setExtractImages(true)
    
    // 是否提取表格
    .setExtractTables(true)
    
    // 是否提取图表（作为图片）
    .setExtractCharts(false)
    
    // 是否包含幻灯片编号
    .setIncludeSlideNumbers(true)
    
    // 是否包含隐藏幻灯片
    .setIncludeHiddenSlides(false);
```

### 输出格式配置

```java
PptConversionConfig config = new PptConversionConfig()
    // 转换模式：严格模式遇到错误会停止，宽松模式会跳过错误继续处理
    .setStrictMode(false)
    
    // 是否保持文本格式（粗体、斜体等）
    .setPreserveFormatting(true)
    
    // 是否生成目录
    .setGenerateTableOfContents(false)
    
    // 图片输出目录
    .setImageOutputDirectory("images")
    
    // 图片格式
    .setImageFormat("png")
    
    // 图片最大宽度和高度
    .setMaxImageWidth(800)
    .setMaxImageHeight(600);
```

### 文本处理配置

```java
PptConversionConfig config = new PptConversionConfig()
    // 是否标准化空白字符
    .setNormalizeWhitespace(true)
    
    // 是否将项目符号转换为 Markdown 格式
    .setConvertBulletsToMarkdown(true)
    
    // 是否保持文本框
    .setPreserveTextBoxes(true)
    
    // 是否从形状中提取文本
    .setExtractTextFromShapes(true);
```

### 性能配置

```java
PptConversionConfig config = new PptConversionConfig()
    // 是否启用缓存
    .setEnableCaching(true)
    
    // 最大缓存大小
    .setMaxCacheSize(100)
    
    // 是否使用流式处理（适用于大文件）
    .setStreamProcessing(false)
    
    // 处理超时时间（秒）
    .setProcessingTimeout(300);
```

## 预设配置

### 高保真配置
适用于需要完整保留所有内容的场景：

```java
PptConversionConfig config = PptConversionConfig.createHighFidelityConfig();
```

### 性能优化配置
适用于需要快速处理的场景：

```java
PptConversionConfig config = PptConversionConfig.createPerformanceConfig();
```

### 严格模式配置
适用于需要严格错误检查的场景：

```java
PptConversionConfig config = PptConversionConfig.createStrictConfig();
```

## 输出示例

### 基本幻灯片转换

输入 PowerPoint 幻灯片：
- 标题：产品介绍
- 内容：我们的产品具有以下特点...

输出 Markdown：
```markdown
## Slide 1

### 产品介绍

我们的产品具有以下特点：
- 高性能
- 易使用
- 可扩展

---
```

### 表格转换

PowerPoint 表格会转换为标准的 Markdown 表格格式：

```markdown
| 功能 | 描述 | 状态 |
|------|------|------|
| 文本提取 | 提取所有文本内容 | ✅ 完成 |
| 图片处理 | 提取并转换图片 | ✅ 完成 |
| 表格转换 | 转换为 Markdown 表格 | ✅ 完成 |
```

### 图片处理

图片会被提取到指定目录，并在 Markdown 中生成引用：

```markdown
![产品截图](images/presentation_slide_1_image_0.png "产品主界面")
```

## 错误处理

### 宽松模式（默认）
- 遇到错误时跳过有问题的元素
- 继续处理其他内容
- 在输出中添加错误占位符

### 严格模式
- 遇到任何错误立即停止处理
- 抛出详细的异常信息
- 适用于需要完整转换的场景

```java
try {
    PptConversionConfig strictConfig = new PptConversionConfig().setStrictMode(true);
    String result = PptToMarkdownConverter.convert(file, strictConfig);
} catch (RuntimeException e) {
    System.err.println("转换失败: " + e.getMessage());
}
```

## 性能建议

### 大文件处理
```java
PptConversionConfig config = new PptConversionConfig()
    .setStreamProcessing(true)  // 启用流式处理
    .setEnableCaching(false)    // 禁用缓存以节省内存
    .setExtractImages(false);   // 跳过图片提取以提高速度
```

### 批量处理
```java
PptToMarkdownConverter converter = new PptToMarkdownConverter();
converter.start();

for (File file : pptFiles) {
    try {
        ConversionResult result = converter.convert(file);
        // 处理结果...
    } catch (Exception e) {
        // 处理错误...
    }
}

converter.stop();
```

## 依赖要求

- Java 17+
- Apache POI 5.4.1+
- SLF4J 日志框架

## 注意事项

1. **内存使用**: 大型演示文稿可能消耗较多内存，建议适当调整 JVM 堆大小
2. **图片格式**: 支持 PNG、JPG、GIF、BMP 格式的图片提取
3. **文本编码**: 自动处理各种字符编码，确保中文等非 ASCII 字符正确显示
4. **版本兼容**: 建议使用最新版本的 Apache POI 以获得最佳兼容性

## 故障排除

### 常见问题

**Q: 转换后的 Markdown 中文显示乱码？**
A: 确保输出文件使用 UTF-8 编码保存。

**Q: 图片无法正确提取？**
A: 检查图片输出目录是否有写入权限，确保图片格式受支持。

**Q: 表格格式不正确？**
A: 检查原始表格是否包含合并单元格，转换器会尽力处理但可能需要手动调整。

**Q: 处理大文件时内存不足？**
A: 启用流式处理模式，禁用缓存，或增加 JVM 堆内存。

### 日志配置

启用详细日志以便调试：

```java
PptConversionConfig config = new PptConversionConfig()
    .setLogDetailedErrors(true)
    .setGenerateErrorReport(true);
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持 PPT/PPTX/PPTM 格式
- 完整的内容提取功能
- 可配置的转换选项
- 高性能处理能力
