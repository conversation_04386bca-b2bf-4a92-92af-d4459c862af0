# 转换器 API 参考

## 核心接口

### DocumentConverter

文档转换操作的主要接口。

#### 方法

##### convert(File input, ConversionContext context)

将文档文件转换为 Markdown 格式。

**参数：**
- `input` (File): 输入文档文件
- `context` (ConversionContext): 转换的配置和选项

**返回值：**
- `ConversionResult`: 转换操作的结果

**抛出异常：**
- `ConversionException`: 如果转换失败

**示例：**
```java
DocumentConverter converter = new PdfToMarkdownConverter();
ConversionContext context = ConversionContext.builder()
    .option("extractImages", true)
    .option("preserveFormatting", true)
    .build();

ConversionResult result = converter.convert(inputFile, context);
if (result.isSuccess()) {
    String markdown = result.getContent();
    // 处理 markdown 内容
}
```

##### getSupportedExtensions()

返回此转换器支持的文件扩展名。

**返回值：**
- `Set<String>`: 支持的文件扩展名集合（不包含点号）

**示例：**
```java
Set<String> extensions = converter.getSupportedExtensions();
// 返回: ["pdf", "PDF"]
```

##### getCapabilities()

返回此转换器的功能。

**返回值：**
- `ConversionCapabilities`: 转换器功能描述

**示例：**
```java
ConversionCapabilities caps = converter.getCapabilities();
boolean supportsImages = caps.hasFeature(ConversionCapabilities.Features.IMAGES);
```

##### getMetadata()

返回此转换器的元数据。

**返回值：**
- `ConversionMetadata`: 转换器元数据，包括名称、版本、描述

## 配置类

### ConversionContext

为转换操作提供配置选项。

#### 建造者模式

```java
ConversionContext context = ConversionContext.builder()
    .option("key", value)
    .mode("STRICT")
    .build();
```

#### 常用选项

| 选项 | 类型 | 描述 | 默认值 |
|--------|------|-------------|---------|
| `conversionMode` | String | 转换模式 (STRICT/LOOSE) | "LOOSE" |
| `extractImages` | Boolean | 从文档中提取图片 | false |
| `preserveFormatting` | Boolean | 保留原始格式 | true |
| `includeMetadata` | Boolean | 包含文档元数据 | false |
| `maxFileSize` | Integer | 最大文件大小（字节） | 100MB |
| `outputEncoding` | String | 输出文本编码 | "UTF-8" |

### ConversionOptions

使用类型安全管理转换选项。

#### 方法

##### getStringOption(String key, String defaultValue)

获取字符串选项值。

**示例：**
```java
String mode = options.getStringOption("conversionMode", "LOOSE");
```

##### getBooleanOption(String key, boolean defaultValue)

获取布尔选项值。

**示例：**
```java
boolean extractImages = options.getBooleanOption("extractImages", false);
```

##### getIntOption(String key, int defaultValue)

获取整数选项值。

**示例：**
```java
int maxSize = options.getIntOption("maxFileSize", 100 * 1024 * 1024);
```

## 结果类

### ConversionResult

表示转换操作的结果。

#### 属性

- `status` (Status): SUCCESS、FAILURE 或 PARTIAL
- `inputPath` (String): 输入文件路径
- `outputPath` (String): 输出文件路径（如果适用）
- `content` (String): 转换后的内容
- `message` (String): 状态消息或错误描述

#### 方法

##### isSuccess()

检查转换是否成功。

**返回值：**
- `boolean`: 如果转换成功则返回 true

##### getContent()

获取转换后的内容。

**返回值：**
- `String`: 转换后的 Markdown 内容

##### getMessage()

获取状态消息。

**返回值：**
- `String`: 状态消息或错误描述

## 功能类

### ConversionCapabilities

描述转换器能做什么。

#### Features 枚举

```java
public enum Features {
    HEADINGS,    // 支持标题转换
    TABLES,      // 支持表格转换
    LISTS,       // 支持列表转换
    IMAGES,      // 支持图片提取
    METADATA     // 支持元数据提取
}
```

#### 方法

##### hasFeature(Features feature)

检查转换器是否支持特定功能。

**示例：**
```java
boolean supportsTables = capabilities.hasFeature(ConversionCapabilities.Features.TABLES);
```

##### getCapability(String name, Class<T> type, T defaultValue)

获取特定功能值。

**示例：**
```java
Integer maxFileSize = capabilities.getCapability("maxFileSize", Integer.class, 100 * 1024 * 1024);
```

## 性能类

### PerformanceOptimizer

为转换提供性能优化。

#### 方法

##### optimizeForLargeFile(long fileSizeBytes)

获取大文件的优化设置。

**返回值：**
- `ConversionOptimizationSettings`: 推荐设置

**示例：**
```java
PerformanceOptimizer optimizer = new PerformanceOptimizer();
ConversionOptimizationSettings settings = optimizer.optimizeForLargeFile(fileSize);

if (settings.isUseStreamProcessing()) {
    // 使用流处理器
}
```

### StreamingProcessor

为大文件提供内存高效的处理。

#### 方法

##### processFileStreaming(File inputFile, Function<byte[], String> chunkProcessor, File outputFile)

以流式方式处理文件。

**示例：**
```java
StreamingProcessor processor = new StreamingProcessor();
processor.processFileStreaming(inputFile, chunk -> {
    // 处理每个块
    return processChunk(chunk);
}, outputFile);
```

## 异常处理

### ConversionException

转换错误的主要异常类型。

#### 常见场景

```java
try {
    ConversionResult result = converter.convert(file, context);
} catch (ConversionException e) {
    switch (e.getErrorCode()) {
        case FILE_NOT_FOUND:
            // 处理文件未找到
            break;
        case UNSUPPORTED_FORMAT:
            // 处理不支持的格式
            break;
        case CONVERSION_FAILED:
            // 处理转换失败
            break;
    }
}
```

## 缓存

### ConversionCacheManager

管理转换结果的缓存。

#### 方法

##### getCache(String name, CacheConfig config)

获取或创建缓存实例。

**示例：**
```java
CacheConfig config = CacheConfig.builder()
    .type(CacheType.LRU)
    .maxSize(1000)
    .expireAfterWrite(30, TimeUnit.MINUTES)
    .build();

Cache<String, ConversionResult> cache = ConversionCacheManager.getCache("myCache", config);
```

##### clearAllCaches()

清除所有缓存。

##### getAllCacheStatistics()

获取所有缓存的统计信息。

**返回值：**
- `CacheStatistics`: 合并的统计信息

## 使用示例

### 基本转换

```java
// 创建转换器
PdfToMarkdownConverter converter = new PdfToMarkdownConverter();

// 创建上下文
ConversionContext context = ConversionContext.builder()
    .option("extractImages", false)
    .build();

// 转换文件
ConversionResult result = converter.convert(pdfFile, context);

if (result.isSuccess()) {
    String markdown = result.getContent();
    Files.writeString(Paths.get("output.md"), markdown);
}
```

### 高级配置

```java
ConversionContext context = ConversionContext.builder()
    .option("conversionMode", "STRICT")
    .option("extractImages", true)
    .option("imageOutputDirectory", "images/")
    .option("preserveFormatting", true)
    .option("includeMetadata", true)
    .option("maxFileSize", 50 * 1024 * 1024) // 50MB
    .build();
```

### 性能优化

```java
PerformanceConfigurationService perfService = new PerformanceConfigurationService();

if (perfService.shouldUseStreamingProcessing(inputFile)) {
    StreamingProcessor processor = perfService.createOptimizedStreamingProcessor(inputFile);
    // 使用流处理器
} else {
    // 使用常规处理
}
```

### 错误处理

```java
try {
    ConversionResult result = converter.convert(file, context);
    
    if (result.getStatus() == ConversionResult.Status.PARTIAL) {
        logger.warn("部分转换: {}", result.getMessage());
    }
    
} catch (ConversionException e) {
    logger.error("转换失败: {}", e.getMessage(), e);
    
    // 检查是否为可恢复错误
    if (e.isRecoverable()) {
        // 使用不同设置重试
    }
}
```
