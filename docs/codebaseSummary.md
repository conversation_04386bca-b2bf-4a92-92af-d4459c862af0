# 代码库概要

本文档提供智能文档索引器项目的高层设计、架构和代码结构说明。

## 1. 系统架构

本系统采用基于插件的模块化架构，以确保高度的可扩展性和可维护性。核心是处理引擎和插件注册表，通过 SPI 机制支持动态扩展各种文档处理能力。

### 1.1 主要组件

#### 核心框架组件
- **`DocConverterApplication`**: Spring Boot 主应用程序，负责系统启动和生命周期管理
- **`PluginManager`**: 插件管理器，负责插件的发现、加载、启动和停止
- **`CommandLineInterface`**: 使用 Picocli 构建，负责解析和验证用户输入的命令和参数，支持批处理配置
- **`FileScanner`**: 遍历指定的输入目录，支持递归扫描与多格式过滤，并生成待处理文件队列
- **`ProcessingPipeline`**: 文档处理流水线，协调不同阶段的处理插件，支持前处理、主处理和后处理阶段

#### 插件系统组件
- **`DocumentProcessor` (接口)**: 定义所有处理器的标准契约，包含处理方法和元数据支持
- **`Plugin` (接口)**: 插件生命周期管理接口，支持启动、停止、配置等操作
- **`PluginRegistry`**: 插件注册表，负责发现、加载和管理插件的生命周期

#### 文档转换器实现 (已完成)
- **`TxtToMarkdownConverter`**: 处理纯文本文档转换
- **`HtmlToMarkdownConverter`**: 处理 HTML 文档转换，支持增强元素处理
- **`PdfToMarkdownConverter`**: 处理 PDF 文档转换，支持页面分割和结构保留
- **`ExcelToMarkdownConverter`**: 处理 Excel 文档转换，支持多工作表和合并单元格
- **`WordToMarkdownConverter`**: 处理 Word 文档转换，支持多种格式和样式保留
- **`PptToMarkdownConverter`**: 处理 PowerPoint 文档转换，支持幻灯片内容提取

#### AI 增强服务 (已完成)
- **`AiEnhancedDocumentProcessor`**: AI 增强文档处理器，提供智能分析能力
- **`DocumentSummaryService`**: 文档摘要生成服务，支持关键点提取和内容分析
- **`DocumentEmbeddingService`**: 文档向量嵌入服务，支持语义表示和文档分块

#### 支撑服务组件
- **`FileWatcherService`**: 文件监控服务，支持实时文件变更检测
- **`ConcurrentProcessingService`**: 并发处理服务，支持虚拟线程和批量处理
- **`CacheManager`**: 缓存管理服务，支持TTL和容量限制
- **`PluginWatcher`**: 插件热加载监控服务

### 1.2 包结构

### 1.2 建议包结构

```
com.talkweb.ai.converter 
├── cli // 命令行接口与参数解析 
├── core // 核心处理引擎与插件框架 
├── pipeline // 处理流水线 
├── plugin // 插件机制 
├── config // 配置管理 
├── processor // 各类文档处理器 
├── service // 业务服务 
├── model // 数据模型 
├── util // 工具类 
└── DocConverterApplication.java // Spring Boot 启动类
```

### 1.3 关键交互流程

1. **初始化流程**:
   - `DocConverterApplication` 启动，初始化 Spring 上下文
   - `PluginRegistry` 扫描并加载可用插件
   - `CommandLineInterface` 解析命令行参数或配置文件

2. **文档处理流程**:
   - `FileScanner` 扫描并过滤文件，创建处理队列
   - `ProcessingPipeline` 协调处理流程：
     - 前处理阶段：文档格式检测、资源限制检查
     - 主处理阶段：调用合适的 `DocumentProcessor` 提取内容
     - 后处理阶段：元数据提取、内容规范化

3. **内容增强流程**:
   - 通过 `AiService` 调用 AI 模型进行文档分析：
     - 自动摘要生成
     - 关键实体提取
     - 知识图谱构建

4. **向量化流程**:
   - `ChunkingStrategy` 将文档内容分块
   - `VectorEmbeddingService` 生成文本块的向量表示
   - `VectorDatabaseConnector` 将向量存入向量数据库

5. **增量处理流程**:
   - 检测文档变更
   - 仅处理变更部分
   - 更新索引与向量库

## 2. 技术栈

- **核心框架**: Java 21, Spring Boot 3.5.3
- **AI 支持**: Spring AI 1.0 (已集成)
- **命令行**: Picocli 4.7.5
- **文档处理**: Apache POI, PDFBox, Jsoup, Tesseract OCR, CommonMark
- **并发处理**: Java Virtual Threads, CompletableFuture
- **监控**: Micrometer, Prometheus, Grafana
- **测试**: JUnit 5, AssertJ, Mockito
- **构建工具**: Maven 3.9+

## 3. 当前开发状态

### 3.1 项目统计
- **Java 文件数量**: 137 个
- **代码总行数**: 30,214 行
- **测试覆盖**: 271 个测试，通过率 99.6% (270/271)
- **项目风险等级**: 中等 (从高风险降级)

### 3.2 完成状态
- ✅ **阶段一: 核心框架搭建** - 100% 完成
  - 项目初始化和环境配置
  - 插件架构框架和核心接口
  - 命令行接口实现
  - 核心扫描与处理引擎

- 🚧 **阶段二: 扩展文档格式支持** - 85% 完成
  - ✅ TXT 处理器插件 - 已完成
  - ✅ HTML 处理器插件 - 已完成 (增强版)
  - ✅ PDF 处理器插件 - 已完成 (增强版)
  - ✅ Excel 处理器插件 - 已完成 (增强版)
  - ✅ Word 处理器插件 - 已完成
  - ✅ PowerPoint 处理器插件 - 已完成
  - ✅ 元数据提取框架 - 已完成
  - ✅ 错误处理与重试机制 - 已完成

- ✅ **AI 增强功能** - 100% 完成
  - Spring AI 集成和配置
  - 文档摘要生成服务
  - 文档向量嵌入服务
  - AI 增强文档处理器
  - 完整测试覆盖

### 3.3 增强功能实现
- **HTML 转换器增强**: 支持 Bootstrap、Tailwind CSS、jQuery、Ant Design 等框架
- **PDF 转换器增强**: 支持页面分割、加密文档处理、结构保留
- **Excel 转换器增强**: 支持多格式兼容、合并单元格、公式处理
- **表格处理优化**: 性能缓存机制、智能框架识别
- **并发处理**: 虚拟线程支持、批量处理优化

## 4. 近期重大变更

### 4.1 AI 功能集成完成 (2025-06-22)
- 完成 DocumentSummaryService、DocumentEmbeddingService 实现
- 实现 AiEnhancedDocumentProcessor 智能处理器
- 测试通过率达到 99.6%，项目风险等级从高降至中等

### 4.2 文档转换器全面增强
- 所有主要格式转换器完成增强版实现
- 添加框架兼容性支持 (Bootstrap、Tailwind 等)
- 实现高性能缓存和优化机制
- 完善错误处理和恢复策略

### 4.3 支撑服务完善
- 实现文件监控服务 (FileWatcherService)
- 添加插件热加载功能 (PluginWatcher)
- 完成并发处理服务 (ConcurrentProcessingService)
- 实现缓存管理服务 (CacheManager)