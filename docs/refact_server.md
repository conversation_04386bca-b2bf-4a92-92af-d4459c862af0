# 文档转换器Web服务重构设计方案

## 1. 项目概述

### 1.1 需求背景
将现有的CLI文档转换器扩展为Web服务，支持通过REST API和Web界面进行文档转换任务管理。

### 1.2 核心需求
1. 增加`-server`命令行参数，以Web服务方式运行
2. 集成Spring Boot Web，提供REST API接口
3. 实现异步任务管理系统
4. 提供任务进度查询和取消功能
5. 开发管理UI界面
6. 集成Swagger API文档
7. 优化文件处理逻辑（临时文件管理）

## 2. 技术架构设计

### 2.1 技术栈选择
- **Web框架**: Spring Boot Web 3.5.2
- **API文档**: SpringDoc OpenAPI 3 (替代Swagger)
- **数据持久化**: Spring Boot Data JPA + H2数据库
- **实时通信**: Spring Boot WebSocket
- **模板引擎**: Thymeleaf
- **前端框架**: Bootstrap 5 + jQuery
- **异步处理**: Spring @Async + CompletableFuture
- **文件上传**: Spring Boot Multipart

### 2.2 架构模式
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CLI Mode      │    │   Web Mode      │    │  Shared Core    │
│                 │    │                 │    │                 │
│ - ConvertCommand│    │ - REST API      │    │ - PluginManager │
│ - ServerCommand │    │ - WebSocket     │    │ - Processors    │
│ - Picocli       │    │ - Thymeleaf UI  │    │ - Services      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.3 运行模式
- **CLI模式**: 传统命令行方式，保持现有功能不变
- **Web模式**: 通过`-server`参数启动，提供Web服务
- **混合模式**: 支持同时运行CLI和Web服务

## 3. REST API设计

### 3.1 API端点规划
```
POST   /api/v1/tasks              # 创建转换任务
GET    /api/v1/tasks              # 查询任务列表（分页）
GET    /api/v1/tasks/{taskId}     # 查询单个任务详情
DELETE /api/v1/tasks/{taskId}     # 取消任务
GET    /api/v1/tasks/{taskId}/progress  # 获取任务进度
GET    /api/v1/tasks/{taskId}/download  # 下载转换结果

POST   /api/v1/files/upload       # 上传待转换文件
GET    /api/v1/files/{fileId}/download  # 下载文件

GET    /api/v1/system/status      # 系统状态
GET    /api/v1/system/plugins     # 插件列表
GET    /api/v1/system/formats     # 支持的格式列表
```

### 3.2 数据模型设计
```java
// 转换任务实体
@Entity
public class ConversionTask {
    private String taskId;           // 任务ID
    private String fileName;         // 原文件名
    private String filePath;         // 文件路径
    private String targetFileName;   // 目标文件名
    private String targetPath;       // 目标路径
    private TaskStatus status;       // 任务状态
    private Integer progress;        // 进度百分比
    private String errorMessage;     // 错误信息
    private LocalDateTime createdAt; // 创建时间
    private LocalDateTime startedAt; // 开始时间
    private LocalDateTime completedAt; // 完成时间
}

// 任务状态枚举
public enum TaskStatus {
    PENDING,     // 等待处理
    PROCESSING,  // 处理中
    COMPLETED,   // 已完成
    FAILED,      // 失败
    CANCELLED    // 已取消
}
```

### 3.3 请求/响应DTO
```java
// 创建任务请求
public class ConversionRequest {
    private MultipartFile file;      // 上传文件
    private String targetFileName;   // 可选目标文件名
    private Map<String, Object> options; // 转换选项
}

// 任务进度响应
public class TaskProgressResponse {
    private String taskId;
    private TaskStatus status;
    private Integer progress;
    private String message;
    private LocalDateTime lastUpdated;
}
```

## 4. 任务管理系统设计

### 4.1 任务生命周期
```
PENDING → PROCESSING → COMPLETED
   ↓           ↓           ↑
CANCELLED ← FAILED ←──────┘
```

### 4.2 文件处理逻辑
1. **文件上传**: 上传到临时目录 `temp/uploads/{taskId}/`
2. **任务创建**: 创建任务记录，状态为PENDING
3. **异步处理**:
   - 更新状态为PROCESSING
   - 处理过程中文件名: `{source}.{ext}.md.tmp`
   - 完成后重命名为: `{target}.md`
4. **结果管理**: 转换结果存储在 `temp/results/{taskId}/`
5. **清理机制**: 定时清理过期任务和临时文件

### 4.3 核心服务类
```java
@Service
public class ConversionTaskService {
    // 任务CRUD操作
    // 任务状态管理
    // 任务查询和过滤
}

@Service
public class AsyncConversionService {
    // 异步任务处理
    // 进度跟踪
    // 错误处理
}

@Service
public class FileStorageService {
    // 文件上传下载
    // 临时文件管理
    // 文件安全检查
}

@Service
public class TaskProgressService {
    // 进度跟踪
    // 状态更新
    // WebSocket通知
}
```

## 5. WebSocket实时通信

### 5.1 WebSocket端点
```
/ws/tasks/{taskId}  # 任务状态实时推送
/ws/system         # 系统状态推送
```

### 5.2 消息格式
```json
{
  "type": "TASK_STATUS_UPDATE",
  "taskId": "task-123",
  "status": "PROCESSING",
  "progress": 45,
  "message": "正在处理第3页...",
  "timestamp": "2025-06-23T10:30:00Z"
}
```

## 6. 管理UI设计

### 6.1 页面结构
```
/                    # 首页 - 系统概览
/tasks              # 任务管理页面
/upload             # 文件上传页面
/tasks/{id}         # 任务详情页面
/settings           # 系统设置页面
/swagger-ui         # API文档页面
```

### 6.2 UI功能特性
- 响应式设计，支持移动端
- 实时任务状态更新（WebSocket）
- 拖拽文件上传
- 批量任务操作
- 任务历史记录
- 错误日志查看
- 系统监控仪表板

## 7. 非功能需求

### 7.1 性能需求
- 支持并发任务处理（可配置线程池）
- 大文件上传支持（最大100MB）
- 任务队列管理，避免系统过载
- 内存使用监控和限制

### 7.2 安全需求
- 文件类型验证和安全扫描
- 路径遍历攻击防护
- 文件大小限制
- 请求频率限制（Rate Limiting）
- CSRF保护

### 7.3 可靠性需求
- 任务失败重试机制
- 系统异常恢复
- 数据持久化
- 健康检查端点
- 优雅关闭处理

### 7.4 监控和日志
- 任务执行指标收集
- 系统性能监控
- 详细的操作日志
- 错误追踪和报警

## 8. 实施计划

### 8.1 开发阶段划分
**阶段1: 基础Web框架集成 (2-3天)**
- 添加Spring Boot Web相关依赖
- 修改ServerCommand实现Web服务启动
- 配置Web相关的application.yaml
- 创建基础的Controller和Service结构
- 集成SpringDoc OpenAPI

**阶段2: 任务管理系统 (3-4天)**
- 设计和实现数据模型
- 创建任务管理服务
- 实现异步任务处理
- 添加任务状态跟踪
- 实现文件存储服务

**阶段3: REST API开发 (2-3天)**
- 实现任务管理API
- 实现文件上传/下载API
- 添加参数验证和异常处理
- 实现分页和过滤功能
- API测试和文档完善

**阶段4: WebSocket实时通信 (1-2天)**
- 配置WebSocket
- 实现任务状态实时推送
- 客户端WebSocket集成

**阶段5: 管理UI开发 (3-4天)**
- 创建Thymeleaf模板
- 实现任务管理界面
- 文件上传界面
- 实时状态更新
- 响应式设计优化

**阶段6: 测试和优化 (2-3天)**
- 单元测试和集成测试
- 性能测试和优化
- 安全测试
- 文档完善

### 8.2 总体时间估算
- **开发时间**: 13-19天
- **测试时间**: 3-5天
- **总计**: 16-24天

## 9. 风险评估和缓解策略

### 9.1 技术风险
- **风险**: Spring Boot版本兼容性问题
- **缓解**: 使用稳定版本，充分测试

- **风险**: 大文件处理内存溢出
- **缓解**: 流式处理，内存监控

### 9.2 进度风险
- **风险**: UI开发复杂度超预期
- **缓解**: 使用成熟的UI框架，简化设计

- **风险**: WebSocket集成困难
- **缓解**: 先实现基础功能，WebSocket作为增强功能

## 10. 验收标准

### 10.1 功能验收
- [ ] `-server`参数正常启动Web服务
- [ ] 所有REST API端点正常工作
- [ ] 任务状态正确跟踪和更新
- [ ] 文件上传下载功能完整
- [ ] UI界面友好易用
- [ ] WebSocket实时更新正常
- [ ] Swagger文档完整准确

### 10.2 性能验收
- [ ] 支持至少10个并发任务
- [ ] 单个任务处理时间不超过原CLI模式的120%
- [ ] 系统内存使用合理
- [ ] 响应时间满足要求（API < 2s，页面 < 3s）

### 10.3 安全验收
- [ ] 文件类型验证有效
- [ ] 路径遍历攻击防护到位
- [ ] 文件大小限制正常
- [ ] 无明显安全漏洞

## 11. 后续扩展规划

### 11.1 短期扩展
- 支持批量文件上传
- 任务优先级管理
- 用户认证和权限管理
- 任务调度功能

### 11.2 长期扩展
- 分布式任务处理
- 云存储集成
- 微服务架构改造
- 容器化部署

## 12. 详细任务清单

### 12.1 阶段1: 基础Web框架集成

#### 任务1.1: 更新项目依赖
- [ ] 在pom.xml中添加spring-boot-starter-web依赖
- [ ] 添加spring-boot-starter-data-jpa依赖
- [ ] 添加h2database依赖
- [ ] 添加springdoc-openapi-starter-webmvc-ui依赖
- [ ] 添加spring-boot-starter-websocket依赖
- [ ] 添加spring-boot-starter-thymeleaf依赖
- [ ] 添加spring-boot-starter-validation依赖

#### 任务1.2: 修改ServerCommand
- [ ] 实现Web服务启动逻辑
- [ ] 添加端口配置选项
- [ ] 集成Spring Boot Web容器
- [ ] 添加优雅关闭处理

#### 任务1.3: 配置文件更新
- [ ] 创建application-server.yaml配置文件
- [ ] 配置Web服务端口和路径
- [ ] 配置数据库连接
- [ ] 配置文件上传限制
- [ ] 配置WebSocket端点

#### 任务1.4: 基础架构搭建
- [ ] 创建web包结构
- [ ] 创建WebConfig配置类
- [ ] 创建全局异常处理器
- [ ] 创建CORS配置
- [ ] 创建安全配置

#### 任务1.5: SpringDoc集成
- [ ] 配置OpenAPI文档
- [ ] 添加API信息和描述
- [ ] 配置Swagger UI路径
- [ ] 添加API分组

### 12.2 阶段2: 任务管理系统

#### 任务2.1: 数据模型设计
- [ ] 创建ConversionTask实体类
- [ ] 创建TaskStatus枚举
- [ ] 创建ConversionTaskRepository接口
- [ ] 配置JPA实体映射
- [ ] 创建数据库初始化脚本

#### 任务2.2: 核心服务实现
- [ ] 实现ConversionTaskService
- [ ] 实现FileStorageService
- [ ] 实现TaskProgressService
- [ ] 添加任务状态变更事件
- [ ] 实现任务清理定时器

#### 任务2.3: 异步处理框架
- [ ] 配置异步执行器
- [ ] 实现AsyncConversionService
- [ ] 添加任务进度跟踪
- [ ] 实现任务取消机制
- [ ] 添加错误处理和重试

#### 任务2.4: 文件管理系统
- [ ] 实现文件上传处理
- [ ] 实现临时文件管理
- [ ] 添加文件安全检查
- [ ] 实现文件下载服务
- [ ] 添加文件清理机制

### 12.3 阶段3: REST API开发

#### 任务3.1: 任务管理API
- [ ] 实现POST /api/v1/tasks接口
- [ ] 实现GET /api/v1/tasks接口（分页查询）
- [ ] 实现GET /api/v1/tasks/{taskId}接口
- [ ] 实现DELETE /api/v1/tasks/{taskId}接口
- [ ] 实现GET /api/v1/tasks/{taskId}/progress接口

#### 任务3.2: 文件管理API
- [ ] 实现POST /api/v1/files/upload接口
- [ ] 实现GET /api/v1/files/{fileId}/download接口
- [ ] 实现GET /api/v1/tasks/{taskId}/download接口
- [ ] 添加文件类型验证
- [ ] 添加文件大小限制

#### 任务3.3: 系统管理API
- [ ] 实现GET /api/v1/system/status接口
- [ ] 实现GET /api/v1/system/plugins接口
- [ ] 实现GET /api/v1/system/formats接口
- [ ] 添加系统健康检查
- [ ] 添加性能指标收集

#### 任务3.4: API增强功能
- [ ] 添加请求参数验证
- [ ] 实现统一异常处理
- [ ] 添加API版本控制
- [ ] 实现分页和排序
- [ ] 添加API限流

### 12.4 阶段4: WebSocket实时通信

#### 任务4.1: WebSocket配置
- [ ] 配置WebSocket端点
- [ ] 实现WebSocket处理器
- [ ] 添加连接管理
- [ ] 配置消息格式

#### 任务4.2: 实时通知服务
- [ ] 实现TaskNotificationService
- [ ] 集成任务状态变更事件
- [ ] 实现消息广播
- [ ] 添加连接状态管理

#### 任务4.3: 客户端集成
- [ ] 创建WebSocket客户端代码
- [ ] 实现自动重连机制
- [ ] 添加消息处理逻辑
- [ ] 实现状态同步

### 12.5 阶段5: 管理UI开发

#### 任务5.1: 基础模板
- [ ] 创建Thymeleaf基础模板
- [ ] 集成Bootstrap 5
- [ ] 创建导航菜单
- [ ] 实现响应式布局

#### 任务5.2: 任务管理界面
- [ ] 创建任务列表页面
- [ ] 实现任务详情页面
- [ ] 添加任务操作按钮
- [ ] 实现实时状态更新

#### 任务5.3: 文件上传界面
- [ ] 创建文件上传页面
- [ ] 实现拖拽上传功能
- [ ] 添加上传进度显示
- [ ] 实现批量上传

#### 任务5.4: 系统管理界面
- [ ] 创建系统状态页面
- [ ] 实现插件管理界面
- [ ] 添加配置管理页面
- [ ] 创建监控仪表板

#### 任务5.5: 用户体验优化
- [ ] 添加加载动画
- [ ] 实现错误提示
- [ ] 优化移动端体验
- [ ] 添加快捷键支持

### 12.6 阶段6: 测试和优化

#### 任务6.1: 单元测试
- [ ] 编写Service层单元测试
- [ ] 编写Controller层单元测试
- [ ] 编写Repository层测试
- [ ] 添加Mock测试

#### 任务6.2: 集成测试
- [ ] 编写API集成测试
- [ ] 编写WebSocket测试
- [ ] 编写文件上传下载测试
- [ ] 添加端到端测试

#### 任务6.3: 性能测试
- [ ] 进行并发任务测试
- [ ] 测试大文件处理
- [ ] 进行内存使用测试
- [ ] 优化性能瓶颈

#### 任务6.4: 安全测试
- [ ] 进行文件上传安全测试
- [ ] 测试路径遍历防护
- [ ] 进行CSRF测试
- [ ] 检查XSS防护

## 13. 配置文件模板

### 13.1 application-server.yaml
```yaml
server:
  port: 8080
  servlet:
    context-path: /doc-converter
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

spring:
  profiles:
    active: server
  datasource:
    url: jdbc:h2:mem:docconverter
    driver-class-name: org.h2.Driver
    username: sa
    password:
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
  h2:
    console:
      enabled: true
      path: /h2-console

doc-converter:
  web:
    upload-dir: temp/uploads
    result-dir: temp/results
    max-concurrent-tasks: 10
    task-timeout: 300
    cleanup-interval: 3600
```

### 13.2 依赖配置更新
```xml
<!-- Web相关依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
<dependency>
    <groupId>com.h2database</groupId>
    <artifactId>h2</artifactId>
    <scope>runtime</scope>
</dependency>
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.2.0</version>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-websocket</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-thymeleaf</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-validation</artifactId>
</dependency>
```

---

**文档版本**: v1.0
**创建日期**: 2025-06-23
**最后更新**: 2025-06-23
**负责人**: AI Assistant
